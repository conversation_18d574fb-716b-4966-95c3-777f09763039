// @version=5
indicator("alpha Multi-Timeframe with Trend Detection", overlay=true)

// Define colors for each timeframe
color_15min = color.new(#0022FC, 0)    // Blue for 15min
color_60min = color.new(#00B300, 0)    // Green for 1h
color_240min = color.new(#FF0000, 0)   // Red for 4h
color_daily = color.new(#000000, 0)    // Black for Daily
color_weekly = color.new(#FFA500, 0)   // Orange for Weekly

// Function to calculate alpha for a specific timeframe
calcAlpha(tf) =>
    // Get data from the specified timeframe
    factor_alpha = tf == "D" ? 3.1 : 
                     tf == "W" ? 3.1 : 
                     tf == "240" ? 3.369 : 
                     tf == "60" ? 2.8 : 
                     tf == "15" ? 4.36 : 3
    
    // Request security data for the specified timeframe
    [tfHigh, tfLow, tfClose] = request.security(syminfo.tickerid, tf, [high, low, close], lookahead=barmerge.lookahead_off)
    tfHLC3 = (tfHigh + tfLow + tfClose) / 3
    
    // Calculate True Range for the timeframe
    tfTR = request.security(syminfo.tickerid, tf, ta.tr, lookahead=barmerge.lookahead_off)
    tfTR_SMA = request.security(syminfo.tickerid, tf, ta.sma(ta.tr, 69), lookahead=barmerge.lookahead_off)
    
    // Calculate MFI for the timeframe
    tfMFI = request.security(syminfo.tickerid, tf, ta.mfi(hlc3, 69), lookahead=barmerge.lookahead_off)
    
    // Calculate upT_S and downT_S
    upT_S = tfLow - tfTR_SMA * factor_alpha
    downT_S = tfHigh + tfTR_SMA * factor_alpha
    
    // Initialize alpha
    var float alpha = 0.0
    
    // Update alpha based on conditions
    alpha := tfMFI >= 50 ? 
                 upT_S < nz(alpha[1]) ? nz(alpha[1]) : upT_S : 
                 downT_S > nz(alpha[1]) ? nz(alpha[1]) : downT_S
    
    alpha

// Calculate alpha for each timeframe
alpha15 = calcAlpha("15")
alpha60 = calcAlpha("60")
alpha240 = calcAlpha("240")
alphaDaily = calcAlpha("D")
alphaWeekly = calcAlpha("W")

// Get 5-minute candle data
[high_5m, low_5m, close_5m] = request.security(syminfo.tickerid, "5", [high[1], low[1], close[1]], lookahead=barmerge.lookahead_off)

// Get the last 100 5-minute candles' close prices to check against alpha60
// We'll use arrays to store the close prices and check them outside of loops
var hasCloseBelow60 = false
var closeBelow60Count = 0
var hasCloseUpper60 = false
var closeUpper60Count = 0

// Get a single close price from 5-minute timeframe
close_5m_check = request.security(syminfo.tickerid, "5", close, lookahead=barmerge.lookahead_off)

// Check if current 5-minute close is below alpha60
if close_5m_check < alpha60
    closeBelow60Count := 50  // Set to max to indicate we found a close below alpha60
else
    // Decrease the counter but don't let it go below 0
    closeBelow60Count := math.max(0, closeBelow60Count - 1)

// Check if current 5-minute close is above alpha60
if close_5m_check > alpha60
    closeUpper60Count := 50  // Set to max to indicate we found a close above alpha60
else
    // Decrease the counter but don't let it go below 0
    closeUpper60Count := math.max(0, closeUpper60Count - 1)

// If counter is above 0, it means we've seen a close below/above alpha60 in the last 100 bars
hasCloseBelow60 := closeBelow60Count > 0
hasCloseUpper60 := closeUpper60Count > 0

// Define trend states as constants
var int STRONG_BUY = 1
var int WEAK_SELL = 2
var int STRONG_SELL = 3
var int WEAK_BUY = 4

// Initialize trend state variable
var int trendState = na

// Calculate basic conditions for each state
bool cond_strongBuy = alpha15 > alpha60 and not hasCloseBelow60
bool cond_weakSell = alpha15 > alpha60 and hasCloseBelow60
bool cond_strongSell = alpha15 < alpha60 and not hasCloseUpper60
bool cond_weakBuy = alpha15 < alpha60 and hasCloseUpper60

// State transition logic
if (barstate.isfirst)
    // Set initial state based on conditions
    if (cond_strongBuy)
        trendState := STRONG_BUY
    else if (cond_strongSell)
        trendState := STRONG_SELL
    else if (cond_weakBuy)
        trendState := WEAK_BUY
    else if (cond_weakSell)
        trendState := WEAK_SELL
    else
        trendState := STRONG_BUY  // Default to STRONG_BUY if no condition is met
else
    // State transitions follow a specific cycle
    if (trendState == STRONG_BUY and cond_weakSell)
        trendState := WEAK_SELL
    else if (trendState == WEAK_SELL and cond_strongSell)
        trendState := STRONG_SELL
    else if (trendState == STRONG_SELL and cond_weakBuy)
        trendState := WEAK_BUY
    else if (trendState == WEAK_BUY and cond_strongBuy)
        trendState := STRONG_BUY

// Create boolean variables for each state for easier use in plotting and conditions
bool strongBuy = trendState == STRONG_BUY
bool weakSell = trendState == WEAK_SELL
bool strongSell = trendState == STRONG_SELL
bool weakBuy = trendState == WEAK_BUY

// For backward compatibility
buyTrend = strongBuy
sellTrend = strongSell
weekBuy = weakBuy
weekSell = weakSell

// Calculate buy and sell signals
buySignal = alpha15 > alpha60 and low_5m < alpha60 and close_5m > alpha60
sellSignal = alpha15 < alpha60 and high_5m > alpha60 and close_5m < alpha60

// Create variables to track when signals change for alerting
var bool prevBuySignal = false
var bool prevSellSignal = false
buySignalChanged = buySignal != prevBuySignal
sellSignalChanged = sellSignal != prevSellSignal
prevBuySignal := buySignal
prevSellSignal := sellSignal

// Variables for take profit and stop loss
var float takeProfit = 0.0
var float stopLoss = 0.0
var float entryPrice = 0.0

// Calculate take profit and stop loss for buy signal
if (buySignal)
    entryPrice := close
    takeProfit := close + 5 * (high_5m - low_5m)
    stopLoss := low_5m - 0.5 * (high_5m - low_5m)

// Calculate take profit and stop loss for sell signal
if (sellSignal)
    entryPrice := close
    takeProfit := close - 5 * (high_5m - low_5m)
    stopLoss := high_5m + 0.5 * (high_5m - low_5m)

// Plot all timeframes simultaneously
plot(alpha15, title="Alpha 15m", color=color_15min, linewidth=1)
plot(alpha60, title="Alpha 1h", color=color_60min, linewidth=2)
plot(alpha240, title="Alpha 4h", color=color_240min, linewidth=2)
plot(alphaDaily, title="Alpha D", color=color_daily, linewidth=2)
plot(alphaWeekly, title="Alpha W", color=color_weekly, linewidth=3)

// Plot buy and sell signals with larger, more visible shapes
plotshape(buySignal, title="Buy Signal", location=location.belowbar, color=color.green, style=shape.triangleup, size=size.small)
plotshape(sellSignal, title="Sell Signal", location=location.abovebar, color=color.red, style=shape.triangledown, size=size.small)

// Plot trend signals
plotshape(strongBuy, title="Strong Buy", location=location.bottom, color=color.new(color.green, 20), style=shape.square, size=size.normal)
plotshape(strongSell, title="Strong Sell", location=location.top, color=color.new(color.red, 20), style=shape.square, size=size.normal)

// Plot weak trend signals
plotshape(weakBuy, title="Weak Buy", location=location.bottom, color=color.new(color.lime, 40), style=shape.circle, size=size.small)
plotshape(weakSell, title="Weak Sell", location=location.top, color=color.new(color.orange, 40), style=shape.circle, size=size.small)

// Plot background color based on trend
bgcolor(strongBuy ? color.new(color.green, 80) : strongSell ? color.new(color.red, 80) : weakBuy ? color.new(color.lime, 90) : weakSell ? color.new(color.orange, 90) : na)

// Plot take profit and stop loss levels
plot(buySignal or sellSignal ? takeProfit : na, title="Take Profit", color=color.new(color.green, 0), style=plot.style_circles, linewidth=2)
plot(buySignal or sellSignal ? stopLoss : na, title="Stop Loss", color=color.new(color.red, 0), style=plot.style_circles, linewidth=2)

// Add alerts for signal changes
alertcondition(buySignalChanged and buySignal, "Buy Signal", "Buy signal triggered at {{close}}")
alertcondition(sellSignalChanged and sellSignal, "Sell Signal", "Sell signal triggered at {{close}}")

// Create variables to track when trend signals change for alerting
var bool prevBuyTrend = false
var bool prevSellTrend = false
buyTrendChanged = buyTrend != prevBuyTrend
sellTrendChanged = sellTrend != prevSellTrend
prevBuyTrend := buyTrend
prevSellTrend := sellTrend

// Add alerts for trend signal changes
alertcondition(buyTrendChanged and buyTrend, "Buy Trend", "Buy trend started at {{close}}")
alertcondition(sellTrendChanged and sellTrend, "Sell Trend", "Sell trend started at {{close}}")

// Add a table to show which color corresponds to which timeframe and display alpha values
if (barstate.islast)
    var table legendTable = table.new(position.top_right, 2, 9, bgcolor=color.new(color.black, 70), border_width=1)
    table.cell(legendTable, 0, 0, "alpha Timeframes", bgcolor=color.new(color.black, 70), text_color=color.white)
    table.cell(legendTable, 1, 0, "Value", bgcolor=color.new(color.black, 70), text_color=color.white)
    
    table.cell(legendTable, 0, 1, "15 min", bgcolor=color.new(color.black, 90), text_color=color_15min)
    table.cell(legendTable, 1, 1, str.tostring(alpha15, "#.##"), bgcolor=color.new(color.black, 90), text_color=color_15min)
    
    table.cell(legendTable, 0, 2, "1 hour", bgcolor=color.new(color.black, 50), text_color=color_60min)
    table.cell(legendTable, 1, 2, str.tostring(alpha60, "#.##"), bgcolor=color.new(color.black, 50), text_color=color_60min)
    
    table.cell(legendTable, 0, 3, "4 hour", bgcolor=color.new(color.black, 90), text_color=color_240min)
    table.cell(legendTable, 1, 3, str.tostring(alpha240, "#.##"), bgcolor=color.new(color.black, 90), text_color=color_240min)
    
    table.cell(legendTable, 0, 4, "Weekly", bgcolor=color.new(color.black, 50), text_color=color_weekly)
    table.cell(legendTable, 1, 4, str.tostring(alphaWeekly, "#.##"), bgcolor=color.new(color.black, 50), text_color=color_weekly)
    
    // Trend Signal
    trendText = trendState == STRONG_BUY ? "STRONG BUY" : trendState == WEAK_SELL ? "WEAK SELL" : trendState == STRONG_SELL ? "STRONG SELL" : trendState == WEAK_BUY ? "WEAK BUY" : "NEUTRAL"
    trendColor = trendState == STRONG_BUY ? color.green : 
                 trendState == STRONG_SELL ? color.red : 
                 trendState == WEAK_BUY ? color.lime : 
                 trendState == WEAK_SELL ? color.orange : color.white
    table.cell(legendTable, 0, 5, "Trend", bgcolor=color.new(color.black, 50), text_color=color.white)
    table.cell(legendTable, 1, 5, trendText, bgcolor=color.new(color.black, 50), text_color=trendColor)
    
    // Take Profit and Stop Loss
    if (buySignal or sellSignal)
        table.cell(legendTable, 0, 7, "Take Profit", bgcolor=color.new(color.black, 90), text_color=color.white)
        table.cell(legendTable, 1, 7, str.tostring(takeProfit, "#.##"), bgcolor=color.new(color.black, 90), text_color=color.green)
        
        table.cell(legendTable, 0, 8, "Stop Loss", bgcolor=color.new(color.black, 90), text_color=color.white)
        table.cell(legendTable, 1, 8, str.tostring(stopLoss, "#.##"), bgcolor=color.new(color.black, 90), text_color=color.red)