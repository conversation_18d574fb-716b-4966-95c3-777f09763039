//+------------------------------------------------------------------+
//|                                           Tom_Gaussian_Filter.mq5 |
//|                                                 TOMBOBAP 2024     |
//+------------------------------------------------------------------+
#property copyright "TOMBOBAP 2024"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   2
#property indicator_label1  "Tom Gaussian Filter"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrMagenta
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

#property indicator_label2  "EMA 32"
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrDodgerBlue
#property indicator_style2  STYLE_SOLID
#property indicator_width2  1
#property indicator_applied_price PRICE_TYPICAL
#property description       "Tom's Custom Gaussian Filter Implementation (filt_Gaus_1h)"

// Input parameters for Gaussian filter
input int GausPeriod = 186;        // Gaussian Filter Period (default for 1H)
input bool ReducedLagMode = false; // Enable Reduced Lag Mode
input bool FastResponseMode = false; // Enable Fast Response Mode
input int EmaPeriod = 32;          // EMA Period
input double ScaleFactor = 1.0;    // Scale factor for Gaussian Filter (adjust if values are too high/low)
input double OffsetValue = 0.0;  // Offset value to shift the Gaussian Filter (0 for no shift)

//--- Indicator buffer for plotting
double GaussianBuffer[];        // Buffer for Gaussian Filter
double EmaBuffer[];             // Buffer for EMA

// Arrays to store previous filter values (needed for recursive calculation)
double FilterValues1[];
double FilterValues2[];
double FilterValues3[];
double FilterValues4[];
double FilterValues5[];
double FilterValues6[];
double FilterValues7[];
double FilterValues8[];
double FilterValues9[];

//--- Function to calculate Gaussian filter with 9 poles (exactly like Pine script)
double Filt9xGaus(double alpha, double source, int pole, int bar)
{
   // Ensure valid parameters
   if(pole < 1 || pole > 9) pole = 4;
   
   // Weights for different poles (from Pine script)
   int m2 = 0, m3 = 0, m4 = 0, m5 = 0, m6 = 0, m7 = 0, m8 = 0, m9 = 0;
   double x = 1 - alpha;
   
   // Set weights based on pole number - exactly as in Pine script
   if(pole == 9) { m2 = 36; m3 = 84; m4 = 126; m5 = 126; m6 = 84; m7 = 36; m8 = 9; m9 = 1; }
   else if(pole == 8) { m2 = 28; m3 = 56; m4 = 70; m5 = 56; m6 = 28; m7 = 8; m8 = 1; }
   else if(pole == 7) { m2 = 21; m3 = 35; m4 = 35; m5 = 21; m6 = 7; m7 = 1; }
   else if(pole == 6) { m2 = 15; m3 = 20; m4 = 15; m5 = 6; m6 = 1; }
   else if(pole == 5) { m2 = 10; m3 = 10; m4 = 5; m5 = 1; }
   else if(pole == 4) { m2 = 6; m3 = 4; m4 = 1; }
   else if(pole == 3) { m2 = 3; m3 = 1; }
   else if(pole == 2) { m2 = 1; }
   
   // Get previous values with safety checks
   double prev1 = 0, prev2 = 0, prev3 = 0, prev4 = 0;
   double prev5 = 0, prev6 = 0, prev7 = 0, prev8 = 0, prev9 = 0;
   
   // Only try to access previous values if they exist
   // Use the appropriate array based on the pole number
   if(bar > 0) 
   {
      if(pole == 1) prev1 = FilterValues1[bar-1];
      else if(pole == 2) prev1 = FilterValues2[bar-1];
      else if(pole == 3) prev1 = FilterValues3[bar-1];
      else if(pole == 4) prev1 = FilterValues4[bar-1];
      else if(pole == 5) prev1 = FilterValues5[bar-1];
      else if(pole == 6) prev1 = FilterValues6[bar-1];
      else if(pole == 7) prev1 = FilterValues7[bar-1];
      else if(pole == 8) prev1 = FilterValues8[bar-1];
      else if(pole == 9) prev1 = FilterValues9[bar-1];
   }
   
   if(bar > 1)
   {
      if(pole == 1) prev2 = FilterValues1[bar-2];
      else if(pole == 2) prev2 = FilterValues2[bar-2];
      else if(pole == 3) prev2 = FilterValues3[bar-2];
      else if(pole == 4) prev2 = FilterValues4[bar-2];
      else if(pole == 5) prev2 = FilterValues5[bar-2];
      else if(pole == 6) prev2 = FilterValues6[bar-2];
      else if(pole == 7) prev2 = FilterValues7[bar-2];
      else if(pole == 8) prev2 = FilterValues8[bar-2];
      else if(pole == 9) prev2 = FilterValues9[bar-2];
   }
   
   if(bar > 2)
   {
      if(pole == 1) prev3 = FilterValues1[bar-3];
      else if(pole == 2) prev3 = FilterValues2[bar-3];
      else if(pole == 3) prev3 = FilterValues3[bar-3];
      else if(pole == 4) prev3 = FilterValues4[bar-3];
      else if(pole == 5) prev3 = FilterValues5[bar-3];
      else if(pole == 6) prev3 = FilterValues6[bar-3];
      else if(pole == 7) prev3 = FilterValues7[bar-3];
      else if(pole == 8) prev3 = FilterValues8[bar-3];
      else if(pole == 9) prev3 = FilterValues9[bar-3];
   }
   
   if(bar > 3)
   {
      if(pole == 1) prev4 = FilterValues1[bar-4];
      else if(pole == 2) prev4 = FilterValues2[bar-4];
      else if(pole == 3) prev4 = FilterValues3[bar-4];
      else if(pole == 4) prev4 = FilterValues4[bar-4];
      else if(pole == 5) prev4 = FilterValues5[bar-4];
      else if(pole == 6) prev4 = FilterValues6[bar-4];
      else if(pole == 7) prev4 = FilterValues7[bar-4];
      else if(pole == 8) prev4 = FilterValues8[bar-4];
      else if(pole == 9) prev4 = FilterValues9[bar-4];
   }
   
   if(bar > 4)
   {
      if(pole == 1) prev5 = FilterValues1[bar-5];
      else if(pole == 2) prev5 = FilterValues2[bar-5];
      else if(pole == 3) prev5 = FilterValues3[bar-5];
      else if(pole == 4) prev5 = FilterValues4[bar-5];
      else if(pole == 5) prev5 = FilterValues5[bar-5];
      else if(pole == 6) prev5 = FilterValues6[bar-5];
      else if(pole == 7) prev5 = FilterValues7[bar-5];
      else if(pole == 8) prev5 = FilterValues8[bar-5];
      else if(pole == 9) prev5 = FilterValues9[bar-5];
   }
   
   if(bar > 5)
   {
      if(pole == 1) prev6 = FilterValues1[bar-6];
      else if(pole == 2) prev6 = FilterValues2[bar-6];
      else if(pole == 3) prev6 = FilterValues3[bar-6];
      else if(pole == 4) prev6 = FilterValues4[bar-6];
      else if(pole == 5) prev6 = FilterValues5[bar-6];
      else if(pole == 6) prev6 = FilterValues6[bar-6];
      else if(pole == 7) prev6 = FilterValues7[bar-6];
      else if(pole == 8) prev6 = FilterValues8[bar-6];
      else if(pole == 9) prev6 = FilterValues9[bar-6];
   }
   
   if(bar > 6)
   {
      if(pole == 1) prev7 = FilterValues1[bar-7];
      else if(pole == 2) prev7 = FilterValues2[bar-7];
      else if(pole == 3) prev7 = FilterValues3[bar-7];
      else if(pole == 4) prev7 = FilterValues4[bar-7];
      else if(pole == 5) prev7 = FilterValues5[bar-7];
      else if(pole == 6) prev7 = FilterValues6[bar-7];
      else if(pole == 7) prev7 = FilterValues7[bar-7];
      else if(pole == 8) prev7 = FilterValues8[bar-7];
      else if(pole == 9) prev7 = FilterValues9[bar-7];
   }
   
   if(bar > 7)
   {
      if(pole == 1) prev8 = FilterValues1[bar-8];
      else if(pole == 2) prev8 = FilterValues2[bar-8];
      else if(pole == 3) prev8 = FilterValues3[bar-8];
      else if(pole == 4) prev8 = FilterValues4[bar-8];
      else if(pole == 5) prev8 = FilterValues5[bar-8];
      else if(pole == 6) prev8 = FilterValues6[bar-8];
      else if(pole == 7) prev8 = FilterValues7[bar-8];
      else if(pole == 8) prev8 = FilterValues8[bar-8];
      else if(pole == 9) prev8 = FilterValues9[bar-8];
   }
   
   if(bar > 8)
   {
      if(pole == 1) prev9 = FilterValues1[bar-9];
      else if(pole == 2) prev9 = FilterValues2[bar-9];
      else if(pole == 3) prev9 = FilterValues3[bar-9];
      else if(pole == 4) prev9 = FilterValues4[bar-9];
      else if(pole == 5) prev9 = FilterValues5[bar-9];
      else if(pole == 6) prev9 = FilterValues6[bar-9];
      else if(pole == 7) prev9 = FilterValues7[bar-9];
      else if(pole == 8) prev9 = FilterValues8[bar-9];
      else if(pole == 9) prev9 = FilterValues9[bar-9];
   }
   
   // Calculate filter using the exact formula from Pine script
   double result = MathPow(alpha, pole) * source;
   
   // Add terms based on pole number
   result += pole * x * prev1;
   
   if(pole >= 2) result -= m2 * MathPow(x, 2) * prev2;
   if(pole >= 3) result += m3 * MathPow(x, 3) * prev3;
   if(pole >= 4) result -= m4 * MathPow(x, 4) * prev4;
   if(pole >= 5) result += m5 * MathPow(x, 5) * prev5;
   if(pole >= 6) result -= m6 * MathPow(x, 6) * prev6;
   if(pole >= 7) result += m7 * MathPow(x, 7) * prev7;
   if(pole >= 8) result -= m8 * MathPow(x, 8) * prev8;
   if(pole == 9) result += m9 * MathPow(x, 9) * prev9;
   
   // Store the result for future calculations
   if(pole == 1) FilterValues1[bar] = result;
   else if(pole == 2) FilterValues2[bar] = result;
   else if(pole == 3) FilterValues3[bar] = result;
   else if(pole == 4) FilterValues4[bar] = result;
   else if(pole == 5) FilterValues5[bar] = result;
   else if(pole == 6) FilterValues6[bar] = result;
   else if(pole == 7) FilterValues7[bar] = result;
   else if(pole == 8) FilterValues8[bar] = result;
   else if(pole == 9) FilterValues9[bar] = result;
   
   // Kiểm tra giá trị hợp lệ
   if(!MathIsValidNumber(result)) {
      Print("Invalid Filt9xGaus result for pole=", pole, ", bar=", bar, ", alpha=", alpha, ", source=", source);
      return source; // Trả về giá trị nguồn nếu kết quả không hợp lệ
   }
   
   return result;
}

//--- Function to calculate pole-based Gaussian filter (exactly like Pine script)
void PoleGaus(double alpha, double source, int poles, int bar, double &result, double &result1)
{
   // Calculate the filter for the specified number of poles
   // This matches the Pine script implementation of f_pole_Gaus
   double f1 = Filt9xGaus(alpha, source, 1, bar);
   double f2 = poles >= 2 ? Filt9xGaus(alpha, source, 2, bar) : 0;
   double f3 = poles >= 3 ? Filt9xGaus(alpha, source, 3, bar) : 0;
   double f4 = poles >= 4 ? Filt9xGaus(alpha, source, 4, bar) : 0;
   double f5 = poles >= 5 ? Filt9xGaus(alpha, source, 5, bar) : 0;
   double f6 = poles >= 6 ? Filt9xGaus(alpha, source, 6, bar) : 0;
   double f7 = poles >= 7 ? Filt9xGaus(alpha, source, 7, bar) : 0;
   double f8 = poles >= 8 ? Filt9xGaus(alpha, source, 8, bar) : 0;
   double f9 = poles == 9 ? Filt9xGaus(alpha, source, 9, bar) : 0;
   
   // Select the appropriate filter based on poles
   if(poles == 1) result = f1;
   else if(poles == 2) result = f2;
   else if(poles == 3) result = f3;
   else if(poles == 4) result = f4;
   else if(poles == 5) result = f5;
   else if(poles == 6) result = f6;
   else if(poles == 7) result = f7;
   else if(poles == 8) result = f8;
   else if(poles == 9) result = f9;
   else result = f4; // Default to 4 poles
   
   // Return f1 as well (needed for fast response mode)
   result1 = f1;
}

//--- OnInit function to initialize the indicators
int OnInit()
{
   // Set up indicator buffers for plotting
   SetIndexBuffer(0, GaussianBuffer, INDICATOR_DATA);
   SetIndexBuffer(1, EmaBuffer, INDICATOR_DATA);
   
   // Set drawing properties for Gaussian Filter
   PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_LINE);
   PlotIndexSetInteger(0, PLOT_LINE_COLOR, clrMagenta);
   PlotIndexSetInteger(0, PLOT_LINE_STYLE, STYLE_SOLID);
   PlotIndexSetInteger(0, PLOT_LINE_WIDTH, 2);
   
   // Set drawing properties for EMA
   PlotIndexSetInteger(1, PLOT_DRAW_TYPE, DRAW_LINE);
   PlotIndexSetInteger(1, PLOT_LINE_COLOR, clrDodgerBlue);
   PlotIndexSetInteger(1, PLOT_LINE_STYLE, STYLE_SOLID);
   PlotIndexSetInteger(1, PLOT_LINE_WIDTH, 1);
   
   // Set indicator name
   string indicatorName = "Tom Gaussian Filter 1H (" + IntegerToString(GausPeriod) + ")";
   IndicatorSetString(INDICATOR_SHORTNAME, indicatorName);
   
   // Print initialization info
   Print("Tom Gaussian Filter initialized with period: ", GausPeriod, 
         ", Reduced Lag Mode: ", ReducedLagMode ? "Enabled" : "Disabled", 
         ", Fast Response Mode: ", FastResponseMode ? "Enabled" : "Disabled");
   
   return INIT_SUCCEEDED;
}

//--- OnCalculate function for indicator calculation
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   // Check for minimum bars
   if(rates_total < 10) return 0;
   
   // Calculate start position
   int start = prev_calculated > 0 ? prev_calculated - 1 : 0;
   
   // Print debug info on first calculation
   if(start == 0)
   {
      Print("Tom Gaussian Filter: Initial calculation with ", rates_total, " bars");
      
      // Initialize filter value arrays
      ArrayResize(FilterValues1, rates_total);
      ArrayResize(FilterValues2, rates_total);
      ArrayResize(FilterValues3, rates_total);
      ArrayResize(FilterValues4, rates_total);
      ArrayResize(FilterValues5, rates_total);
      ArrayResize(FilterValues6, rates_total);
      ArrayResize(FilterValues7, rates_total);
      ArrayResize(FilterValues8, rates_total);
      ArrayResize(FilterValues9, rates_total);
      
      ArrayInitialize(FilterValues1, 0);
      ArrayInitialize(FilterValues2, 0);
      ArrayInitialize(FilterValues3, 0);
      ArrayInitialize(FilterValues4, 0);
      ArrayInitialize(FilterValues5, 0);
      ArrayInitialize(FilterValues6, 0);
      ArrayInitialize(FilterValues7, 0);
      ArrayInitialize(FilterValues8, 0);
      ArrayInitialize(FilterValues9, 0);
   }
   else
   {
      // Resize arrays if needed
      if(ArraySize(FilterValues1) < rates_total)
      {
         int oldSize = ArraySize(FilterValues1);
         ArrayResize(FilterValues1, rates_total);
         ArrayResize(FilterValues2, rates_total);
         ArrayResize(FilterValues3, rates_total);
         ArrayResize(FilterValues4, rates_total);
         ArrayResize(FilterValues5, rates_total);
         ArrayResize(FilterValues6, rates_total);
         ArrayResize(FilterValues7, rates_total);
         ArrayResize(FilterValues8, rates_total);
         ArrayResize(FilterValues9, rates_total);
         
         // Initialize new elements
         for(int i = oldSize; i < rates_total; i++)
         {
            FilterValues1[i] = 0;
            FilterValues2[i] = 0;
            FilterValues3[i] = 0;
            FilterValues4[i] = 0;
            FilterValues5[i] = 0;
            FilterValues6[i] = 0;
            FilterValues7[i] = 0;
            FilterValues8[i] = 0;
            FilterValues9[i] = 0;
         }
      }
   }
   
   // Calculate beta and alpha for timeframe - exactly like Pine script
   // beta_Gaus = (1 - math.cos(4 * math.asin(1) / per_Gaus)) / (math.pow(1.414, 0.5) - 1)
   // alpha_Gaus = -beta_Gaus + math.sqrt(math.pow(beta_Gaus, 2) + 2 * beta_Gaus)
   double gaus_beta = (1 - MathCos(4 * M_PI / GausPeriod)) / (MathSqrt(1.414) - 1);
   double gaus_alpha = -gaus_beta + MathSqrt(MathPow(gaus_beta, 2) + 2 * gaus_beta);
   
   // Adjust alpha for desired curve smoothness
   gaus_alpha = gaus_alpha * 0.92; // Adjust alpha to get the right amount of curve
   
   // Calculate lag
   double lag = (GausPeriod - 1) / (2 * 4);
   
   // Calculate for each bar
   for(int i = start; i < rates_total; i++)
   {
      // Calculate typical price (OHLC4)
      double price = (open[i] + high[i] + low[i] + close[i]) / 4.0;
      
      // Calculate lag index
      int lagIndex = (int)lag;
      if(lagIndex >= i) lagIndex = i > 0 ? i - 1 : 0;
      
      // Prepare source data with lag reduction if enabled
      double srcdata = 0;
      if(ReducedLagMode && i >= lagIndex)
      {
         // Apply lag reduction formula
         srcdata = price + price - ((open[i-lagIndex] + high[i-lagIndex] + low[i-lagIndex] + close[i-lagIndex]) / 4.0);
      }
      else
      {
         srcdata = price;
      }
      
      // Calculate Gaussian Filter using the proper algorithm from Pine script
      double filtn_result = 0;
      double filt1_result = 0;
      
      // Calculate filter with 8 poles (reduced from 9 to allow more curve)
      PoleGaus(gaus_alpha, srcdata, 8, i, filtn_result, filt1_result);
      
      // Apply fast response mode if enabled
      double rawValue = 0;
      if(FastResponseMode)
      {
         rawValue = ((filtn_result + filt1_result) / 2);
      }
      else
      {
         rawValue = filtn_result;
      }
      
      // Apply additional smoothing if needed
      static double lastValue = 0;
      if(i > 0 && lastValue != 0)
      {
         // Apply additional smoothing (90% current value, 10% previous value)
         // Increased from 80/20 to 90/10 to allow more curve
         rawValue = 0.85 * rawValue + 0.15 * lastValue;
      }
      lastValue = rawValue;
      
      // Apply scale factor and offset
      GaussianBuffer[i] = rawValue * ScaleFactor + OffsetValue;
      
      // Calculate EMA
      if(i == 0)
         EmaBuffer[i] = close[i];
      else
         EmaBuffer[i] = EmaBuffer[i-1] + (2.0 / (EmaPeriod + 1)) * (close[i] - EmaBuffer[i-1]);
      
      // Debug output for first few bars
      if(i < 5 || i == rates_total-1) {
         Print("Bar ", i, ": Price=", price, ", srcdata=", srcdata, ", filtn=", filtn_result, ", GaussianBuffer=", GaussianBuffer[i]);
      }
   }
   
   // Debug output for the last calculated bar
   Print("Tom Gaussian Filter: Last value = ", GaussianBuffer[rates_total-1], 
         ", EMA(", EmaPeriod, ") = ", EmaBuffer[rates_total-1],
         " at bar ", rates_total-1);
   
   // Return the number of calculated bars
   return rates_total;
}

//--- OnDeinit function to clean up
void OnDeinit(const int reason)
{
   // No indicator handles to release anymore
   Print("Tom Gaussian Filter deinitialized");
}