# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# VS Code Extension build files
*.vsix
out/
dist/
build/

# TypeScript compiled files
*.js.map
*.d.ts

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# MetaTrader 5 compiled files
*.ex5
*.ex4

# MetaTrader 5 log files
*.tlog

# MetaTrader 5 backup files
*.bak

# Windows system files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# macOS system files
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.Spotlight-V100
.Trashes

# Linux system files
*~
.fuse_hidden*
.directory
.Trash-*

# IDE files
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json
.vscode/*.code-workspace
.idea/
*.swp
*.swo
*~

# Temporary files
*.tmp
*.temp
*.cache

# Archive files (unless specifically needed)
*.zip
*.rar
*.7z
*.tar
*.gz

# Test coverage
coverage/
.nyc_output/

# Misc
.sass-cache/
connect.lock
typings/
.angular/