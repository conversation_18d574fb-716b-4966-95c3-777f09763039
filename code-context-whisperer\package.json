{"name": "code-context-whisperer", "displayName": "Code Context Whisperer", "description": "<PERSON><PERSON>n thị ngữ cảnh mã nguồn khi hover chuột", "version": "0.0.1", "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "activationEvents": ["onLanguage:javascript", "onLanguage:typescript", "onLanguage:python", "onLanguage:java", "onLanguage:csharp"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "code-context-whisperer.showContext", "title": "Code Context Whisperer: <PERSON><PERSON><PERSON> thị ngữ cảnh"}, {"command": "code-context-whisperer.activatePremium", "title": "Code Context Whisperer: <PERSON><PERSON><PERSON> t<PERSON>h năng cao cấp"}], "configuration": {"title": "Code Context Whisperer", "properties": {"codeContextWhisperer.licenseKey": {"type": "string", "default": "", "description": "License key đ<PERSON> kích ho<PERSON>t t<PERSON>h năng cao cấp"}, "codeContextWhisperer.enableAIAnalysis": {"type": "boolean", "default": true, "description": "Bật/tắt phân tích AI (chỉ có sẵn cho người dùng cao cấp)"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package"}, "devDependencies": {"@types/vscode": "^1.60.0", "@types/glob": "^7.1.3", "@types/mocha": "^8.2.2", "@types/node": "14.x", "eslint": "^7.27.0", "@typescript-eslint/eslint-plugin": "^4.26.0", "@typescript-eslint/parser": "^4.26.0", "glob": "^7.1.7", "mocha": "^8.4.0", "typescript": "^4.3.2", "vscode-test": "^1.5.2"}}