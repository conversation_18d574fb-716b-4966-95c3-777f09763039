﻿//@version=5
indicator("PTT", overlay = true, max_labels_count = 500)

// Helper functions
round_(val) => val > .99 ? .999 : val < -.99 ? -.999 : val

normalize_1(value, minValue, maxValue) =>
    range_1 = maxValue - minValue == 0 ? 0.0001 : maxValue - minValue
    -1 + (value - minValue) / range_1 * 2

// ==================== PROGRESSIVE PENALTY/BONUS FUNCTIONS ====================
// Function to calculate progressive penalty based on FEX value above threshold
calc_progressive_penalty(fex_value, threshold_1st) =>
    if fex_value <= threshold_1st
        0.0  // No penalty if below threshold
    else
        excess = fex_value - threshold_1st
        if excess <= 3.0  // Points 94-96 (assuming threshold is 93)
            excess * -3.0  // -3 per point
        else  // Points 97+
            (3.0 * -3.0) + ((excess - 3.0) * -5.0)  // First 3 points at -3, rest at -5

// Function to calculate progressive bonus based on FEX value below threshold  
calc_progressive_bonus(fex_value, threshold_1st) =>
    if fex_value >= threshold_1st
        0.0  // No bonus if above threshold
    else
        excess = threshold_1st - fex_value  // How far below threshold
        if excess <= 3.0  // Points -94 to -96 (assuming threshold is -93)
            excess * 3.0  // +3 per point
        else  // Points -97 and below
            (3.0 * 3.0) + ((excess - 3.0) * 5.0)  // First 3 points at +3, rest at +5

// ==================== TIMEFRAME DETECTION ====================
is1min = timeframe.isintraday and timeframe.multiplier == 1
is3min = timeframe.isintraday and timeframe.multiplier == 3
is5min = timeframe.isintraday and timeframe.multiplier == 5
is15min = timeframe.isintraday and timeframe.multiplier == 15
is30min = timeframe.isintraday and timeframe.multiplier == 30
is60min = timeframe.isintraday and timeframe.multiplier == 60
is120min = timeframe.isintraday and timeframe.multiplier == 120
is180min = timeframe.isintraday and timeframe.multiplier == 180
is240min = timeframe.isintraday and timeframe.multiplier == 240
isday = timeframe.isdaily
isweek = timeframe.isweekly
ismonth = timeframe.ismonthly
isGold = syminfo.ticker == "XAUUSD"
isBitcoin = syminfo.ticker == "BTCUSD"

// ==================== FOREX DETECTION ====================
// Major Forex pairs detection
isEURUSD = syminfo.ticker == "EURUSD"
isGBPUSD = syminfo.ticker == "GBPUSD"
isUSDJPY = syminfo.ticker == "USDJPY"
isUSDCHF = syminfo.ticker == "USDCHF"
isAUDUSD = syminfo.ticker == "AUDUSD"
isUSDCAD = syminfo.ticker == "USDCAD"
isNZDUSD = syminfo.ticker == "NZDUSD"

// General Forex detection (covers all major pairs)
isForex = isEURUSD or isGBPUSD or isUSDJPY or isUSDCHF or isAUDUSD or isUSDCAD or isNZDUSD

// ==================== VIETNAMESE STOCK DETECTION ====================
// Vietnamese stock exchanges detection
isHOSE = str.contains(syminfo.tickerid, "HOSE")
isHNX = str.contains(syminfo.tickerid, "HNX")
isUPCOM = str.contains(syminfo.tickerid, "UPCOM")

// General Vietnamese stock detection (covers all Vietnamese exchanges)
isVN = isHOSE or isHNX or isUPCOM

// ==================== CONSOLIDATED DISTANCE CALCULATIONS ====================
// Base volatility calculations for different timeframes
volatility_current = ta.atr(30)  // Current timeframe volatility
atr_value = volatility_current

volatility_1h = request.security(syminfo.tickerid, "60", volatility_current, lookahead=barmerge.lookahead_off)
volatility_4h = request.security(syminfo.tickerid, "240", volatility_current, lookahead=barmerge.lookahead_off)
volatility_d = request.security(syminfo.tickerid, "D", volatility_current, lookahead=barmerge.lookahead_off)
volatility_w = request.security(syminfo.tickerid, "W", volatility_current, lookahead=barmerge.lookahead_off)
volatility_m = request.security(syminfo.tickerid, "M", volatility_current, lookahead=barmerge.lookahead_off)


// ==================== CENTRALIZED DISTANCE MULTIPLIER SYSTEM ====================
// Define which timeframes use 1H filter (needed for medium_distance calculations)
use_1h_filter = is1min or is3min or is5min or is15min  // Small timeframes use shortTF1H (1H data)

// CENTRALIZED MULTIPLIER FUNCTION - Single source of truth for all distance calculations
get_multiplier_for_timeframe(tf_string) =>
    switch tf_string
        "60" => 1.5  // 1H multiplier
        "240" => isGold ? 1.2 : 1.2  // 4H: Gold gets higher multiplier
        "D" => isGold or isBitcoin ? 1.2 : 1.7   // Daily: Special assets get lower multiplier
        "W" => isGold ? 1.7 : 1.2   // Weekly multiplier
        "M" => 1.2   // Monthly multiplier
        => 1.0  // Default fallback

// Current timeframe multiplier using the centralized function
timeframe_multiplier = switch
    is60min => get_multiplier_for_timeframe("60")
    is120min => get_multiplier_for_timeframe("120")
    is180min => get_multiplier_for_timeframe("180")
    is240min => get_multiplier_for_timeframe("240")
    isday => get_multiplier_for_timeframe("D")
    isweek => get_multiplier_for_timeframe("W")
    ismonth => get_multiplier_for_timeframe("M")
    => get_multiplier_for_timeframe("default")

// Specific multiplier constants for cross-timeframe usage
multiplier_1h = get_multiplier_for_timeframe("60")
multiplier_4h = get_multiplier_for_timeframe("240")
multiplier_monthly = get_multiplier_for_timeframe("M")

// ==================== UNIFIED DISTANCE FUNCTIONS ====================
// All distances now use the centralized multiplier system

// 1. Distance for current timeframe operations (signals, shortTerm cloud)
distance_current = volatility_current * timeframe_multiplier

// 2. Distance for shortTerm filter cloud (uses appropriate volatility + centralized multiplier)
short_distance = switch
    is60min => volatility_1h * get_multiplier_for_timeframe("60")   // 1H uses 1H volatility with 1H multiplier
    is240min => volatility_4h * get_multiplier_for_timeframe("240") // 4H uses 4H volatility with 4H multiplier
    isday => volatility_d * get_multiplier_for_timeframe("D")       // Daily uses Daily volatility with Daily multiplier
    isweek => volatility_w * get_multiplier_for_timeframe("W")      // Weekly uses Weekly volatility with Weekly multiplier
    ismonth => volatility_m * get_multiplier_for_timeframe("M")     // Monthly uses Monthly volatility with Monthly multiplier
    => volatility_current * get_multiplier_for_timeframe("default")           // Default: use 1H volatility with 1H multiplier

// 3. Distance for mediumTermFilter cloud (based on filter source timeframe)
// IMPORTANT: For consistency, mediumTermFilter cloud should match shortTermFilter cloud from source timeframe
medium_distance = switch
    is60min => volatility_4h * get_multiplier_for_timeframe("240")  // 1H uses 4H distance with 4H multiplier
    is240min => volatility_d * get_multiplier_for_timeframe("D")    // 4H uses D distance with D multiplier
    isday => volatility_w * get_multiplier_for_timeframe("W")       // D uses W distance with W multiplier (matches Weekly shortTermFilter)
    isweek => volatility_m * get_multiplier_for_timeframe("M")      // W uses M distance with M multiplier (matches Monthly shortTermFilter)
    ismonth => volatility_m * get_multiplier_for_timeframe("M")     // M uses M distance with M multiplier
    => volatility_current * get_multiplier_for_timeframe("default")                         // Default fallback for small timeframes

// Note: distance_to_use has been consolidated with medium_distance above for consistency

// Function to calculate FEX_ALL and OutBound for any timeframe
calc_fex_all_outbound() =>
    //1-Indicator EX
    Factor_plus = syminfo.ticker == "BTCUSD" ? 5 : 0
    Factor_plus_rsi = syminfo.ticker == "BTCUSD" ? 3 : 0

    // Get timeframe parameters using switch (inline)
    [Factor_FEX, Delta_tom, RSI_Period, Period] = switch
        is1min or is3min or is5min or is15min or is30min => [3.0, 93.9, 26, 269]
        is60min => [2.7, 83.9 + Factor_plus, 24 + Factor_plus_rsi, 179]
        is240min => [2.7, 83.9 + Factor_plus, 24 + Factor_plus_rsi, 179]
        isday => [2.0, 80.0 + Factor_plus, 23 + Factor_plus_rsi, 100]
        isweek => [2.0, 78.0 + Factor_plus, 22 + Factor_plus_rsi, 100]
        ismonth => [2.0, 76.0 + Factor_plus, 21 + Factor_plus_rsi, 100]
        => [2.5, 90.0, 24, 100]  // Default values
     
    Closema = ta.sma(close, Period)
    Openma = ta.sma(open, Period)
    Highma = ta.sma(high, Period)
    Lowma = ta.sma(low, Period)
    CloseSpread = close - Closema
    OpenSpread = open - Openma
    HighSpread = high - Highma
    LowSpread = low - Lowma

    ZscoreClose = CloseSpread / ta.stdev(CloseSpread, Period)
    ZscoreOpen = OpenSpread / ta.stdev(OpenSpread, Period)
    ZscoreHigh = HighSpread / ta.stdev(HighSpread, Period)
    ZscoreLow = LowSpread / ta.stdev(LowSpread, Period)

    //2-Indicator Fisher Transform
    high_ = ta.highest(hl2, 139)
    low_ = ta.lowest(hl2, 139)
    value = 0.0
    value := round_(.66 * ((hl2 - low_) / (high_ - low_) - .5) + .67 * nz(value[1]))
    fish1 = 0.0
    fish1 := .5 * math.log((1 + value) / (1 - value)) + .5 * nz(fish1[1])

    //3-Indicator Buy Low Sell High Composite-'BLSH'
    atrValue = ta.atr(12)
    priceRange = Factor_FEX * atrValue

    //3.2 RSI
    rsiValue = ta.rsi(close, 22)
    rsiValueNormalized = normalize_1(rsiValue, 27, 77)

    //3.3 Elliot Wave
    emaDiff = ta.ema(close, 17) - ta.ema(close, 66)
    emaDiffNormalized = normalize_1(emaDiff, -priceRange, priceRange)

    //3.4 MACD
    fastMovingAverage = ta.ema(close, 16)
    slowMovingAverage = ta.ema(close, 100)
    macd = fastMovingAverage - slowMovingAverage
    macdSignal = ta.sma(macd, 12)
    macdHistogram = macd - macdSignal
    macdNormalized = normalize_1(macd, -priceRange, priceRange)
    macdSignalNormalized = normalize_1(macdSignal, -priceRange, priceRange)
    macdHistogramNormalized = normalize_1(macdHistogram, -priceRange, priceRange)

    //3.5 MFI
    positiveFlow = math.sum(volume * (ta.change(hlc3) <= 0 ? 0 : hlc3), 14)
    negativeFlow = math.sum(volume * (ta.change(hlc3) >= 0 ? 0 : hlc3), 14)
    mfiValue = 100.0 - 100.0 / (1.0 + positiveFlow / negativeFlow)
    mfiValueNormalized = normalize_1(mfiValue, 25, 75)

    //3.6 Composite
    compositeValue = emaDiffNormalized + rsiValueNormalized + macdHistogramNormalized + mfiValueNormalized
    compositeNormalized = normalize_1(compositeValue, -4, 4)

    //4-Indicator "QQE MOD ALL" => Quantity
    RSI_ALL = ta.rsi(close, RSI_Period)
    RsiMa2 = ta.ema(RSI_ALL, 6) // trung bình SF2=6  của RSI là 6 kỳ

    //6- Indicator Stochastic Tom
    ll = ta.lowest(27)
    hh = ta.highest(27)
    diff = hh - ll
    rdiff = close - (hh+ll)/2
    avgrel = ta.ema(ta.ema(rdiff,9),9)
    avgdiff = ta.ema(ta.ema(diff,9),9)
    // SMI calculations
    SMI = avgdiff != 0 ? (avgrel/(avgdiff/2)*100) : 0
    SMIsignal = ta.ema(SMI,9)
    emasignal =ta.ema(SMI,10)
    mfi_cal = ta.mfi(hlc3, 42) - 50

    FEX_E = ZscoreClose > 5.3 ? 5.3 : ZscoreClose < -5.3 ? -5.3 : ZscoreClose*0.85 //Indicator 1 FEX_E
    FEX_F = fish1*6/7 > 6 ? 6 : fish1*6/7 //Indicator 2 FEX_F
    FEX_BLSH = 5.3*compositeNormalized > 5.3 ? 5.3 : 5.3*compositeNormalized //Indicator 3 FEX_BLSH
    FEX_QQE = (RsiMa2 - 50)*0.3 > 5.5 ? 5.5 : (RsiMa2 - 50)*0.3 < -5.5 ? -5.5 : (RsiMa2 - 50)*0.3 //Indicator 4 FEX_QQE
    FEX_RSI = (((RSI_ALL - 50)/5.5) > 5.5) ? 5.5 : ((RSI_ALL - 50)/5.5) < -5.5 ? -5.5 : ((RSI_ALL - 50)/5.5) //Indicator 5 FEX_RSI
    FEX_STOC = emasignal > 77 ? 5.3 : emasignal < -77 ? -5.3 : emasignal*5.3/77 //Indicator 6 FEX_STOC

    //FEX_MFI = mfi_cal*5.5/30 > 5.5 ? 5.5 : mfi_cal*5.5/30 < -5.5 ? -5.5 : mfi_cal*5.5/30 //Indicator tested => not yet find the suitable value for it.
    //Calculating 6 Indicators before combining with the 7th Indicator.
    FEX = math.avg(FEX_E,FEX_F,FEX_BLSH,FEX_QQE,FEX_RSI,FEX_STOC)
    FEX_EMA = ta.ema(FEX,24)
    FEX_TOM = math.round(math.avg(FEX,FEX_EMA)*18.1,3)

    //7-Indicator Tom_wave trend
    esa = ta.ema(hlc3, 32)//ema of hlc3 with multiple n1
    d = ta.ema(math.abs(hlc3 - esa), 32)//ema of dev between ap vs esa
    ci = (hlc3 - esa) / (0.015 * d)//dev divide ema of dev
    ema_cn = 100*ta.ema(ci, 222)/57
    // Optimized tom calculation using switch
    tom = switch
        ema_cn > 93 => 93 + (ema_cn - 93)/3
        ema_cn < -93 => -93 + (93 + ema_cn)/3
        => ema_cn

    BTC_factor = syminfo.ticker == "BTCUSD" ? 0.9 : 1
    FEX_ALL = (100*math.avg(FEX_TOM,0.92*tom)/87 > 0 ? 100*math.avg(FEX_TOM,0.92*tom)/87 : 100*math.avg(FEX_TOM,0.92*tom)/87)*BTC_factor
    FEX_ALL

// ==================== INPUTS ====================

show_xy_info = input.bool(false, "Show X-Y Info", tooltip="Display X, Y, and XY correlation information for timeframes >= 1H")
use_dynamic_weighting = input.bool(true, "Use Dynamic Weighting", tooltip="Enable dynamic weight adjustment based on trend positions and FEX levels. When disabled, uses static weights (Short: 40%, Medium: 35%, FEX: 25%)")

// ==================== FEX THRESHOLD VARIABLES ====================
// FEX penalty thresholds - adjustable by symbol
FEX_Penalty_Threshold_1st = switch
    isGold => 93.0
    isBitcoin => 95.0
    => 95.0  // Default value

FEX_Penalty_Threshold_2nd = switch
    isGold => 95.0
    isBitcoin => 98.0
    => 95.0  // Default value

FEX_Penalty_Threshold_3rd = switch
    isGold => 95.0
    isBitcoin => 98.0
    => 95.0  // Default value

FEX_Penalty_Reset = switch
    isGold => 90.0
    isBitcoin => 93.0
    => 90.0  // Default value

FEX_Penalty_Complete_Reset = switch
    isGold => 80.0
    isBitcoin => 85.0
    => 80.0  // Default value

// FEX penalty amounts - adjustable by symbol
FEX_Penalty_1st = switch
    isGold => -30.0
    isBitcoin => -30.0
    => -30.0  // Default value

FEX_Penalty_2nd = switch
    isGold => -60.0
    isBitcoin => -60.0
    => -60.0  // Default value

FEX_Penalty_3rd = switch
    isGold => -90.0
    isBitcoin => -90.0
    => -90.0  // Default value

// ==================== FEX BONUS SYSTEM ====================
// FEX bonus thresholds - perfectly opposite of penalty thresholds
FEX_Bonus_Threshold_1st = switch
    isGold => -93.0
    isBitcoin => -95.0
    isVN => -90.0
    => -90.0  // Default value

FEX_Bonus_Threshold_2nd = switch
    isGold => -95.0
    isBitcoin => -98.0
    isVN => -93.0
    => -92.0  // Default value

FEX_Bonus_Threshold_3rd = switch
    isGold => -95.0
    isBitcoin => -98.0
    isVN => -93.0
    => -93.0  // Default value

FEX_Bonus_Reset = switch
    isGold => -90.0
    isBitcoin => -93.0
    isVN => -85
    => -85.0  // Default value

FEX_Bonus_Complete_Reset = switch
    isGold => -80.0
    isBitcoin => -85.0
    isVN => -80.0
    => -80.0  // Default value

// FEX bonus amounts - positive values
FEX_Bonus_1st = switch
    isGold => 30.0
    isBitcoin => 30.0
    => 30.0  // Default value

FEX_Bonus_2nd = switch
    isGold => 60.0
    isBitcoin => 60.0
    => 60.0  // Default value

FEX_Bonus_3rd = switch
    isGold => 90.0
    isBitcoin => 90.0
    => 90.0  // Default value

// ==================== X-Y CORRELATION PENALTY SYSTEM ====================
// X-Y threshold values for penalty trigger (timeframe >= 1H only)
X_Threshold = switch
    is60min => isGold ? 8 : isBitcoin ? 13.0 : 8  // 1H threshold
    is240min => isGold ? 9 : isBitcoin ? 12 : 9  // 4H threshold
    isday => isGold ? 6.5 : isBitcoin ? 7 : 7     // Daily threshold
    isweek => isGold ? 9 : isBitcoin ? 9 : 10.5     // Weekly threshold
    ismonth => isGold ? 8.5 : isBitcoin ? 8.8 : 8.8     // Monthly threshold
    => 0.0  // No penalty for timeframes < 1H

Y_Threshold = switch
    is60min => isGold ? 18 : isBitcoin ? 23 : 18.5  // 1H threshold
    is240min => isGold ? 15 : isBitcoin ? 18 : 15  // 4H threshold
    isday => isGold ? 15 : isBitcoin ? 17 : 17  // Daily threshold
    isweek => isGold ? 13 : isBitcoin ? 13 : 14    // Weekly threshold
    ismonth => isGold ? 11 : isBitcoin ? 11 : 11     // Monthly threshold
    => 0.0  // No penalty for timeframes < 1H

XY_Sum_Threshold = X_Threshold + Y_Threshold  // Combined X+Y threshold
YX_Ratio_Min = 2.0  // Minimum Y/X ratio for penalty trigger
YX_Ratio_Max = 4.0  // Maximum Y/X ratio for penalty trigger

// X-Y penalty amounts (only for timeframes >= 1H)
XY_Penalty_Amount = switch
    is60min or is240min or isday or isweek or ismonth => isGold ? -25.0 : isBitcoin ? -35.0 : -30.0
    => 0.0  // No penalty for timeframes < 1H

// ==================== X-Y OVERSOLD BONUS SYSTEM ====================
// Bonus thresholds (perfectly opposite of penalty thresholds)
X_Bonus_Threshold = switch
    is60min => isGold ? -8 : isBitcoin ? -13.0 : -8  // 1H threshold
    is240min => isGold ? -9 : isBitcoin ? -12 : -9  // 4H threshold
    isday => isGold ? -6.5 : isBitcoin ? -7 : isVN ? -8 : -7     // Daily threshold
    isweek => isGold ? -9 : isBitcoin ? -9 : -9     // Weekly threshold
    ismonth => isGold ? -8.5 : isBitcoin ? -8.8 : -8.8     // Monthly threshold
    => 0.0  // No penalty for timeframes < 1H

Y_Bonus_Threshold = switch
    is60min => isGold ? -18 : isBitcoin ? -23 : -18.5  // 1H threshold
    is240min => isGold ? -15 : isBitcoin ? -18 : -15  // 4H threshold
    isday => isGold ? -15 : isBitcoin ? -17 : isVN ? -28 : -17  // Daily threshold
    isweek => isGold ? -13 : isBitcoin ? -13 : -12    // Weekly threshold
    ismonth => isGold ? -11 : isBitcoin ? -11 : -11     // Monthly threshold
    => 0.0  // No penalty for timeframes < 1H

XY_Sum_Bonus_Threshold = X_Bonus_Threshold + Y_Bonus_Threshold  // Combined X+Y bonus threshold

// X-Y bonus amounts (only for timeframes >= 1H)
XY_Bonus_Amount = switch
    is60min or is240min or isday or isweek or ismonth => isGold ? 20.0 : isBitcoin ? 25.0 : 22.0  // Positive bonus when oversold
    => 0.0  // No bonus for timeframes < 1H

// Optimized filter parameters using switch statements
get_short_filter_params() =>
    [length_val, sigma_val] = switch
        ismonth => [48, 48]
        isweek => [60, 60]
        isday => [40, 40]
        is240min => [60, 60]
        is60min => [60, 60]
        is15min => [63, 63]
        is5min => [180, 180]
        is3min => [300, 300]
        => [910, 910]
    [length_val, sigma_val]

get_medium_filter_params() =>
    [length_val, sigma_val] = switch
        ismonth => [120, 120]
        isweek => [120, 120]
        isday => [360, 360]
        is240min => [240, 240]
        is60min => [240, 240]
        is15min => [120, 120]
        is5min => [360, 360]
        is3min => [600, 600]
        => [1800, 1800]
    [length_val, sigma_val]

get_long_filter_params() =>
    [length_val, sigma_val] = switch
        ismonth => [300, 300]
        isweek => [240, 240]
        => [na, na]
    [length_val, sigma_val]

// Apply the optimized parameters
[length_s, sigma_s] = get_short_filter_params()
[length_m, sigma_m] = get_medium_filter_params()
[length, sigma] = get_long_filter_params()



// Fixed Color Variables (not user-adjustable)
//@variable Color for long-term uptrend
color longUpColor = color.rgb(255, 145, 0)
//@variable Color for long-term downtrend
color longDnColor = color.rgb(150, 20, 0)
//@variable Color for medium-term uptrend
color mediumUpColor = color.rgb(0, 180, 0)
//@variable Color for medium-term downtrend
color mediumDnColor = color.rgb(180, 0, 180)
//@variable Color for short-term uptrend
color shortUpColor = #00bcd4
//@variable Color for short-term downtrend
color shortDnColor = #ff5722

// Pattern Display Options - Fixed settings (no user inputs)
show_pattern_labels = true
show_high_reliability_only = true
use_volume_confirmation = true

// ==================== GAUSSIAN FILTER IMPLEMENTATION ====================
// Define a type for the Gaussian Filter parameters
type GaussianFilterParams
    int length
    int sigma

// Create instances of GaussianFilterParams
// Long-term filter only for W and M timeframes (optimization)
GaussianFilterParams longFilterParams = (isweek or ismonth) ? GaussianFilterParams.new(length, sigma) : na
GaussianFilterParams mediumFilterParams = GaussianFilterParams.new(length_m, sigma_m)
GaussianFilterParams shortFilterParams = GaussianFilterParams.new(length_s, sigma_s)
GaussianFilterParams shortTrendFilter = GaussianFilterParams.new(60, 60)
GaussianFilterParams longTrendFilter = GaussianFilterParams.new(963, 963)

// Gaussian filter function
gaussian_filter(float src, params) =>
    var float[] weights = array.new_float(params.length)  // Array to store Gaussian weights
    total = 0.0
    pi = math.pi
    
    // Calculate Gaussian weights
    for i = 0 to params.length - 1
        weight = math.exp(-0.5 * math.pow((i - params.length / 2) / params.sigma, 2.0)) / math.sqrt(params.sigma * 2.0 * pi)
        weights.set(i, weight)
        total := total + weight

    // Normalize weights
    for i = 0 to params.length - 1
        weights.set(i, weights.get(i) / total)

    // Apply filter
    sum = 0.0
    for i = 0 to params.length - 1
        sum := sum + src[i] * weights.get(i)
    sum

// Variables for tracking trend
var longTermTrend = bool(na)  // Current long-term trend direction (true = up, false = down)
var mediumTermTrend = bool(na) // Current medium-term trend direction (true = up, false = down)
var shortTermTrend = bool(na) // Current short-term trend direction (true = up, false = down)

// ==================== CALCULATIONS ====================
// Calculate base FEX_ALL
fex_all = calc_fex_all_outbound()

// Apply penalties and bonuses (calculated later in the code)
var float final_fex_penalty = 0.0
var float final_xy_penalty = 0.0
var float final_xy_bonus = 0.0

// ==================== PERSISTENT PENALTY POINTS SYSTEM ====================
// Variables to track persistent penalty points that accumulate over time
var float persistent_basic_penalty_points = 0.0
var float persistent_fex_penalty_points = 0.0
var float persistent_xy_penalty_points = 0.0
var float persistent_total_penalty_points = 0.0

// Calculate the smoothed Gaussian values
// Long-term filter only calculated for W and M timeframes (optimization)
longTermFilter = (isweek or ismonth) ? gaussian_filter(close, longFilterParams) : na
shortTermFilter = gaussian_filter(close, shortFilterParams)

// Helper function to get mediumTermFilter for specific timeframe
get_medium_filter(tf) =>
    request.security(syminfo.tickerid, tf, gaussian_filter(close, shortFilterParams), lookahead=barmerge.lookahead_off)

// CONSISTENCY FIX: Get the exact same shortTermFilter that generates the signals
get_signal_source_filter(tf) =>
    request.security(syminfo.tickerid, tf, shortTermFilter, lookahead=barmerge.lookahead_off)

// Calculate all possible mediumTermFilter values - use signal source for consistency
mediumTermFilter_4H = get_signal_source_filter("240")  // Use exact 4H shortTermFilter
mediumTermFilter_D = get_signal_source_filter("D")     // Use exact Daily shortTermFilter  
mediumTermFilter_W = get_signal_source_filter("W")     // Use exact Weekly shortTermFilter
mediumTermFilter_M = get_signal_source_filter("M")     // Use exact Monthly shortTermFilter
mediumTermFilter_default = gaussian_filter(close, mediumFilterParams)

// Enhanced mediumTermFilter logic based on timeframe using switch
mediumTermFilter = switch
    is60min => mediumTermFilter_4H
    is240min => mediumTermFilter_D
    isday => mediumTermFilter_W
    isweek => mediumTermFilter_M  // Weekly uses Monthly short data for consistency
    => mediumTermFilter_default

// Detect trend changes based on crossover conditions
// Long-term trend detection only for W and M timeframes (optimization)
longTermUp = (isweek or ismonth) ? ta.rising(longTermFilter, 4) : false
longTermDown = (isweek or ismonth) ? ta.falling(longTermFilter, 4) : false
mediumTermUp = ta.rising(mediumTermFilter, 4)
mediumTermDown = ta.falling(mediumTermFilter, 4)
shortTermUp = ta.rising(shortTermFilter, 4)
shortTermDown = ta.falling(shortTermFilter, 4)

// Set the trend direction based on the detected conditions
// Long-term trend only updated for W and M timeframes (optimization)
if (longTermUp and (isweek or ismonth))
    longTermTrend := true
if (longTermDown and (isweek or ismonth))
    longTermTrend := false
if (mediumTermUp)
    mediumTermTrend := true
if (mediumTermDown)
    mediumTermTrend := false
if (shortTermUp)
    shortTermTrend := true
if (shortTermDown)
    shortTermTrend := false

// Determine colors based on trend
// Long-term color only relevant for W and M timeframes (optimization)
color longTermColor = (isweek or ismonth) ? (longTermTrend ? longUpColor : longDnColor) : na
color mediumTermColor = mediumTermTrend ? mediumUpColor : mediumDnColor
// For timeframes >= 4H, use alternative trend detection for short-term color
// This will be calculated after alt_shortTrendUp/Down are defined
var color shortTermColor = shortTermTrend ? shortUpColor : shortDnColor

// Function to calculate Gaussian filter with specific parameters
calcGaussianWithParams(src, params) =>
    gaussian_filter(src, params)

// ==================== FIXED TIMEFRAME TREND DETECTION ====================
// Helper function to get 1H trend filters using tuple optimization
get_1h_trend_filters() =>
    request.security(syminfo.tickerid, "60", [calcGaussianWithParams(close, shortTrendFilter), calcGaussianWithParams(close, longTrendFilter)], lookahead=barmerge.lookahead_off)

// Use the existing shortTrendFilter and longTrendFilter parameters but calculate them on 1H timeframe
[shortTF, longTF] = get_1h_trend_filters()

// Determine trend based on Short and Long relationship
shortAboveLong = shortTF > longTF
shortBelowLong = shortTF < longTF

// Calculate slope of shortTF using longer timeframe to reduce noise
lookback_period = 15  // Use 15 bars for more stable slope calculation
shortTF_slope = (shortTF - shortTF[lookback_period]) / lookback_period  // Average change per bar over 15 bars
shortTF_slope_normalized = shortTF_slope / shortTF * 1000  // Normalize as percentage * 1000

// Calculate smoothed slope with longer smoothing period
shortTF_slope_smooth = ta.sma(shortTF_slope_normalized, 5)  // 5-bar average of slope for extra stability

// Calculate distance between close and shortTF as percentage
close_shortTF_distance = math.abs(close - shortTF) / shortTF * 100

// Define slope thresholds for strong trends (adjusted for longer lookback period)
slope_threshold_very_strong = 1.5   // 0.15% per bar over 15 bars = very strong trend
slope_threshold_strong = 0.8        // 0.08% per bar over 15 bars = strong trend  
slope_threshold_weak = 0.3          // 0.03% per bar over 15 bars = weak trend

// Determine Short trend direction with slope consideration
shortTrendUp = ta.rising(shortTF, 4) or shortTF_slope_smooth > slope_threshold_strong
shortTrendDown = ta.falling(shortTF, 4) or shortTF_slope_smooth < -slope_threshold_strong
shortTrendVeryStrong = math.abs(shortTF_slope_smooth) > slope_threshold_very_strong
shortTrendStrong = math.abs(shortTF_slope_smooth) > slope_threshold_strong
shortTrendWeak = math.abs(shortTF_slope_smooth) < slope_threshold_weak

// Determine position signals based on the requirements
// 1) Short > Long 
//    - short(color up) -> close > Short (Buy) , close < Short (Weak Buy)
//    - short(color down) => weak Sell
// 2) Short < Long
//    - short(color down) -> close < Short (Sell) , close > Short (Weak Sell)
//    - short(color up) => weak Buy

// Position signals - Improved with slope filtering to reduce fake signals
// Buy: close > shortTF and shortTrendUp
buySignal = shortTrendUp and close > shortTF

// Sell: close < shortTF and shortTrendDown  
sellSignal = shortTrendDown and close < shortTF

// WB (Weak Buy): Improved logic with long-term slope filtering
// - Normal case: shortTrendDown and close > shortTF
// - Sideways case: no clear trend and close > shortTF  
// - Very strong uptrend case: only if long-term slope is VERY strong AND close is very close to shortTF
weakBuySignal = (shortTrendDown and close > shortTF) or (not shortTrendUp and not shortTrendDown and close > shortTF) or (shortTrendUp and close < shortTF and shortTrendVeryStrong and shortTF_slope_smooth > 1.8 and close_shortTF_distance < 0.3)

// WS (Weak Sell): Improved logic with long-term slope filtering
// - Normal case: shortTrendUp and close < shortTF (but not in very strong uptrend)
// - Sideways case: no clear trend and close < shortTF
// - Very strong downtrend case: only if long-term slope is VERY strong AND close is very close to shortTF
weakSellSignal = (shortTrendUp and close < shortTF and not (shortTrendVeryStrong and shortTF_slope_smooth > 1.8)) or (not shortTrendUp and not shortTrendDown and close < shortTF) or (shortTrendDown and close > shortTF and shortTrendVeryStrong and shortTF_slope_smooth < -1.8 and close_shortTF_distance < 0.3)

// ==================== TIMEFRAME-BASED SIGNAL LOGIC ====================
// For timeframes <= 3H, use 1H timeframe signals (shortTF/longTF based)
// For timeframes >= 4H, use current timeframe signals (shortTermFilter based)

// Determine if we should use 1H timeframe signals for lower timeframes
// For timeframes <= 1H: use 1H signals
// For timeframes 2H, 3H: also use 1H signals (closer to 1H than 4H)
// For timeframes >= 4H: use current timeframe signals
use_1h_signals = is1min or is3min or is5min or is15min or is30min or is60min or is120min or is180min  // <= 3H timeframes

// Calculate alternative signals based on shortTermFilter for higher timeframes (>= 4H)
alt_shortTrendUp = ta.rising(shortTermFilter, 4)
alt_shortTrendDown = ta.falling(shortTermFilter, 4)

// Update shortTermColor based on current timeframe
// For timeframes >= 4H, use alt_shortTrend logic; for lower timeframes, use original shortTermTrend
is_high_timeframe = (timeframe.isintraday and timeframe.multiplier >= 240) or timeframe.isdaily or timeframe.isweekly or timeframe.ismonthly
shortTermColor := is_high_timeframe ? 
                  (alt_shortTrendUp ? shortUpColor : alt_shortTrendDown ? shortDnColor : (shortTermTrend ? shortUpColor : shortDnColor)) : 
                  (shortTermTrend ? shortUpColor : shortDnColor)

// ==================== NEW SIGNAL LOGIC WITH STATE TRACKING ====================
// Variable to track previous signal state
var string prev_alt_signal = na

// Now all signals use the same centralized distance system
// For timeframes >= 4H: use short_distance (which now uses centralized multiplier)
// For timeframes < 4H: use distance_current (which also uses centralized multiplier)
signal_distance = is240min or isday or isweek or ismonth ? short_distance : distance_current

// Strong signals - require trend + distance + previous state validation
alt_buySignal = alt_shortTrendUp and close > (shortTermFilter - signal_distance) and (prev_alt_signal == "WB" or na(prev_alt_signal))
alt_sellSignal = alt_shortTrendDown and close < (shortTermFilter + signal_distance) and (prev_alt_signal == "WS" or na(prev_alt_signal))

// Weak signals - ensure every candle has a status with state transition logic
alt_weakBuySignal = ((close > (shortTermFilter + signal_distance))) and (prev_alt_signal == "S" or prev_alt_signal == "WS" or na(prev_alt_signal))
alt_weakSellSignal =((close < (shortTermFilter - signal_distance))) and (prev_alt_signal == "B" or prev_alt_signal == "WB" or na(prev_alt_signal))

// Determine Final_Alt_Signal based on priority and state transitions
Final_Alt_Signal = alt_buySignal ? "B" : 
                   alt_sellSignal ? "S" : 
                   alt_weakBuySignal ? "WB" : 
                   alt_weakSellSignal ? "WS" : 
                   prev_alt_signal // Keep previous state if no condition is met

// Update previous signal for next bar
prev_alt_signal := Final_Alt_Signal

// Functions to calculate trend conditions for 1H timeframe
calcShortTrend1H() =>
    shortFilter = calcGaussianWithParams(close, shortTrendFilter)
    shortUp = ta.rising(shortFilter, 4)
    shortDown = ta.falling(shortFilter, 4)
    var shortTrend = bool(na)
    if shortUp
        shortTrend := true
    if shortDown
        shortTrend := false
    shortTrend

calcLongTrend1H() =>
    longFilter = calcGaussianWithParams(close, longTrendFilter)
    longUp = ta.rising(longFilter, 4)
    longDown = ta.falling(longFilter, 4)
    var longTrend = bool(na)
    if longUp
        longTrend := true
    if longDown
        longTrend := false
    longTrend

// Functions to calculate individual signals from 1H timeframe data
// ==================== SIMPLIFIED 1H SIGNAL LOGIC ====================
calcBuySignal1H() =>
    // Get 1H data
    close1H = close
    shortTF1H = calcGaussianWithParams(close1H, shortTrendFilter)
    
    // Calculate slope for 1H
    lookback_period_1h = 15
    shortTF_slope_1h = (shortTF1H - shortTF1H[lookback_period_1h]) / lookback_period_1h
    shortTF_slope_normalized_1h = shortTF_slope_1h / shortTF1H * 1000
    shortTF_slope_smooth_1h = ta.sma(shortTF_slope_normalized_1h, 5)
    
    // Define thresholds
    slope_threshold_strong_1h = 0.8
    
    // Determine trends
    shortTrendUp1H = ta.rising(shortTF1H, 4) or shortTF_slope_smooth_1h > slope_threshold_strong_1h
    
    // Calculate buy signal using 1H data with centralized distance
    distance_1h_centralized = volatility_current * multiplier_1h  // Use centralized 1H multiplier
    shortTrendUp1H and close1H > (shortTF1H + distance_1h_centralized)

calcSellSignal1H() =>
    // Get 1H data
    close1H = close
    shortTF1H = calcGaussianWithParams(close1H, shortTrendFilter)
    
    // Calculate slope for 1H
    lookback_period_1h = 15
    shortTF_slope_1h = (shortTF1H - shortTF1H[lookback_period_1h]) / lookback_period_1h
    shortTF_slope_normalized_1h = shortTF_slope_1h / shortTF1H * 1000
    shortTF_slope_smooth_1h = ta.sma(shortTF_slope_normalized_1h, 5)
    
    // Define thresholds
    slope_threshold_strong_1h = 0.8
    
    // Determine trends
    shortTrendDown1H = ta.falling(shortTF1H, 4) or shortTF_slope_smooth_1h < -slope_threshold_strong_1h
    
    // Calculate sell signal using 1H data with centralized distance
    distance_1h_centralized = volatility_current * multiplier_1h  // Use centralized 1H multiplier
    shortTrendDown1H and close1H < (shortTF1H - distance_1h_centralized)

calcWeakBuySignal1H() =>
    // Get 1H data
    close1H = close
    shortTF1H = calcGaussianWithParams(close1H, shortTrendFilter)
    
    // Calculate slope for 1H
    lookback_period_1h = 15
    shortTF_slope_1h = (shortTF1H - shortTF1H[lookback_period_1h]) / lookback_period_1h
    shortTF_slope_normalized_1h = shortTF_slope_1h / shortTF1H * 1000
    shortTF_slope_smooth_1h = ta.sma(shortTF_slope_normalized_1h, 5)
    
    // Define thresholds
    slope_threshold_strong_1h = 0.8
    
    // Determine trends
    shortTrendUp1H = ta.rising(shortTF1H, 4) or shortTF_slope_smooth_1h > slope_threshold_strong_1h
    
    // Calculate weak buy signal using 1H data with centralized distance
    distance_1h_centralized = volatility_current * multiplier_1h  // Use centralized 1H multiplier
    shortTrendUp1H and close1H < (shortTF1H + distance_1h_centralized)

calcWeakSellSignal1H() =>
    // Get 1H data
    close1H = close
    shortTF1H = calcGaussianWithParams(close1H, shortTrendFilter)
    
    // Calculate slope for 1H
    lookback_period_1h = 15
    shortTF_slope_1h = (shortTF1H - shortTF1H[lookback_period_1h]) / lookback_period_1h
    shortTF_slope_normalized_1h = shortTF_slope_1h / shortTF1H * 1000
    shortTF_slope_smooth_1h = ta.sma(shortTF_slope_normalized_1h, 5)
    
    // Define thresholds
    slope_threshold_strong_1h = 0.8
    
    // Determine trends
    shortTrendDown1H = ta.falling(shortTF1H, 4) or shortTF_slope_smooth_1h < -slope_threshold_strong_1h
    
    // Calculate weak sell signal using 1H data with centralized distance
    distance_1h_centralized = volatility_current * multiplier_1h  // Use centralized 1H multiplier
    shortTrendDown1H and close1H > (shortTF1H - distance_1h_centralized)

// Get 1H timeframe trend data for color determination
shortTrend1H = request.security(syminfo.tickerid, "60", calcShortTrend1H(), lookahead=barmerge.lookahead_off)
longTrend1H = request.security(syminfo.tickerid, "60", calcLongTrend1H(), lookahead=barmerge.lookahead_off)

// Get 1H timeframe signals
buySignal1H_raw = request.security(syminfo.tickerid, "60", calcBuySignal1H(), lookahead=barmerge.lookahead_off)
sellSignal1H_raw = request.security(syminfo.tickerid, "60", calcSellSignal1H(), lookahead=barmerge.lookahead_off)
weakBuySignal1H_raw = request.security(syminfo.tickerid, "60", calcWeakBuySignal1H(), lookahead=barmerge.lookahead_off)
weakSellSignal1H_raw = request.security(syminfo.tickerid, "60", calcWeakSellSignal1H(), lookahead=barmerge.lookahead_off)

// ==================== 1H STATE TRACKING LOGIC ====================
// Variable to track previous 1H signal state
var string prev_1h_signal = na

// Apply state transition logic for 1H signals - matching Alt logic
// Helper function to get 1H data for distance calculation using tuple optimization
get_1h_distance_data() =>
    request.security(syminfo.tickerid, "60", [close, calcGaussianWithParams(close, shortTrendFilter)], lookahead=barmerge.lookahead_off)

// Get 1H data for distance calculation
[close1H, shortTF1H] = get_1h_distance_data()

// Get 1H trend conditions
shortTrendUp1H = request.security(syminfo.tickerid, "60", calcShortTrend1H(), lookahead=barmerge.lookahead_off)
shortTrendDown1H = not shortTrendUp1H

// Apply same logic as Alt signals using centralized distance system
// For 1H signals, use 1H volatility with centralized 1H multiplier
distance_1h_centralized = volatility_1h * multiplier_1h  // Use centralized multiplier for 1H timeframe

buySignal1H = shortTrendUp1H and close1H > (shortTF1H - distance_1h_centralized) and (prev_1h_signal == "WB" or na(prev_1h_signal))
sellSignal1H = shortTrendDown1H and close1H < (shortTF1H + distance_1h_centralized) and (prev_1h_signal == "WS" or na(prev_1h_signal))
weakBuySignal1H = close1H > (shortTF1H + distance_1h_centralized) and (prev_1h_signal == "S" or prev_1h_signal == "WS" or na(prev_1h_signal))
weakSellSignal1H = close1H < (shortTF1H - distance_1h_centralized) and (prev_1h_signal == "B" or prev_1h_signal == "WB" or na(prev_1h_signal))


// Determine Final_1H_Signal based on priority and state transitions
Final_1H_Signal = buySignal1H ? "B" : 
                  sellSignal1H ? "S" : 
                  weakBuySignal1H ? "WB" : 
                  weakSellSignal1H ? "WS" : 
                  prev_1h_signal // Keep previous state if no condition is met

// Update previous 1H signal for next bar
prev_1h_signal := Final_1H_Signal

// Use the 1H timeframe trends for coloring - these will only change when 1H candles complete
shortTFColor = shortTrend1H ? mediumUpColor : mediumDnColor
longTFColor = longTrend1H ? longUpColor : longDnColor

// ==================== CORRECTED SIGNAL LOGIC FOR CONSISTENCY ====================
// Each timeframe should use its own ShortTermFilter for tiny arrows
// This ensures tiny arrows match the ShortTermFilter cloud on each timeframe

// Helper function to calculate current timeframe signals using current shortTermFilter
calc_current_timeframe_signals() =>
    // Use current timeframe's shortTermFilter and trend
    current_shortTrendUp = ta.rising(shortTermFilter, 4)
    current_shortTrendDown = ta.falling(shortTermFilter, 4)
    
    // Use appropriate distance for current timeframe (centralized system)
    current_signal_distance = switch
        is60min => short_distance    // 1H uses short_distance
        is240min => short_distance   // 4H uses short_distance  
        isday => short_distance      // Daily uses short_distance
        isweek => short_distance     // Weekly uses short_distance
        ismonth => short_distance    // Monthly uses short_distance
        => distance_current          // Smaller timeframes use distance_current
    
    // State tracking for current timeframe
    var string prev_current_signal = na
    
    // Calculate signals using current timeframe data
    current_buySignal = current_shortTrendUp and close > (shortTermFilter - current_signal_distance) and (prev_current_signal == "WB" or na(prev_current_signal))
    current_sellSignal = current_shortTrendDown and close < (shortTermFilter + current_signal_distance) and (prev_current_signal == "WS" or na(prev_current_signal))
    current_weakBuySignal = ((close > (shortTermFilter + current_signal_distance))) and (prev_current_signal == "S" or prev_current_signal == "WS" or na(prev_current_signal))
    current_weakSellSignal = (close < (shortTermFilter - current_signal_distance)) and (prev_current_signal == "B" or prev_current_signal == "WB" or na(prev_current_signal))
    
    // Determine final signal
    final_current_signal = current_buySignal ? "B" : 
                          current_sellSignal ? "S" : 
                          current_weakBuySignal ? "WB" : 
                          current_weakSellSignal ? "WS" : 
                          prev_current_signal
    
    // Update state
    prev_current_signal := final_current_signal
    
    // Return signal
    final_current_signal

// Get current timeframe signals
Final_Current_Signal = calc_current_timeframe_signals()

// Now use the CORRECTED logic:
// All timeframes use their own shortTermFilter for tiny arrows (consistency with cloud)
Final_Signal = Final_Current_Signal

// Convert Final_Signal to individual boolean signals for compatibility with existing plots
final_buySignal = Final_Signal == "B"
final_sellSignal = Final_Signal == "S" 
final_weakBuySignal = Final_Signal == "WB"
final_weakSellSignal = Final_Signal == "WS"

// ==================== MEDIUM TERM FILTER SIGNALS ====================
// Create MediumTermFilter signals similar to ShortTermFilter

// Function to calculate medium term trend for 1H timeframe
calcMediumTrend1H() =>
    mediumFilter = calcGaussianWithParams(close, mediumFilterParams)
    mediumUp = ta.rising(mediumFilter, 4)
    mediumUp

// Function to calculate 4H medium trend
calcMediumTrend4H() =>
    mediumFilter = calcGaussianWithParams(close, mediumFilterParams)
    mediumUp = ta.rising(mediumFilter, 4)
    mediumUp

// Variable to track previous medium signal state
var string prev_medium_signal = na
var string prev_medium_1h_signal = na

// For timeframes >= 1H: use current timeframe mediumTermFilter
// Calculate medium term signals for current timeframe (>= 1H)
// Need to use close from the source timeframe of mediumTermFilter
// Also need to get trend from the source timeframe, not current timeframe
mediumTrendUp = ta.rising(mediumTermFilter, 4)
mediumTrendDown = ta.falling(mediumTermFilter, 4)

// Helper function to get trend conditions for specific timeframe using tuple
get_trend_conditions(tf) =>
    request.security(syminfo.tickerid, tf, [ta.rising(shortTermFilter, 4), ta.falling(shortTermFilter, 4)], lookahead=barmerge.lookahead_off)

// Get trend conditions from source timeframes using optimized tuple approach
[mediumTrendUp_4H, mediumTrendDown_4H] = get_trend_conditions("240")
[mediumTrendUp_D, mediumTrendDown_D] = get_trend_conditions("D")
[mediumTrendUp_W, mediumTrendDown_W] = get_trend_conditions("W")
[mediumTrendUp_M, mediumTrendDown_M] = get_trend_conditions("M")

// Select appropriate trend based on timeframe using switch
trend_up_for_medium = switch
    is60min => mediumTrendUp_4H
    is240min => mediumTrendUp_D
    isday => mediumTrendUp_W
    isweek => mediumTrendUp_M
    => mediumTrendUp

trend_down_for_medium = switch
    is60min => mediumTrendDown_4H
    is240min => mediumTrendDown_D
    isday => mediumTrendDown_W
    isweek => mediumTrendDown_M
    => mediumTrendDown

// Helper function to get close prices from multiple timeframes
get_close_prices() =>
    close_4H = request.security(syminfo.tickerid, "240", close, lookahead=barmerge.lookahead_off)
    close_D = request.security(syminfo.tickerid, "D", close, lookahead=barmerge.lookahead_off)
    close_W = request.security(syminfo.tickerid, "W", close, lookahead=barmerge.lookahead_off)
    close_M = request.security(syminfo.tickerid, "M", close, lookahead=barmerge.lookahead_off)
    [close_4H, close_D, close_W, close_M]

// Pre-calculate all required close prices from different timeframes
[close_4H, close_D, close_W, close_M] = get_close_prices()

// Get close price from the appropriate source timeframe for mediumTermFilter using switch
close_for_medium = switch
    is60min => close_4H
    is240min => close_D
    isday => close_W
    isweek => close_M  // Weekly uses Monthly close (matches Monthly shortTermFilter)
    => close

// For proper signal logic, we need to calculate signals directly from source timeframes
// instead of using current timeframe's previous state

// Calculate medium signals directly based on conditions from source timeframes
// This ensures we get the actual signal from the source timeframe
alt_mediumBuySignal = trend_up_for_medium and close_for_medium > (mediumTermFilter - medium_distance)
alt_mediumSellSignal = trend_down_for_medium and close_for_medium < (mediumTermFilter + medium_distance)
alt_mediumWeakBuySignal = close_for_medium > (mediumTermFilter + medium_distance)
alt_mediumWeakSellSignal = close_for_medium < (mediumTermFilter - medium_distance)

// Determine Final_Alt_Medium_Signal with priority order using switch
Final_Alt_Medium_Signal = switch
    alt_mediumBuySignal => "B"
    alt_mediumSellSignal => "S"
    alt_mediumWeakBuySignal => "WB"
    alt_mediumWeakSellSignal => "WS"
    => prev_medium_signal // Keep previous state instead of "NONE"

// Update previous medium signal for next bar
prev_medium_signal := Final_Alt_Medium_Signal

// For timeframes < 1H: use 1H mediumTermFilter data
// Helper function to get 1H medium term filter data using tuple optimization
get_1h_medium_data() =>
    request.security(syminfo.tickerid, "60", [close, calcGaussianWithParams(close, mediumFilterParams)], lookahead=barmerge.lookahead_off)

// Get 1H medium term filter data
[close1H_medium, mediumTF1H] = get_1h_medium_data()

// Distance for 1H medium term filter - use centralized system (1H medium uses 4H timeframe logic)
// For 1H medium signals, use 4H volatility with 4H multiplier for consistency with 4H shortTermFilter
medium_distance_1h_centralized = volatility_4h * get_multiplier_for_timeframe("240")  // Use 4H multiplier for consistency

// Get 1H medium trend conditions
mediumTrendUp1H = request.security(syminfo.tickerid, "60", calcMediumTrend1H(), lookahead=barmerge.lookahead_off)
mediumTrendDown1H = not mediumTrendUp1H

// Medium term signals using 1H data with centralized distance
mediumBuySignal1H = mediumTrendUp1H and close1H_medium > (mediumTF1H - medium_distance_1h_centralized) and (prev_medium_1h_signal == "WB" or na(prev_medium_1h_signal))
mediumSellSignal1H = mediumTrendDown1H and close1H_medium < (mediumTF1H + medium_distance_1h_centralized) and (prev_medium_1h_signal == "WS" or na(prev_medium_1h_signal))
mediumWeakBuySignal1H = close1H_medium > (mediumTF1H + medium_distance_1h_centralized) and (prev_medium_1h_signal == "S" or prev_medium_1h_signal == "WS" or na(prev_medium_1h_signal))
mediumWeakSellSignal1H = close1H_medium < (mediumTF1H - medium_distance_1h_centralized) and (prev_medium_1h_signal == "B" or prev_medium_1h_signal == "WB" or na(prev_medium_1h_signal))

// Determine Final_1H_Medium_Signal using switch
Final_1H_Medium_Signal = switch
    mediumBuySignal1H => "B"
    mediumSellSignal1H => "S"
    mediumWeakBuySignal1H => "WB"
    mediumWeakSellSignal1H => "WS"
    => prev_medium_1h_signal

// Update previous 1H medium signal for next bar
prev_medium_1h_signal := Final_1H_Medium_Signal

// ==================== 4H MEDIUM SIGNALS ====================
// Helper function to get 4H medium term filter data using tuple optimization
get_4h_medium_data() =>
    request.security(syminfo.tickerid, "240", [close, calcGaussianWithParams(close, mediumFilterParams)], lookahead=barmerge.lookahead_off)

// Request 4H data for medium signals (matching 1H logic exactly)
[close4H_medium, mediumTF4H] = get_4h_medium_data()

// Distance for 4H medium term filter - use centralized system (4H medium uses D timeframe logic)
// For 4H medium signals, use Daily volatility with Daily multiplier for consistency with Daily shortTermFilter
medium_distance_4h_centralized = volatility_d * get_multiplier_for_timeframe("D")

// Get 4H medium trend conditions
mediumTrendUp4H = request.security(syminfo.tickerid, "240", calcMediumTrend4H(), lookahead=barmerge.lookahead_off)
mediumTrendDown4H = not mediumTrendUp4H

// Previous 4H medium signal state
var string prev_medium_4h_signal = na

// Medium term signals using 4H data with centralized distance
mediumBuySignal4H = mediumTrendUp4H and close4H_medium > (mediumTF4H - medium_distance_4h_centralized) and (prev_medium_4h_signal == "WB" or na(prev_medium_4h_signal))
mediumSellSignal4H = mediumTrendDown4H and close4H_medium < (mediumTF4H + medium_distance_4h_centralized) and (prev_medium_4h_signal == "WS" or na(prev_medium_4h_signal))
mediumWeakBuySignal4H = close4H_medium > (mediumTF4H + medium_distance_4h_centralized) and (prev_medium_4h_signal == "S" or prev_medium_4h_signal == "WS" or na(prev_medium_4h_signal))
mediumWeakSellSignal4H = close4H_medium < (mediumTF4H - medium_distance_4h_centralized) and (prev_medium_4h_signal == "B" or prev_medium_4h_signal == "WB" or na(prev_medium_4h_signal))

// Determine Final_4H_Medium_Signal
Final_4H_Medium_Signal = mediumBuySignal4H ? "B" : mediumSellSignal4H ? "S" : mediumWeakBuySignal4H ? "WB" : mediumWeakSellSignal4H ? "WS" : prev_medium_4h_signal

// Update previous 4H medium signal for next bar
prev_medium_4h_signal := Final_4H_Medium_Signal

// ==================== HIGHER TIMEFRAME SIGNALS ====================
// Helper function to get real-time signals for specific timeframe
get_realtime_signal(tf) =>
    request.security(syminfo.tickerid, tf, Final_Alt_Signal, lookahead=barmerge.lookahead_off)

// Request Final_Alt_Signal directly from higher timeframes for consistent display
Final_4H_Short_Signal = get_realtime_signal("240")
Final_D_Signal = get_realtime_signal("D")
Final_W_Signal = get_realtime_signal("W")
Final_M_Signal = get_realtime_signal("M")

// Helper function to get CONFIRMED signals for specific timeframe
get_confirmed_signal(tf) =>
    request.security(syminfo.tickerid, tf, Final_Alt_Signal, lookahead=barmerge.lookahead_on)

// CONFIRMED SIGNALS - Only change when higher timeframe bar is closed
Final_4H_Signal_Confirmed = get_confirmed_signal("240")
Final_D_Signal_Confirmed = get_confirmed_signal("D")
Final_W_Signal_Confirmed = get_confirmed_signal("W")
Final_M_Signal_Confirmed = get_confirmed_signal("M")

// Final medium signals - use direct signals from source timeframes for consistency
// This ensures the triangle status matches exactly what you see on the source timeframe
Final_Medium_Signal = switch
    use_1h_signals => Final_4H_Signal_Confirmed  // For < 4H: use CONFIRMED 4H signals (only changes when 4H bar closes)
    is240min => Final_D_Signal_Confirmed         // 4H uses CONFIRMED Daily signals (only changes when Daily bar closes)
    isday => Final_W_Signal_Confirmed            // Daily uses CONFIRMED Weekly signals (only changes when Weekly bar closes)
    isweek => Final_M_Signal_Confirmed           // Weekly uses CONFIRMED Monthly signals (only changes when Monthly bar closes)
    ismonth => Final_Alt_Medium_Signal           // Monthly uses its own medium calculation
    => Final_Alt_Medium_Signal                   // Default fallback

// Convert Final_Medium_Signal to individual boolean signals
final_mediumBuySignal = Final_Medium_Signal == "B"
final_mediumSellSignal = Final_Medium_Signal == "S" 
final_mediumWeakBuySignal = Final_Medium_Signal == "WB"
final_mediumWeakSellSignal = Final_Medium_Signal == "WS"

// ==================== ADVANCED CANDLESTICK PATTERN DETECTION ====================
// ENHANCED CANDLE COLORING SYSTEM:
// PRIORITY SYSTEM:
// 1. Strongest Patterns (Marubozu): Lime/Red - Very strong momentum
// 2. High Reliability Reversal Patterns: Aqua/Fuchsia (Engulfing), Blue (Hammer), Purple (Shooting Star)
// 3. Consolidation/Breakout: White (Outside Bar), Navy (Inside Bar)
// 4. Neutral/Indecision: Yellow (Doji), Silver (Spinning Top)
// 5. Filter-based Analysis: Green shades (Bullish), Red shades (Bearish)
// 6. Mixed Signals: Teal/Orange based on stronger filter
// PATTERN LABELS: BE=Engulfing, H=Hammer, SS=Shooting Star, M+/M-=Marubozu, D=Doji, ST=Spinning Top, OB=Outside Bar, IB=Inside Bar
// FILTER COMBINATION: Short-term Filter Weight: 40%, Medium-term Filter Weight: 60% (more stable)

// Candlestick pattern detection functions
isDoji() =>
    body_size = math.abs(close - open)
    candle_range = high - low
    body_size <= (candle_range * 0.1) and candle_range > 0

isHammer() =>
    body_size = math.abs(close - open)
    upper_shadow = high - math.max(close, open)
    lower_shadow = math.min(close, open) - low
    candle_range = high - low
    candle_range > 0 and body_size <= (candle_range * 0.3) and lower_shadow >= (candle_range * 0.6) and upper_shadow <= (candle_range * 0.1)

isShootingStar() =>
    body_size = math.abs(close - open)
    upper_shadow = high - math.max(close, open)
    lower_shadow = math.min(close, open) - low
    candle_range = high - low
    candle_range > 0 and body_size <= (candle_range * 0.3) and upper_shadow >= (candle_range * 0.6) and lower_shadow <= (candle_range * 0.1)

isBullishEngulfing() =>
    prev_bearish = open[1] > close[1]
    curr_bullish = close > open
    curr_engulfs = open < close[1] and close > open[1]
    prev_bearish and curr_bullish and curr_engulfs

isBearishEngulfing() =>
    prev_bullish = close[1] > open[1]
    curr_bearish = open > close
    curr_engulfs = open > close[1] and close < open[1]
    prev_bullish and curr_bearish and curr_engulfs

isPiercingLine() =>
    prev_bearish = open[1] > close[1]
    curr_bullish = close > open
    prev_body_size = math.abs(open[1] - close[1])
    curr_opens_below = open < close[1]
    curr_closes_above_mid = close > (open[1] + close[1]) / 2
    curr_closes_below_open = close < open[1]
    prev_bearish and curr_bullish and curr_opens_below and curr_closes_above_mid and curr_closes_below_open and prev_body_size > 0

isHangingMan() =>
    body_size = math.abs(close - open)
    upper_shadow = high - math.max(close, open)
    lower_shadow = math.min(close, open) - low
    candle_range = high - low
    is_small_body = candle_range > 0 and body_size <= (candle_range * 0.3)
    is_long_lower_shadow = lower_shadow >= (candle_range * 0.6)
    is_short_upper_shadow = upper_shadow <= (candle_range * 0.1)
    is_at_high = close > ta.sma(close, 20)  // Should appear after uptrend
    is_small_body and is_long_lower_shadow and is_short_upper_shadow and is_at_high

isPinBar() =>
    body_size = math.abs(close - open)
    candle_range = high - low
    candle_range > 0 and body_size <= (candle_range * 0.25)

isMarubozu() =>
    body_size = math.abs(close - open)
    upper_shadow = high - math.max(close, open)
    lower_shadow = math.min(close, open) - low
    candle_range = high - low
    candle_range > 0 and body_size >= (candle_range * 0.95) and upper_shadow <= (candle_range * 0.025) and lower_shadow <= (candle_range * 0.025)

isSpinningTop() =>
    body_size = math.abs(close - open)
    upper_shadow = high - math.max(close, open)
    lower_shadow = math.min(close, open) - low
    candle_range = high - low
    candle_range > 0 and body_size <= (candle_range * 0.3) and upper_shadow >= (candle_range * 0.3) and lower_shadow >= (candle_range * 0.3)

isInsideBar() =>
    high < high[1] and low > low[1]

isOutsideBar() =>
    high > high[1] and low < low[1]

// ==================== VOLUME ANALYSIS FOR PATTERN CONFIRMATION ====================
// Volume moving averages for comparison
volume_sma_20 = ta.sma(volume, 20)  // 20-period volume average
volume_sma_50 = ta.sma(volume, 50)  // 50-period volume average

// Volume conditions for pattern confirmation
high_volume = volume > volume_sma_20 * 1.5  // Volume 50% above 20-period average
very_high_volume = volume > volume_sma_20 * 2.0  // Volume 100% above average
above_avg_volume = volume > volume_sma_20  // Volume above average
significant_volume = volume > volume_sma_50 * 1.2  // Volume 20% above 50-period average

// Volume trend analysis (increasing volume over last few bars)
volume_increasing = volume > volume[1] and volume[1] > volume[2]
volume_surge = volume > volume[1] * 1.5  // Current volume 50% higher than previous

// Combined volume confirmation for different pattern types
// For reversal patterns: need high volume to confirm strength
reversal_volume_confirm = high_volume or (above_avg_volume and volume_increasing)

// For continuation patterns: moderate volume is acceptable
continuation_volume_confirm = above_avg_volume or significant_volume

// For breakout patterns: need very high volume
breakout_volume_confirm = very_high_volume or (high_volume and volume_surge)

// Pattern detection with volume confirmation
// Basic pattern detection (without volume)
basic_doji = isDoji()
basic_hammer = isHammer()
basic_shooting_star = isShootingStar()
basic_bullish_engulfing = isBullishEngulfing()
basic_bearish_engulfing = isBearishEngulfing()
basic_piercing_line = isPiercingLine()
basic_hanging_man = isHangingMan()
basic_pin_bar = isPinBar()
basic_marubozu = isMarubozu()
basic_spinning_top = isSpinningTop()
basic_inside_bar = isInsideBar()
basic_outside_bar = isOutsideBar()

// Final pattern detection with optional volume confirmation
doji_pattern = use_volume_confirmation ? (basic_doji and continuation_volume_confirm) : basic_doji
hammer_pattern = use_volume_confirmation ? (basic_hammer and reversal_volume_confirm) : basic_hammer
shooting_star_pattern = use_volume_confirmation ? (basic_shooting_star and reversal_volume_confirm) : basic_shooting_star
bullish_engulfing_pattern = use_volume_confirmation ? (basic_bullish_engulfing and reversal_volume_confirm) : basic_bullish_engulfing
bearish_engulfing_pattern = use_volume_confirmation ? (basic_bearish_engulfing and reversal_volume_confirm) : basic_bearish_engulfing
piercing_line_pattern = use_volume_confirmation ? (basic_piercing_line and reversal_volume_confirm) : basic_piercing_line
hanging_man_pattern = use_volume_confirmation ? (basic_hanging_man and reversal_volume_confirm) : basic_hanging_man
pin_bar_pattern = use_volume_confirmation ? (basic_pin_bar and reversal_volume_confirm) : basic_pin_bar
marubozu_pattern = use_volume_confirmation ? (basic_marubozu and (reversal_volume_confirm or continuation_volume_confirm)) : basic_marubozu
spinning_top_pattern = use_volume_confirmation ? (basic_spinning_top and continuation_volume_confirm) : basic_spinning_top
inside_bar_pattern = basic_inside_bar  // Inside bars typically have lower volume, so no volume requirement
outside_bar_pattern = use_volume_confirmation ? (basic_outside_bar and breakout_volume_confirm) : basic_outside_bar

// Determine marubozu direction
bullish_marubozu = marubozu_pattern and close > open
bearish_marubozu = marubozu_pattern and close < open

// High reliability patterns (for special highlighting)
high_reliability_bullish = hammer_pattern or bullish_engulfing_pattern or piercing_line_pattern or bullish_marubozu
high_reliability_bearish = shooting_star_pattern or bearish_engulfing_pattern or hanging_man_pattern or bearish_marubozu
high_reliability_neutral = doji_pattern or spinning_top_pattern
consolidation_patterns = inside_bar_pattern
breakout_patterns = outside_bar_pattern

// ==================== FILTER TREND STRENGTH CALCULATION ====================
// Note: short_trend_strength and medium_trend_strength are defined later in the file
// We'll use those existing definitions and add our additional calculations here

// Combined filter score with weights (medium-term gets higher weight for stability)
short_weight = 0.4
medium_weight = 0.6

// ==================== ADVANCED CANDLE COLORING SYSTEM ====================

// Base colors for different market conditions
color strong_bull_color = color.new(color.lime, 0)      // Very strong bullish
color medium_bull_color = color.new(color.green, 20)    // Medium bullish  
color weak_bull_color = color.new(color.teal, 40)       // Weak bullish
color neutral_color = color.new(color.gray, 30)         // Neutral/Mixed
color weak_bear_color = color.new(color.orange, 40)     // Weak bearish
color medium_bear_color = color.new(color.red, 20)      // Medium bearish
color strong_bear_color = color.new(color.maroon, 0)    // Very strong bearish

// Special pattern colors
color doji_color = color.new(color.yellow, 10)          // Doji patterns
color hammer_color = color.new(color.purple, 10)        // Hammer patterns (TÍM = TĂNG)
color shooting_star_color = color.new(color.blue, 10)   // Shooting star patterns (XANH DƯƠNG = GIẢM)
color engulfing_bull_color = color.new(color.fuchsia, 0) // Bullish engulfing (TÍM ĐẬM = TĂNG MẠNH)
color engulfing_bear_color = color.new(color.aqua, 0)   // Bearish engulfing (XANH NƯỚC BIỂN = GIẢM MẠNH)
color marubozu_bull_color = color.new(color.lime, 0)    // Bullish marubozu
color marubozu_bear_color = color.new(color.red, 0)     // Bearish marubozu
color spinning_top_color = color.new(color.silver, 20)  // Spinning top
color inside_bar_color = color.new(color.navy, 30)      // Inside bar
color outside_bar_color = color.new(color.white, 10)    // Outside bar

// ==================== TREND STRENGTH CALCULATIONS ====================
// Function to calculate trend strength based on price position relative to filter + distance
// IMPORTANT: This now considers trend direction for proper support/resistance logic
calcTrendStrength(price, filter_line, upper_band, lower_band) =>
    // Determine if the filter/cloud is trending up or down - MATCH VISUAL ARROWS LOGIC
    cloud_trend_up = ta.rising(filter_line, 4)
    
    // Calculate basic relative position within bands
    band_range = upper_band - lower_band
    if band_range == 0
        0.0
    else
        // Calculate relative position: -1 (at lower band) to +1 (at upper band)
        relative_pos = (price - lower_band) / band_range * 2 - 1
        clamped_pos = math.max(-1, math.min(1, relative_pos))
        
        // Apply trend direction logic:
        // UPTREND: Cloud = Support → Above cloud = Bullish, Below = Bearish
        // DOWNTREND: Cloud = Resistance → Above cloud = Bearish (overextended), Below = Bullish (oversold)
        if cloud_trend_up
            // UPTREND: Normal logic - above cloud is bullish
            clamped_pos
        else
            // DOWNTREND: Inverted logic - above cloud is bearish (hitting resistance)
            -clamped_pos

// For timeframes < 1H, get ratio data from 1H timeframe
use_1h_data = not (is60min or is240min or isday or isweek or ismonth)

// ==================== BAND CALCULATIONS ====================
// Select the appropriate filter for distance bands
// For small timeframes: use 1H data
// For large timeframes: use mediumTermFilter (which shows higher timeframe shortTerm)
selected_filter = use_1h_filter ? shortTF1H : mediumTermFilter

// Calculate volatility for bands
volatility_period = 100
volatility = ta.sma(high - low, volatility_period)

// Use the consolidated medium_distance from above
lower_trend_band = selected_filter - medium_distance
upper_trend_band = selected_filter + medium_distance

// Short-term bands
short_upper_band = shortTermFilter + short_distance
short_lower_band = shortTermFilter - short_distance

// Pre-calculate 1H data (outside conditional structures)
short_trend_strength_1h = request.security(syminfo.tickerid, "60", calcTrendStrength(close, shortTermFilter, short_upper_band, short_lower_band), lookahead=barmerge.lookahead_off)
medium_trend_strength_1h = request.security(syminfo.tickerid, "60", calcTrendStrength(close, selected_filter, upper_trend_band, lower_trend_band), lookahead=barmerge.lookahead_off)
fex_1h = request.security(syminfo.tickerid, "60", fex_all, lookahead=barmerge.lookahead_off)

// Calculate current timeframe data
short_trend_strength_current = calcTrendStrength(close, shortTermFilter, short_upper_band, short_lower_band)
medium_trend_strength_current = calcTrendStrength(close, selected_filter, upper_trend_band, lower_trend_band)
fex_current = fex_all

// Select appropriate data based on timeframe
short_trend_strength = use_1h_data ? short_trend_strength_1h : short_trend_strength_current
medium_trend_strength = use_1h_data ? medium_trend_strength_1h : medium_trend_strength_current

// ==================== ADDITIONAL FILTER CALCULATIONS FOR CANDLE COLORING ====================
// Normalize trend strengths (0-1 scale) for candle coloring
short_strength_normalized = math.min(math.abs(short_trend_strength) / 5.0, 1.0)  // 5% = max strength
medium_strength_normalized = math.min(math.abs(medium_trend_strength) / 3.0, 1.0)  // 3% = max strength

// Determine filter directions
short_bullish = close > shortTermFilter
medium_bullish = close > mediumTermFilter
short_bearish = close < shortTermFilter
medium_bearish = close < mediumTermFilter

// Combined trend direction
combined_bullish = short_bullish and medium_bullish
combined_bearish = short_bearish and medium_bearish
mixed_trend = (short_bullish and medium_bearish) or (short_bearish and medium_bullish)

// Calculate combined strength using weights defined earlier
combined_strength = (short_strength_normalized * short_weight) + (medium_strength_normalized * medium_weight)

// ==================== ENHANCED CANDLE COLOR SYSTEM ====================
// Màu nến được xác định theo thứ tự ưu tiên:
// 🟢 TĂNG (Bullish): Xanh lá các tông - từ nhạt đến đậm
//    - strong_bull_color (lime): Tăng mạnh
//    - medium_bull_color (green): Tăng vừa  
//    - weak_bull_color (teal): Tăng yếu
// 🔴 GIẢM (Bearish): Đỏ/Cam các tông - từ nhạt đến đậm
//    - strong_bear_color (maroon): Giảm mạnh
//    - medium_bear_color (red): Giảm vừa
//    - weak_bear_color (orange): Giảm yếu
// ⚪ TRUNG TÍNH (Neutral): Xám - không có xu hướng rõ ràng
//    - neutral_color (gray): Trung tính/hỗn hợp
// 🎨 ĐẶC BIỆT (Special Patterns): Màu riêng cho các mô hình đặc biệt
//    - Hammer, Engulfing, Marubozu, Doji, etc.

// Determine candle colors based on combined analysis using switch
getCandleColor() =>
    switch
        // Priority 1: Strongest patterns (Marubozu - very strong momentum)
        bullish_marubozu and combined_bullish => marubozu_bull_color
        bearish_marubozu and combined_bearish => marubozu_bear_color
        // Priority 2: High reliability reversal patterns
        bullish_engulfing_pattern and (combined_bullish or not combined_bearish) => engulfing_bull_color
        bearish_engulfing_pattern and (combined_bearish or not combined_bullish) => engulfing_bear_color
        piercing_line_pattern and (combined_bullish or not combined_bearish) => engulfing_bull_color  // Same color as bullish engulfing (similar pattern)
        hanging_man_pattern and (combined_bearish or not combined_bullish) => shooting_star_color  // Same color as shooting star (similar pattern)
        hammer_pattern and (combined_bullish or not combined_bearish) => hammer_color
        shooting_star_pattern and (combined_bearish or not combined_bullish) => shooting_star_color
        // Priority 3: Consolidation and breakout patterns
        outside_bar_pattern and combined_bullish => combined_strength > 0.5 ? strong_bull_color : outside_bar_color
        outside_bar_pattern and combined_bearish => combined_strength > 0.5 ? strong_bear_color : outside_bar_color
        outside_bar_pattern => outside_bar_color
        inside_bar_pattern => inside_bar_color  // Inside bar suggests consolidation
        // Priority 4: Neutral/indecision patterns
        spinning_top_pattern => spinning_top_color
        doji_pattern => doji_color
        // Priority 5: Combined filter analysis - Bullish (TĂNG)
        combined_bullish and combined_strength > 0.7 => strong_bull_color
        combined_bullish and combined_strength > 0.4 => medium_bull_color
        combined_bullish => weak_bull_color
        // Priority 5: Combined filter analysis - Bearish (GIẢM)
        combined_bearish and combined_strength > 0.7 => strong_bear_color
        combined_bearish and combined_strength > 0.4 => medium_bear_color
        combined_bearish => weak_bear_color
        // Priority 6: Mixed signals
        mixed_trend and short_bullish and medium_bearish and short_strength_normalized > medium_strength_normalized => weak_bull_color
        mixed_trend and short_bullish and medium_bearish => weak_bear_color
        mixed_trend and medium_strength_normalized > short_strength_normalized => weak_bear_color
        mixed_trend => weak_bull_color
        // Priority 7: Basic candle direction (TĂNG/GIẢM cơ bản)
        close > open and not mixed_trend => medium_bull_color  // Nến tăng cơ bản
        close < open and not mixed_trend => medium_bear_color  // Nến giảm cơ bản
        // Default: Neutral (TRUNG TÍNH)
        => neutral_color

// Get the final candle color
// Kết quả: Mỗi nến sẽ có màu phù hợp với xu hướng và mô hình
// - Nến TĂNG: Các tông xanh lá (lime, green, teal)
// - Nến GIẢM: Các tông đỏ/cam (maroon, red, orange)  
// - Nến TRUNG TÍNH: Màu xám (gray)
// - Mô hình ĐẶC BIỆT: Màu riêng (purple, blue, yellow, etc.)
final_candle_color = getCandleColor()

// ==================== CANDLE DISPLAY IMPLEMENTATION ====================

// Display candles based on user selection
// Enhanced Candles: Always show plotcandle (main feature)
plotcandle(open, high, low, close, 
          color = final_candle_color,
          wickcolor = final_candle_color,
          bordercolor = final_candle_color,
          title = "Pattern Candles")

// Always apply barcolor to ensure colors show even when menu is closed
barcolor(final_candle_color, title = "Candle Colors")

// ==================== VOLUME INFO FUNCTION ====================
// Function to get volume status for labels using switch
getVolumeInfo() =>
    vol_ratio = volume / volume_sma_20
    vol_ratio_str = str.tostring(math.round(vol_ratio, 1)) + "x"
    switch
        very_high_volume => "🔥" + vol_ratio_str
        high_volume => "🚀" + vol_ratio_str
        above_avg_volume => "⚡" + vol_ratio_str
        => "💧" + vol_ratio_str

// ==================== PATTERN LABELS WITH ICONS ====================

// Show pattern labels based on user settings
if show_pattern_labels
    // High reliability patterns (always shown if labels are enabled)
    if bullish_engulfing_pattern and (combined_bullish or not combined_bearish)
        // Check if volume is exceptional (>2x average)
        vol_ratio = volume / volume_sma_20
        has_exceptional_volume = vol_ratio > 2.0
        
        pattern_text = has_exceptional_volume ? "⬆️\n" + getVolumeInfo() : "⬆️"
        label.new(bar_index, low - (high - low) * 0.1, pattern_text, 
                  color = color.new(color.fuchsia, 50), 
                  textcolor = color.white, 
                  style = label.style_label_up, 
                  size = size.small)

    if bearish_engulfing_pattern and (combined_bearish or not combined_bullish)
        // Check if volume is exceptional (>2x average)
        vol_ratio = volume / volume_sma_20
        has_exceptional_volume = vol_ratio > 2.0
        
        pattern_text = has_exceptional_volume ? "⬇️\n" + getVolumeInfo() : "⬇️"
        label.new(bar_index, high + (high - low) * 0.1, pattern_text, 
                  color = color.new(color.aqua, 50), 
                  textcolor = color.white, 
                  style = label.style_label_down, 
                  size = size.small)

    if piercing_line_pattern and (combined_bullish or not combined_bearish)
        // Check if volume is exceptional (>2x average)
        vol_ratio = volume / volume_sma_20
        has_exceptional_volume = vol_ratio > 2.0
        
        pattern_text = has_exceptional_volume ? "🌅\n" + getVolumeInfo() : "🌅"
        label.new(bar_index, low - (high - low) * 0.1, pattern_text, 
                  color = color.new(color.orange, 80), 
                  textcolor = color.white, 
                  style = label.style_label_up, 
                  size = size.small)

    // Use Final_Signal to determine pattern display (works for all timeframes)
    // For <= 1H: uses Final_1H_Signal (1H shortTermFilter)
    // For > 1H: uses Final_Alt_Signal (current timeframe shortTermFilter)
    is_bearish_signal = Final_Signal == "S" or Final_Signal == "WS"
    is_bullish_signal = Final_Signal == "B" or Final_Signal == "WB"

    if (hanging_man_pattern or hammer_pattern) and is_bearish_signal
        // Check if volume is exceptional (>2x average)
        vol_ratio = volume / volume_sma_20
        has_exceptional_volume = vol_ratio > 2.0
        
        pattern_text = has_exceptional_volume ? "🐻\n" + getVolumeInfo() : "🐻"
        label.new(bar_index, high + (high - low) * 0.1, pattern_text, 
                  color = color.new(color.red, 80), 
                  textcolor = color.white, 
                  style = label.style_label_down, 
                  size = size.small)

    if (hanging_man_pattern or hammer_pattern) and is_bullish_signal
        // Check if volume is exceptional (>2x average)
        vol_ratio = volume / volume_sma_20
        has_exceptional_volume = vol_ratio > 2.0
        
        pattern_text = has_exceptional_volume ? "🐂\n" + getVolumeInfo() : "🐂"
        label.new(bar_index, low - (high - low) * 0.1, pattern_text, 
                  color = color.new(color.purple, 80), 
                  textcolor = color.white, 
                  style = label.style_label_up, 
                  size = size.small)

    if shooting_star_pattern and (combined_bearish or not combined_bullish)
        // Check if volume is exceptional (>2x average)
        vol_ratio = volume / volume_sma_20
        has_exceptional_volume = vol_ratio > 2.0
        
        pattern_text = has_exceptional_volume ? "⭐\n" + getVolumeInfo() : "⭐"
        label.new(bar_index, high + (high - low) * 0.1, pattern_text, 
                  color = color.new(color.blue, 50), 
                  textcolor = color.white, 
                  style = label.style_label_down, 
                  size = size.small)

    if bullish_marubozu and combined_bullish
        // Check if volume is exceptional (>2x average)
        vol_ratio = volume / volume_sma_20
        has_exceptional_volume = vol_ratio > 2.0
        
        pattern_text = has_exceptional_volume ? "🚀\n" + getVolumeInfo() : "🚀"
        label.new(bar_index, low - (high - low) * 0.1, pattern_text, 
                  color = color.new(color.lime, 50), 
                  textcolor = color.black, 
                  style = label.style_label_up, 
                  size = size.small)

    if bearish_marubozu and combined_bearish
        // Check if volume is exceptional (>2x average)
        vol_ratio = volume / volume_sma_20
        has_exceptional_volume = vol_ratio > 2.0
        
        pattern_text = has_exceptional_volume ? "💥\n" + getVolumeInfo() : "💥"
        label.new(bar_index, high + (high - low) * 0.1, pattern_text, 
                  color = color.new(color.red, 70), 
                  textcolor = color.white, 
                  style = label.style_label_down, 
                  size = size.small)

    // Lower reliability patterns (only shown if high reliability only is disabled)
    if not show_high_reliability_only
        if doji_pattern
            // Check if volume is exceptional (>2x average)
            vol_ratio = volume / volume_sma_20
            has_exceptional_volume = vol_ratio > 2.0
            
            pattern_text = has_exceptional_volume ? "🎯\n" + getVolumeInfo() : "🎯"
            label.new(bar_index, high + (high - low) * 0.05, pattern_text, 
                      color = color.new(color.yellow, 50), 
                      textcolor = color.black, 
                      style = label.style_label_down, 
                      size = size.tiny)

        if spinning_top_pattern
            // Check if volume is exceptional (>2x average)
            vol_ratio = volume / volume_sma_20
            has_exceptional_volume = vol_ratio > 2.0
            
            pattern_text = has_exceptional_volume ? "🌀\n" + getVolumeInfo() : "🌀"
            label.new(bar_index, high + (high - low) * 0.05, pattern_text, 
                      color = color.new(color.silver, 50), 
                      textcolor = color.black, 
                      style = label.style_label_down, 
                      size = size.tiny)

        if outside_bar_pattern and (combined_bullish or combined_bearish)
            // Check if volume is exceptional (>2x average)
            vol_ratio = volume / volume_sma_20
            has_exceptional_volume = vol_ratio > 2.0
            
            pattern_text = has_exceptional_volume ? "📦\n" + getVolumeInfo() : "📦"
            label.new(bar_index, high + (high - low) * 0.05, pattern_text, 
                      color = color.new(color.white, 50), 
                      textcolor = color.black, 
                      style = label.style_label_down, 
                      size = size.tiny)

        if inside_bar_pattern
            // Inside bars typically have lower volume, so show icon only
            label.new(bar_index, high + (high - low) * 0.05, "📊", 
                      color = color.new(color.navy, 50), 
                      textcolor = color.white, 
                      style = label.style_label_down, 
                      size = size.tiny)

// Note: Candle coloring is now handled by the unified candle display system above
// The old separate barcolor implementation has been integrated into the main candle display logic

// Display trend state on every candle - Updated to use timeframe-based signals
// Reduced plots to stay under 64 limit - use separate plots with conditions

// ShortTermFilter signals - tiny triangles (CORRECTED: Now uses current timeframe's shortTermFilter for consistency)
plotshape(final_buySignal, title="Short Buy Signal", location=location.belowbar, style=shape.triangleup, color=color.lime, size=size.tiny)
plotshape(final_weakBuySignal, title="Short Weak Buy", location=location.belowbar, style=shape.triangleup, color=color.aqua, size=size.tiny)
plotshape(final_sellSignal, title="Short Sell Signal", location=location.abovebar, style=shape.triangledown, color=color.red, size=size.tiny)
plotshape(final_weakSellSignal, title="Short Weak Sell", location=location.abovebar, style=shape.triangledown, color=color.orange, size=size.tiny)

// MediumTermFilter signals - small triangles (direct from higher timeframes for consistency)
// Daily shows Weekly signals, 4H shows Daily signals, etc. - ensures status matches source timeframe
plotshape(final_mediumBuySignal, title="Medium Buy Signal", location=location.belowbar, style=shape.triangleup, color=color.lime, size=size.small)
plotshape(final_mediumWeakBuySignal, title="Medium Weak Buy", location=location.belowbar, style=shape.triangleup, color=color.aqua, size=size.small)
plotshape(final_mediumSellSignal, title="Medium Sell Signal", location=location.abovebar, style=shape.triangledown, color=color.red, size=size.small)
plotshape(final_mediumWeakSellSignal, title="Medium Weak Sell", location=location.abovebar, style=shape.triangledown, color=color.orange, size=size.small)

// ==================== FINAL SIGNAL DEBUG TABLE ====================

// Plot the medium-term Gaussian Filter line (enhanced logic based on timeframe)
// Removed duplicates - using optimized versions below

// Plot the long-term Gaussian Filter line (only for timeframes >= 4H) - COMMENTED to reduce plot count
// plot(series = (isweek or ismonth) ? longTermFilter : na, 
//      color = longTermColor, 
//      title = "Long-term Gaussian Filter",
//      linewidth = 3)

// ==================== VOLATILITY GAUSSIAN BANDS VISUALIZATION ====================
// Enhanced visualization for shortTermFilter and mediumTermFilter based on timeframe

// Create a function to calculate trend state that can be requested
calcShortTrendState() =>
    var bool trend_state = na
    upper_band = shortTermFilter + short_distance
    lower_band = shortTermFilter - short_distance
    if ta.crossover(close, upper_band)
        trend_state := true
    if ta.crossunder(close, lower_band)
        trend_state := false
    trend_state

// Helper function to get shortTerm cloud data for specific timeframe using tuple optimization
get_short_cloud_data(tf) =>
    request.security(syminfo.tickerid, tf, [shortTermFilter, short_distance, shortTermFilter - short_distance, shortTermFilter + short_distance], lookahead=barmerge.lookahead_off)

// Request shortTerm cloud data from higher timeframes to ensure consistency using optimized tuple approach
// Note: Tuple order is [shortTermFilter, short_distance, lower_band, upper_band]
// For timeframes < 1H: request 1H data
[shortTerm_1H, short_distance_1H, short_lower_band_1H, short_upper_band_1H] = get_short_cloud_data("60")

// For 1H: request 4H data
[shortTerm_4H, short_distance_4H, short_lower_band_4H, short_upper_band_4H] = get_short_cloud_data("240")

// For 4H: request D data
[shortTerm_D, short_distance_D, short_lower_band_D, short_upper_band_D] = get_short_cloud_data("D")

// Helper function to get trend state for specific timeframe
get_trend_state(tf) =>
    request.security(syminfo.tickerid, tf, calcShortTrendState(), lookahead=barmerge.lookahead_off)

// Request trend state from higher timeframes using helper function
medium_trend_state_1H = get_trend_state("60")
medium_trend_state_4H = get_trend_state("240")
medium_trend_state_D = get_trend_state("D")

// Helper function to get CONFIRMED trend state for specific timeframe
get_confirmed_trend_state(tf) =>
    request.security(syminfo.tickerid, tf, calcShortTrendState(), lookahead=barmerge.lookahead_on)

// CONFIRMED trend states - only change when higher timeframe bar is closed
medium_trend_state_4H_confirmed = get_confirmed_trend_state("240")
medium_trend_state_D_confirmed = get_confirmed_trend_state("D")
medium_trend_state_W_confirmed = get_confirmed_trend_state("W")
medium_trend_state_M_confirmed = get_confirmed_trend_state("M")

// Determine which filter to use based on timeframe
// use_1h_filter already defined above in consolidated distance section
use_short_filter = is30min or is60min or is240min or isday or isweek or ismonth  // Large timeframes use shortTermFilter

// Determine trend direction - should match mediumTerm cloud logic
var bool gaussian_trend = na

if use_1h_filter  // Timeframes < 1H (1min, 3min, 5min, 15min)
    gaussian_trend := medium_trend_state_1H
else if is60min
    gaussian_trend := medium_trend_state_4H
else if is240min
    gaussian_trend := medium_trend_state_D
else
    if ta.crossover(close, upper_trend_band)
        gaussian_trend := true
    if ta.crossunder(close, lower_trend_band)
        gaussian_trend := false

// Calculate trend line based on trend direction - should match mediumTerm cloud
trend_line_gaussian = if use_1h_filter  // Timeframes < 1H
    gaussian_trend ? short_lower_band_1H : short_upper_band_1H
else if is60min
    gaussian_trend ? short_lower_band_4H : short_upper_band_4H
else if is240min
    gaussian_trend ? short_lower_band_D : short_upper_band_D
else
    gaussian_trend ? lower_trend_band : upper_trend_band

// Plot the main filter line with dynamic color
// For timeframes < 1H, use 1H trend colors; for others use short-term colors
filter_color = use_1h_filter ? (shortTrend1H ? shortUpColor : shortDnColor) : shortTermColor

// Optimized plots - only keep essential lines
// Medium-term filter for timeframes < 1H only (as requested)
plot((is1min or is3min or is5min or is15min or is30min) ? mediumTermFilter : na, 
     color = mediumTermColor, linewidth=1, title="Medium-term Filter (< 1H)")

// p_filter = plot(na)  // Dummy plot for fill reference - REMOVED to reduce plot count

// ==================== SHORTTERM FILTER CLOUD ====================
show_bands = is30min or is60min or is240min or isday or isweek or ismonth

// shortTerm cloud data already requested above

// Select appropriate shortTerm data based on timeframe
// ShortTerm cloud should show current timeframe data
display_shortTerm_filter = shortTermFilter
display_short_upper_band = short_upper_band
display_short_lower_band = short_lower_band

// Determine shortTerm trend direction using the selected data
var bool short_gaussian_trend = na
if ta.crossover(close, display_short_upper_band)
    short_gaussian_trend := true
if ta.crossunder(close, display_short_lower_band)
    short_gaussian_trend := false

// Calculate shortTerm trend line
short_trend_line = short_gaussian_trend ? display_short_lower_band : display_short_upper_band

// Plot shortTerm cloud - hide for timeframes < 1H (but show for 1H and above)
show_short_cloud = show_bands and not use_1h_filter  // Show for timeframes >= 1H (exclude small timeframes < 1H)
p_short_filter_ref = plot(show_short_cloud ? display_shortTerm_filter : na, color = na, linewidth=1, title="Short Filter Reference")
p_short_trend_line = plot(show_short_cloud ? short_trend_line : na, 
                         color = color.new(#ff9800, 70), 
                         linewidth=1, 
                         title="Short Distance Trend Line")

// Fill shortTerm half cloud - hide for timeframes < 1H, reduced transparency for small timeframes
short_cloud_transparency = use_1h_filter ? 60 : 85  // Less transparent for small timeframes (1m,3m,5m,15m)
fill(p_short_filter_ref, p_short_trend_line, 
     color = show_short_cloud ? (short_gaussian_trend ? color.new(#00bcd4, short_cloud_transparency) : color.new(#ff9800, short_cloud_transparency)) : na, 
     title="Short Distance Half Cloud")

// ==================== MEDIUMTERM FILTER CLOUD ====================
// For consistency, mediumTerm cloud should use the same logic as shortTerm cloud from higher timeframe
// For 1H: mediumTerm should match 4H shortTerm cloud exactly
// For 4H: mediumTerm should match D shortTerm cloud exactly

// Request mediumTerm cloud data to match shortTerm from higher timeframes
mediumTerm_filter_display = if use_1h_filter
    shortTerm_1H  // Timeframes < 1H use 1H data
else if is60min
    shortTerm_4H  // 1H uses 4H data
else if is240min
    shortTerm_D   // 4H uses D data
else
    selected_filter

mediumTerm_upper_band = if use_1h_filter
    short_upper_band_1H  // Timeframes < 1H use 1H data
else if is60min
    short_upper_band_4H  // 1H uses 4H data
else if is240min
    short_upper_band_D   // 4H uses D data
else
    upper_trend_band

mediumTerm_lower_band = if use_1h_filter
    short_lower_band_1H  // Timeframes < 1H use 1H data
else if is60min
    short_lower_band_4H  // 1H uses 4H data
else if is240min
    short_lower_band_D   // 4H uses D data
else
    lower_trend_band

// Request complete trend state from higher timeframes to ensure exact matching
// Function and requests already defined above

// Use the appropriate trend state based on timeframe
var bool medium_gaussian_trend = na

if use_1h_filter  // Timeframes < 1H (1min, 3min, 5min, 15min)
    medium_gaussian_trend := medium_trend_state_4H_confirmed  // Small timeframes use CONFIRMED 4H trend (only changes when 4H bar closes)
else if is60min
    medium_gaussian_trend := medium_trend_state_4H_confirmed  // 1H uses CONFIRMED 4H trend (only changes when 4H bar closes)
else if is240min
    medium_gaussian_trend := medium_trend_state_D_confirmed   // 4H uses CONFIRMED Daily trend (only changes when Daily bar closes)
else if isday
    medium_gaussian_trend := medium_trend_state_W_confirmed   // Daily uses CONFIRMED Weekly trend (only changes when Weekly bar closes)
else if isweek
    medium_gaussian_trend := medium_trend_state_M_confirmed   // Weekly uses CONFIRMED Monthly trend (only changes when Monthly bar closes)
else
    if ta.crossover(close, mediumTerm_upper_band)
        medium_gaussian_trend := true
    if ta.crossunder(close, mediumTerm_lower_band)
        medium_gaussian_trend := false

// Calculate mediumTerm trend line using shortTerm logic
medium_trend_line = medium_gaussian_trend ? mediumTerm_lower_band : mediumTerm_upper_band

// Plot filter line for fill reference
p_filter_ref = plot(show_bands ? mediumTerm_filter_display : na, color = na, linewidth=1, title="Medium Filter Reference")

// Plot trend line (upper or lower band based on trend direction)
p_trend_line = plot(show_bands ? medium_trend_line : na, 
                   color = color.new(color.gray, 70), 
                   linewidth=1, 
                   title="Medium Distance Trend Line")

// Fill between filter and trend line to create half cloud - reduced transparency for small timeframes
medium_cloud_transparency = use_1h_filter ? 60 : 85  // Less transparent for small timeframes (1m,3m,5m,15m)
fill(p_filter_ref, p_trend_line, 
     color = show_bands ? (medium_gaussian_trend ? color.new(color.green, medium_cloud_transparency) : color.new(color.red, medium_cloud_transparency)) : na, 
     title="Medium Distance Half Cloud")

// ==================== LONGTERM FILTER CLOUD ====================
// Long term cloud only for Monthly timeframe (using longTermFilter)
show_long_cloud = ismonth

// Calculate long term distance using centralized multiplier system
long_distance = ismonth ? volatility_m * multiplier_monthly : 0.0

// Long term bands
long_upper_band = longTermFilter + long_distance
long_lower_band = longTermFilter - long_distance

// Long term trend direction
long_gaussian_trend = longTermUp

// Select trend line based on direction
long_trend_line = long_gaussian_trend ? long_lower_band : long_upper_band

// Plot long term filter and trend line (only for Monthly)
p_long_filter_ref = plot(show_long_cloud ? longTermFilter : na, 
                         color = na, 
                         linewidth=1, 
                         title="Long Filter Reference")

p_long_trend_line = plot(show_long_cloud ? long_trend_line : na,
                         color = color.new(color.purple, 70), 
                         linewidth=2, 
                         title="Long Distance Trend Line")

// Fill long term half cloud (only for Monthly)
long_cloud_transparency = 80
fill(p_long_filter_ref, p_long_trend_line, 
     color = show_long_cloud ? (long_gaussian_trend ? color.new(color.blue, long_cloud_transparency) : color.new(color.maroon, long_cloud_transparency)) : na, 
     title="Long Distance Half Cloud")

// ==================== OVEREXTENDED PRICE DETECTION ====================
// Only apply for timeframes >= 1H (not for timeframes < 1H)
apply_overextended = is60min or is120min or is180min or is240min or isday or isweek or ismonth

// Calculate ATR for distance measurement


// ==================== FEX_ALL PENALTY SYSTEM ====================
// Track FEX_ALL penalty with proper escalation logic
var float fex_penalty = 0.0
var int fex_hit_count = 0
var bool fex_was_under_90 = false  // Track if FEX went below 90 (allows escalation)

// Progressive penalty/bonus variables (additional to base penalty)
var float fex_progressive_penalty = 0.0  // Additional penalty based on points above threshold
var float fex_progressive_bonus = 0.0    // Additional bonus based on points below threshold

// Current FEX conditions using dynamic thresholds
fex_over_1st = apply_overextended and fex_all > FEX_Penalty_Threshold_1st
fex_over_2nd = apply_overextended and fex_all > FEX_Penalty_Threshold_2nd  
fex_over_3rd = apply_overextended and fex_all > FEX_Penalty_Threshold_3rd
fex_under_reset_threshold = fex_all < FEX_Penalty_Reset
fex_under_complete_reset = fex_all < FEX_Penalty_Complete_Reset

// Complete reset logic: when FEX drops below complete reset level OR low < shortTermFilter
penalty_fex_reset_condition = fex_under_complete_reset or low < shortTermFilter
if penalty_fex_reset_condition
    fex_penalty := 0.0
    fex_hit_count := 0
    fex_was_under_90 := false

// Track when FEX goes below reset threshold (this allows next escalation)
if fex_under_reset_threshold
    fex_was_under_90 := true

// FEX penalty escalation logic using dynamic penalties
if fex_over_1st
    if fex_penalty == 0.0
        // First time: FEX > Threshold_1st
        fex_penalty := FEX_Penalty_1st
        fex_hit_count := 1
        fex_was_under_90 := false  // Reset the flag
    else if fex_penalty == FEX_Penalty_1st and fex_was_under_90 and fex_over_2nd
        // Second time: was 1st penalty, went <Reset_Threshold, now >Threshold_2nd
        fex_penalty := FEX_Penalty_2nd
        fex_hit_count := 2
        fex_was_under_90 := false  // Reset the flag
    else if fex_penalty == FEX_Penalty_2nd and fex_was_under_90 and fex_over_3rd
        // Third time: was 2nd penalty, went <Reset_Threshold, now >Threshold_3rd
        fex_penalty := FEX_Penalty_3rd
        fex_hit_count := 3
        fex_was_under_90 := false  // Reset the flag
    // IMPORTANT: If none of the escalation conditions are met, penalty persists
    // This ensures penalty is maintained when FEX remains above threshold

// ==================== PROGRESSIVE PENALTY CALCULATION ====================
// Calculate additional progressive penalty based on how far above threshold FEX is
// This is added to the base penalty (-30, -60, -90)
if fex_over_1st and fex_hit_count >= 1
    // Calculate progressive penalty for 1st level
    threshold_to_use = FEX_Penalty_Threshold_1st
    fex_progressive_penalty := calc_progressive_penalty(fex_all, threshold_to_use)
else if fex_over_2nd and fex_hit_count >= 2  
    // Calculate progressive penalty for 2nd level
    threshold_to_use = FEX_Penalty_Threshold_2nd
    fex_progressive_penalty := calc_progressive_penalty(fex_all, threshold_to_use)
else if fex_over_3rd and fex_hit_count >= 3
    // Calculate progressive penalty for 3rd level  
    threshold_to_use = FEX_Penalty_Threshold_3rd
    fex_progressive_penalty := calc_progressive_penalty(fex_all, threshold_to_use)
else
    fex_progressive_penalty := 0.0

// Reset progressive penalty when FEX drops below complete reset level OR low < shortTermFilter
if penalty_fex_reset_condition
    fex_progressive_penalty := 0.0

// Penalty persists until complete reset (FEX < Reset_Level)
// Escalation allowed when FEX goes below Threshold_Reset and then exceeds higher thresholds
// Progressive escalation: 1st -> 2nd -> 3rd thresholds with corresponding penalties

// ==================== FEX BONUS SYSTEM ====================
// FEX bonus variables (opposite of penalty system)
var float fex_bonus = 0.0
var int fex_bonus_hit_count = 0
var bool fex_was_above_reset = false  // Track if FEX went above reset (allows bonus escalation)

// Current FEX bonus conditions using dynamic thresholds
fex_under_1st = apply_overextended and fex_all < FEX_Bonus_Threshold_1st
fex_under_2nd = apply_overextended and fex_all < FEX_Bonus_Threshold_2nd  
fex_under_3rd = apply_overextended and fex_all < FEX_Bonus_Threshold_3rd
fex_over_bonus_reset = fex_all > FEX_Bonus_Reset
fex_over_bonus_complete_reset = fex_all > FEX_Bonus_Complete_Reset

// Complete bonus reset logic: when FEX rises above complete reset level OR high > shortTermFilter
bonus_reset_condition = fex_over_bonus_complete_reset or high > shortTermFilter
if bonus_reset_condition
    fex_bonus := 0.0
    fex_bonus_hit_count := 0
    fex_was_above_reset := false

// Track when FEX goes above bonus reset threshold (becomes less negative - allows next escalation)
if fex_over_bonus_reset
    fex_was_above_reset := true

// FEX bonus escalation logic using dynamic bonuses
if fex_under_1st
    if fex_bonus == 0.0
        // First time: FEX < Bonus_Threshold_1st
        fex_bonus := FEX_Bonus_1st
        fex_bonus_hit_count := 1
        fex_was_above_reset := false  // Reset the flag
    else if fex_bonus == FEX_Bonus_1st and fex_was_above_reset and fex_under_2nd
        // Second time: was 1st bonus, went >Bonus_Reset, now <Bonus_Threshold_2nd
        fex_bonus := FEX_Bonus_2nd
        fex_bonus_hit_count := 2
        fex_was_above_reset := false  // Reset the flag
    else if fex_bonus == FEX_Bonus_2nd and fex_was_above_reset and fex_under_3rd
        // Third time: was 2nd bonus, went >Bonus_Reset, now <Bonus_Threshold_3rd
        fex_bonus := FEX_Bonus_3rd
        fex_bonus_hit_count := 3
        fex_was_above_reset := false  // Reset the flag

// Bonus persists until complete reset (FEX > Bonus_Complete_Reset)
// Escalation allowed when FEX goes above Bonus_Reset and then drops below lower thresholds
// Progressive escalation: 1st -> 2nd -> 3rd bonus thresholds with corresponding bonuses

// ==================== PROGRESSIVE BONUS CALCULATION ====================
// Calculate additional progressive bonus based on how far below threshold FEX is
// This is added to the base bonus (+30, +60, +90)
if fex_under_1st and fex_bonus_hit_count >= 1
    // Calculate progressive bonus for 1st level
    threshold_to_use = FEX_Bonus_Threshold_1st
    fex_progressive_bonus := calc_progressive_bonus(fex_all, threshold_to_use)
else if fex_under_2nd and fex_bonus_hit_count >= 2  
    // Calculate progressive bonus for 2nd level
    threshold_to_use = FEX_Bonus_Threshold_2nd
    fex_progressive_bonus := calc_progressive_bonus(fex_all, threshold_to_use)
else if fex_under_3rd and fex_bonus_hit_count >= 3
    // Calculate progressive bonus for 3rd level  
    threshold_to_use = FEX_Bonus_Threshold_3rd
    fex_progressive_bonus := calc_progressive_bonus(fex_all, threshold_to_use)
else
    fex_progressive_bonus := 0.0

// Reset progressive bonus when FEX rises above complete reset level
if fex_over_bonus_complete_reset
    fex_progressive_bonus := 0.0

// ==================== UNIFIED X-Y PENALTY/BONUS SYSTEM ====================
// Single system handling both penalty (overextended) and bonus (oversold) with persistent escalation
var float xy_net_points = 0.0  // Negative = penalty, Positive = bonus
var int xy_penalty_level = 0   // 0=none, 1=1st, 2=2nd, 3=3rd penalty
var int xy_bonus_level = 0     // 0=none, 1=1st, 2=2nd, 3=3rd bonus
var bool xy_was_in_neutral = false  // Track neutral zone crossing for escalation

// Calculate current X and Y values (for timeframes >= 1H)
current_X = (is60min or is240min or isday or isweek or ismonth) ? (high - shortTermFilter) / atr_value : 0.0
current_Y = (is60min or is240min or isday or isweek or ismonth) ? (high - mediumTermFilter) / atr_value : 0.0
current_XY_sum = current_X + current_Y
current_YX_ratio = current_X != 0 ? current_Y / current_X : 0.0

// ==================== UNIFIED XY THRESHOLD FUNCTION ====================
// Function to get appropriate X/Y thresholds based on FEX direction
get_xy_thresholds() =>
    // When FEX > 0: use penalty thresholds (positive values for overextended)
    // When FEX < 0: use bonus thresholds (negative values for oversold)
    if fex_all >= 0
        // Use penalty thresholds (same as current X_Threshold, Y_Threshold)
        x_thresh = X_Threshold
        y_thresh = Y_Threshold
        [x_thresh, y_thresh]
    else
        // Use bonus thresholds (negative values)
        x_thresh = X_Bonus_Threshold
        y_thresh = Y_Bonus_Threshold
        [x_thresh, y_thresh]

// Get current thresholds based on FEX direction
[current_X_Threshold, current_Y_Threshold] = get_xy_thresholds()

// ==================== PENALTY SIDE THRESHOLDS ====================
// Penalty thresholds (when overextended - X+Y too high, indicates overvalued/less attractive)
X_Penalty_Threshold = X_Threshold  // Penalty X threshold
Y_Penalty_Threshold = Y_Threshold  // Penalty Y threshold
XY_Penalty_Threshold_1st = X_Penalty_Threshold + Y_Penalty_Threshold  // First penalty trigger
XY_Penalty_Threshold_2nd = XY_Penalty_Threshold_1st * 1.1  // Second penalty trigger (10% higher)
XY_Penalty_Threshold_3rd = XY_Penalty_Threshold_1st * 1.2  // Third penalty trigger (20% higher)
XY_Penalty_Reset = XY_Penalty_Threshold_1st * 0.8  // Must drop below this to allow escalation
XY_Penalty_Complete_Reset = XY_Penalty_Threshold_1st * 0.6  // Complete penalty reset

// Penalty amounts (negative values)
XY_Penalty_1st_Amount = -XY_Penalty_Amount      // -30 (example)
XY_Penalty_2nd_Amount = -XY_Penalty_Amount * 2  // -60
XY_Penalty_3rd_Amount = -XY_Penalty_Amount * 3  // -90

// ==================== BONUS SIDE THRESHOLDS ====================
// Bonus thresholds (when oversold - X+Y too low/negative, FEX < 0)
X_Bonus_Threshold_Used = X_Bonus_Threshold  // Bonus X threshold (negative)
Y_Bonus_Threshold_Used = Y_Bonus_Threshold  // Bonus Y threshold (negative)
XY_Bonus_Threshold_1st = X_Bonus_Threshold_Used + Y_Bonus_Threshold_Used  // First bonus trigger (negative sum)
XY_Bonus_Threshold_2nd = XY_Bonus_Threshold_1st * 1.1  // Second bonus trigger (10% lower/more negative)
XY_Bonus_Threshold_3rd = XY_Bonus_Threshold_1st * 1.2  // Third bonus trigger (20% lower/more negative)
XY_Bonus_Reset = XY_Bonus_Threshold_1st * 0.8  // Must rise above this to allow escalation
XY_Bonus_Complete_Reset = XY_Bonus_Threshold_1st * 0.6  // Complete bonus reset

// Bonus amounts (positive values)
XY_Bonus_1st_Amount = XY_Bonus_Amount      // +30 (example)
XY_Bonus_2nd_Amount = XY_Bonus_Amount * 2  // +60
XY_Bonus_3rd_Amount = XY_Bonus_Amount * 3  // +90

// ==================== ZONE DEFINITION ====================
// Define zones based on thresholds
in_penalty_zone = current_XY_sum > XY_Penalty_Threshold_1st  // Above penalty threshold = overextended
in_bonus_zone = current_XY_sum < XY_Bonus_Threshold_1st      // Below bonus threshold = oversold  
in_neutral_zone = not in_penalty_zone and not in_bonus_zone  // Between thresholds = neutral

// ==================== COMPLETE RESET CONDITIONS ====================
// Complete reset: enter opposite zone OR FEX extreme level
penalty_complete_reset = in_bonus_zone or fex_all < FEX_Penalty_Complete_Reset
bonus_complete_reset = in_penalty_zone or fex_all > FEX_Bonus_Complete_Reset

// Reset all penalty states
if penalty_complete_reset and xy_penalty_level > 0
    xy_net_points := 0.0
    xy_penalty_level := 0
    xy_was_in_neutral := false

// Reset all bonus states  
if bonus_complete_reset and xy_bonus_level > 0
    xy_net_points := 0.0
    xy_bonus_level := 0
    xy_was_in_neutral := false

// ==================== ENHANCED ATTRACTIVENESS-BASED PENALTY/BONUS SYSTEM ====================
if (is60min or is240min or isday or isweek or ismonth) and apply_overextended
    
    // ==================== OPTIMAL 3-PARAMETER ATTRACTIVENESS SYSTEM ====================
    // Score each parameter individually based on its position relative to peak/bottom thresholds
    
    // X Parameter Score (ShortTermFilter distance) - PROGRESSIVE PENALTY SYSTEM
    x_score = 0.0
    if current_X >= X_Threshold
        // Overextended territory - exponential penalty (less attractive)
        excess_ratio = (current_X - X_Threshold) / X_Threshold  // How much beyond threshold
        x_score := -1.0 * (1.0 + math.pow(excess_ratio, 1.5))  // Exponential penalty beyond threshold
    else if current_X <= X_Bonus_Threshold  
        // Oversold territory - exponential bonus (more attractive)
        oversold_ratio = (X_Bonus_Threshold - current_X) / math.abs(X_Bonus_Threshold)  // How much into oversold
        x_score := 1.0 * (1.0 + math.pow(oversold_ratio, 1.5))  // Exponential bonus beyond threshold
    else
        // Neutral zone - PROGRESSIVE PENALTY as approaching threshold
        total_range = X_Threshold - X_Bonus_Threshold
        position_in_range = current_X - X_Bonus_Threshold
        normalized_position = position_in_range / total_range  // 0 to 1 (0=bonus threshold, 1=penalty threshold)
        
        // Progressive penalty: slow at first, accelerating as approaching threshold
        // Using quadratic function: penalty increases dramatically near threshold
        if normalized_position <= 0.5
            // First half: mild penalty increase
            penalty_factor = math.pow(normalized_position * 2, 1.2)  // Slight acceleration
            x_score := 1.0 - penalty_factor  // +1 to 0 range
        else
            // Second half: dramatic penalty increase near threshold
            penalty_factor = math.pow((normalized_position - 0.5) * 2, 2.5)  // Dramatic acceleration
            x_score := -penalty_factor  // 0 to -1 range
    
    // Y Parameter Score (MediumTermFilter distance) - PROGRESSIVE PENALTY SYSTEM
    y_score = 0.0
    if current_Y >= Y_Threshold
        // Overextended territory - exponential penalty
        excess_ratio = (current_Y - Y_Threshold) / Y_Threshold
        y_score := -1.0 * (1.0 + math.pow(excess_ratio, 1.5))  // Exponential penalty beyond threshold
    else if current_Y <= Y_Bonus_Threshold
        // Oversold territory - exponential bonus  
        oversold_ratio = (Y_Bonus_Threshold - current_Y) / math.abs(Y_Bonus_Threshold)
        y_score := 1.0 * (1.0 + math.pow(oversold_ratio, 1.5))  // Exponential bonus beyond threshold
    else
        // Neutral zone - PROGRESSIVE PENALTY as approaching threshold
        total_range = Y_Threshold - Y_Bonus_Threshold
        position_in_range = current_Y - Y_Bonus_Threshold
        normalized_position = position_in_range / total_range  // 0 to 1
        
        // Progressive penalty: accelerating as approaching threshold
        if normalized_position <= 0.5
            // First half: mild penalty increase
            penalty_factor = math.pow(normalized_position * 2, 1.2)  // Slight acceleration
            y_score := 1.0 - penalty_factor  // +1 to 0 range
        else
            // Second half: dramatic penalty increase near threshold
            penalty_factor = math.pow((normalized_position - 0.5) * 2, 2.5)  // Dramatic acceleration
            y_score := -penalty_factor  // 0 to -1 range
    
    // FEX Parameter Score (-105 to +105 range) - PROGRESSIVE PENALTY SYSTEM
    fex_score = 0.0
    fex_penalty_threshold = 95.0  // Overbought level
    fex_bonus_threshold = -95.0   // Oversold level
    
    if fex_all >= fex_penalty_threshold
        // Overbought territory - exponential penalty (less attractive)
        excess_ratio = (fex_all - fex_penalty_threshold) / (105.0 - fex_penalty_threshold)
        fex_score := -1.0 * (1.0 + math.pow(excess_ratio, 1.5))  // Exponential penalty beyond threshold
    else if fex_all <= fex_bonus_threshold
        // Oversold territory - exponential bonus (more attractive)
        oversold_ratio = (fex_bonus_threshold - fex_all) / (105.0 - math.abs(fex_bonus_threshold))
        fex_score := 1.0 * (1.0 + math.pow(oversold_ratio, 1.5))  // Exponential bonus beyond threshold
    else
        // Neutral zone - PROGRESSIVE PENALTY as approaching threshold
        total_range = fex_penalty_threshold - fex_bonus_threshold  // 190
        position_in_range = fex_all - fex_bonus_threshold  // Position from bonus threshold
        normalized_position = position_in_range / total_range  // 0 to 1
        
        // Progressive penalty: accelerating as approaching overbought threshold
        if normalized_position <= 0.5
            // First half: mild penalty increase (FEX from -95 to 0)
            penalty_factor = math.pow(normalized_position * 2, 1.2)  // Slight acceleration
            fex_score := 1.0 - penalty_factor  // +1 to 0 range
        else
            // Second half: dramatic penalty increase near overbought (FEX from 0 to 95)
            penalty_factor = math.pow((normalized_position - 0.5) * 2, 2.0)  // Strong acceleration
            fex_score := -penalty_factor  // 0 to -1 range
    
    // ==================== WEIGHTED COMPOSITE ATTRACTIVENESS SCORE ====================
    // Weight the parameters based on importance and timeframe
    x_weight = 0.35      // Short-term filter weight
    y_weight = 0.35      // Medium-term filter weight  
    fex_weight = 0.30    // FEX momentum weight
    
    // Composite attractiveness score (theoretical range: -3 to +3)
    composite_attractiveness = (x_score * x_weight) + (y_score * y_weight) + (fex_score * fex_weight)
    
    // ==================== CONVERT TO PENALTY/BONUS POINTS ====================
    // Scale attractiveness to penalty/bonus points with smooth transitions
    // More attractive = positive points (bonus), Less attractive = negative points (penalty)
    
    if composite_attractiveness > 0.7
        // Very attractive - strong bonus
        xy_net_points := math.min(75.0, composite_attractiveness * 75.0)
        xy_bonus_level := 2
        xy_penalty_level := 0
        
    else if composite_attractiveness > 0.3
        // Moderately attractive - medium bonus
        xy_net_points := composite_attractiveness * 50.0
        xy_bonus_level := 1
        xy_penalty_level := 0
        
    else if composite_attractiveness > -0.3
        // Neutral - small adjustment
        xy_net_points := composite_attractiveness * 15.0
        xy_bonus_level := 0
        xy_penalty_level := 0
        
    else if composite_attractiveness > -0.7
        // Moderately unattractive - medium penalty
        xy_net_points := composite_attractiveness * 50.0  // Negative value
        xy_bonus_level := 0
        xy_penalty_level := 1
        
    else
        // Very unattractive - strong penalty
        xy_net_points := math.max(-75.0, composite_attractiveness * 75.0)  // Negative value
        xy_bonus_level := 0
        xy_penalty_level := 2
        
else
    // No penalty/bonus for timeframes < 1H or when overextended is disabled
    xy_net_points := 0.0
    xy_penalty_level := 0
    xy_bonus_level := 0

// ==================== LEGACY COMPATIBILITY ====================
// For backward compatibility with existing code
xy_penalty = xy_net_points < 0 ? xy_net_points : 0.0  // Extract penalty component
xy_bonus = xy_net_points > 0 ? xy_net_points : 0.0    // Extract bonus component

// ==================== UPDATE FINAL FEX CALCULATION ====================
// Update final penalty and bonus variables for FEX calculation
// Combine base penalty/bonus with progressive penalty/bonus
final_fex_penalty := fex_penalty + fex_bonus + fex_progressive_penalty + fex_progressive_bonus  // Combine all FEX penalties and bonuses
final_xy_penalty := xy_penalty  
final_xy_bonus := xy_bonus


// Calculate other overextended conditions
overextended_short = apply_overextended and (high - shortTermFilter) > (X_Threshold * atr_value)
overextended_medium = apply_overextended and (high - mediumTermFilter) > (Y_Threshold * atr_value)

// Count basic overextended signals (X, Y only - FEX handled separately)
basic_overextended_count = (overextended_short ? 1 : 0) + (overextended_medium ? 1 : 0)

// ==================== PERSISTENT PENALTY POINTS UPDATE ====================
// Reset all persistent penalty points when FEX_ALL < 80 OR low < shortTermFilter (complete reset condition)
penalty_reset_condition = fex_all < FEX_Penalty_Complete_Reset or low < shortTermFilter
if penalty_reset_condition
    persistent_basic_penalty_points := 0.0
    persistent_fex_penalty_points := 0.0
    persistent_xy_penalty_points := 0.0
    persistent_total_penalty_points := 0.0

// Update persistent penalty points based on escalation system
// Basic penalty points (current bar calculation)
current_basic_penalty = apply_overextended ? basic_overextended_count * 10.0 : 0.0

// FEX penalty points (use escalation system values + progressive penalty - persistent until reset)
// Include both base penalty and progressive penalty
total_fex_penalty = math.abs(fex_penalty) + math.abs(fex_progressive_penalty)
persistent_fex_penalty_points := total_fex_penalty

// FEX bonus points (use escalation system values + progressive bonus - persistent until reset)
// Include both base bonus and progressive bonus
total_fex_bonus = math.abs(fex_bonus) + math.abs(fex_progressive_bonus)
persistent_fex_bonus_points = total_fex_bonus

// XY penalty points (use unified system values - persistent until reset)  
persistent_xy_penalty_points := xy_net_points < 0 ? math.abs(xy_net_points) : 0.0

// XY bonus points (use unified system values - persistent until reset)
persistent_xy_bonus_points = xy_net_points > 0 ? xy_net_points : 0.0

// Basic penalty points (also make persistent - keep maximum encountered until reset)
if current_basic_penalty > persistent_basic_penalty_points
    persistent_basic_penalty_points := current_basic_penalty

// Calculate total persistent penalty points (penalties - bonuses)
// Bonuses reduce the total penalty points, and can go negative (net bonus)
net_fex_points = persistent_fex_penalty_points - persistent_fex_bonus_points
net_xy_points = persistent_xy_penalty_points - persistent_xy_bonus_points
persistent_total_penalty_points := persistent_basic_penalty_points + net_fex_points + net_xy_points

// Use persistent penalty points for calculations
basic_penalty_points = persistent_basic_penalty_points
fex_penalty_points = persistent_fex_penalty_points  
xy_penalty_points = persistent_xy_penalty_points
total_penalty_points = persistent_total_penalty_points

// DEBUG: Penalty tracking (will be moved after bullish calculation)

// Calculate X, Y ratios for display (real-time monitoring)
x_ratio = atr_value > 0 ? (high - shortTermFilter) / atr_value : 0.0
y_ratio = atr_value > 0 ? (high - mediumTermFilter) / atr_value : 0.0

// ==================== BULLISH/BEARISH RATIO ALGORITHM ====================
// Apply for all timeframes, but use 1H data for timeframes < 1H
apply_ratio = true  // Always calculate ratio

// Normalize FEX_ALL to -1 to +1 range (assuming -105 to +105 range)
fex_to_use = use_1h_data ? fex_1h : fex_current
fex_normalized = math.max(-1, math.min(1, fex_to_use / 105.0))

// Old dynamic weighting system removed - replaced with improved version below

// ==================== SUPPORT/RESISTANCE DYNAMIC WEIGHTING SYSTEM ====================
// Base equal weights (1/3 each when all components are neutral)
base_equal_weight = 1.0 / 3.0  // 33.33% each

// Calculate distances and directions
short_distance_pct = math.abs(close - shortTermFilter) / short_distance * 100  // % of X_Multiple
medium_distance_pct = math.abs(close - mediumTermFilter) / medium_distance * 100  // % of X_Multiple
fex_distance_pct = math.abs(fex_to_use) / 105.0 * 100  // % of max FEX range (±105)

// Determine price momentum and trend relationships
price_momentum = ta.change(close, 3)  // 3-bar price change for momentum
short_above_medium = shortTermFilter > mediumTermFilter
price_above_short = close > shortTermFilter
price_above_medium = close > mediumTermFilter

// Determine trend direction (cloud slope and overall direction) - MATCH VISUAL ARROWS LOGIC
short_trend_direction = ta.rising(shortTermFilter, 4)  // Match alt_shortTrendUp logic
medium_trend_direction = ta.rising(mediumTermFilter, 4)  // Match medium trend logic

// Determine market context based on TREND DIRECTION
short_context = switch
    // UPTREND: Cloud is SUPPORT
    short_trend_direction and not price_above_short and price_momentum < 0 => "support_test"      // Testing support (BUY opportunity)
    short_trend_direction and not price_above_short and price_momentum > 0 => "bounce_from_support" // Bouncing from support (BUY confirm)
    short_trend_direction and price_above_short and price_momentum > 0 => "breakout_up"          // Breaking above (strong BUY)
    short_trend_direction and price_above_short and price_momentum < 0 => "pullback_to_support"  // Pullback to support (BUY dip)
    
    // DOWNTREND: Cloud is RESISTANCE  
    not short_trend_direction and price_above_short and price_momentum > 0 => "resistance_test"   // Testing resistance (SELL opportunity)
    not short_trend_direction and price_above_short and price_momentum < 0 => "rejection_from_resistance" // Rejected from resistance (SELL confirm)
    not short_trend_direction and not price_above_short and price_momentum < 0 => "breakdown"     // Breaking below (strong SELL)
    not short_trend_direction and not price_above_short and price_momentum > 0 => "pullback_to_resistance" // Pullback to resistance (SELL rally)
    => "neutral"

medium_context = switch
    // UPTREND: Cloud is SUPPORT
    medium_trend_direction and not price_above_medium and price_momentum < 0 => "support_test"
    medium_trend_direction and not price_above_medium and price_momentum > 0 => "bounce_from_support"
    medium_trend_direction and price_above_medium and price_momentum > 0 => "breakout_up"
    medium_trend_direction and price_above_medium and price_momentum < 0 => "pullback_to_support"
    
    // DOWNTREND: Cloud is RESISTANCE
    not medium_trend_direction and price_above_medium and price_momentum > 0 => "resistance_test"
    not medium_trend_direction and price_above_medium and price_momentum < 0 => "rejection_from_resistance"
    not medium_trend_direction and not price_above_medium and price_momentum < 0 => "breakdown"
    not medium_trend_direction and not price_above_medium and price_momentum > 0 => "pullback_to_resistance"
    => "neutral"

// ==================== SHORT TERM WEIGHT CALCULATION ====================
calc_short_weight() =>
    if not use_dynamic_weighting
        base_equal_weight
    else
        dist_pct = short_distance_pct
        context = short_context
        
        // Base weight calculation with distance
        base_weight_adj = if dist_pct <= 70.0
            // Normal range: increase weight as distance increases
            progress = dist_pct / 70.0
            sin_factor = math.sin(progress * math.pi / 2)
            base_equal_weight + (0.15 * sin_factor)  // Max +15% at 70%
        else
            // Overextended: progressive penalty
            peak_weight = base_equal_weight + 0.15
            excess_pct = dist_pct - 70.0
            penalty_factor = math.min(excess_pct / 30.0, 1.0)
            penalty = 0.20 * penalty_factor
            math.max(peak_weight - penalty, 0.05)
        
        // Context-based adjustment for support/resistance logic
        context_adj = switch context
            // UPTREND contexts (BUY opportunities)
            "support_test" => 0.20          // Strong BUY - testing support
            "bounce_from_support" => 0.15   // BUY confirm - bouncing from support
            "breakout_up" => 0.10           // Moderate BUY - breaking above
            "pullback_to_support" => 0.12   // BUY dip - pullback to support
            
            // DOWNTREND contexts (SELL opportunities)  
            "resistance_test" => -0.20       // Strong SELL - testing resistance
            "rejection_from_resistance" => -0.15  // SELL confirm - rejected from resistance
            "breakdown" => -0.10             // Moderate SELL - breaking below
            "pullback_to_resistance" => -0.12    // SELL rally - pullback to resistance
            => 0.0  // neutral
        
        math.max(base_weight_adj + context_adj, 0.05)  // Minimum 5%

// ==================== MEDIUM TERM WEIGHT CALCULATION ====================
calc_medium_weight() =>
    if not use_dynamic_weighting
        base_equal_weight
    else
        dist_pct = medium_distance_pct
        context = medium_context
        
        // Base weight calculation with distance
        base_weight_adj = if dist_pct <= 70.0
            progress = dist_pct / 70.0
            sin_factor = math.sin(progress * math.pi / 2)
            base_equal_weight + (0.15 * sin_factor)
        else
            peak_weight = base_equal_weight + 0.15
            excess_pct = dist_pct - 70.0
            penalty_factor = math.min(excess_pct / 30.0, 1.0)
            penalty = 0.20 * penalty_factor
            math.max(peak_weight - penalty, 0.05)
        
        // Context-based adjustment
        context_adj = switch context
            // UPTREND contexts (BUY opportunities)
            "support_test" => 0.20          // Strong BUY - testing support
            "bounce_from_support" => 0.15   // BUY confirm - bouncing from support
            "breakout_up" => 0.10           // Moderate BUY - breaking above
            "pullback_to_support" => 0.12   // BUY dip - pullback to support
            
            // DOWNTREND contexts (SELL opportunities)
            "resistance_test" => -0.20       // Strong SELL - testing resistance
            "rejection_from_resistance" => -0.15  // SELL confirm - rejected from resistance
            "breakdown" => -0.10             // Moderate SELL - breaking below
            "pullback_to_resistance" => -0.12    // SELL rally - pullback to resistance
            => 0.0
        
        math.max(base_weight_adj + context_adj, 0.05)

// ==================== FEX WEIGHT CALCULATION ====================
calc_fex_weight() =>
    if not use_dynamic_weighting
        base_equal_weight
    else
        dist_pct = fex_distance_pct
        fex_value = fex_to_use
        
        // Determine FEX context (support/resistance for FEX levels)
        fex_context = switch
            fex_value > 85.0 => "extreme_overbought"   // Strong SELL signal
            fex_value > 70.0 => "overbought"           // Moderate SELL signal
            fex_value < -85.0 => "extreme_oversold"    // Strong BUY signal
            fex_value < -70.0 => "oversold"            // Moderate BUY signal
            => "neutral"
        
        // Base weight calculation
        base_weight_adj = if dist_pct <= 80.0
            // Normal range: boost when moderate FEX
            base_equal_weight + (0.10 * (1.0 - dist_pct / 80.0))
        else
            // Progressive penalty when extreme
            excess_pct = dist_pct - 80.0
            penalty_factor = math.min(excess_pct / 20.0, 1.0)
            penalty = 0.15 * penalty_factor
            math.max(base_equal_weight - penalty, 0.05)
        
        // Context-based adjustment for FEX support/resistance
        context_adj = switch fex_context
            "extreme_oversold" => 0.15    // Strong BUY signal - increase FEX weight
            "oversold" => 0.10            // Moderate BUY signal
            "extreme_overbought" => -0.15 // Strong SELL signal - decrease FEX weight
            "overbought" => -0.10         // Moderate SELL signal
            => 0.0  // neutral
        
        math.max(base_weight_adj + context_adj, 0.05)

// Calculate individual weights
weight_s = calc_short_weight()
weight_m = calc_medium_weight()
weight_f = calc_fex_weight()

// Normalize weights to ensure they sum to 1.0
total_weight = weight_s + weight_m + weight_f
weight_s := weight_s / total_weight
weight_m := weight_m / total_weight
weight_f := weight_f / total_weight

// ==================== ENHANCED COMPOSITE SCORE WITH ATTRACTIVENESS FACTOR ====================
// Calculate base composite score
base_composite_score = short_trend_strength * weight_s + medium_trend_strength * weight_m + fex_normalized * weight_f

// Calculate continuous attractiveness factor for all timeframes
// This provides smooth penalty/bonus based on price attractiveness
continuous_attractiveness_factor = 0.0

if apply_overextended
    // Use same 3-parameter scoring system as XY calculation for consistency
    
    // X Parameter Score for composite adjustment - PROGRESSIVE PENALTY
    x_score_composite = 0.0
    if current_X >= X_Threshold
        excess_ratio = (current_X - X_Threshold) / X_Threshold
        x_score_composite := -1.0 * (1.0 + math.pow(excess_ratio, 1.5))
    else if current_X <= X_Bonus_Threshold  
        oversold_ratio = (X_Bonus_Threshold - current_X) / math.abs(X_Bonus_Threshold)
        x_score_composite := 1.0 * (1.0 + math.pow(oversold_ratio, 1.5))
    else
        total_range = X_Threshold - X_Bonus_Threshold
        position_in_range = current_X - X_Bonus_Threshold
        normalized_position = position_in_range / total_range
        if normalized_position <= 0.5
            penalty_factor = math.pow(normalized_position * 2, 1.2)
            x_score_composite := 1.0 - penalty_factor
        else
            penalty_factor = math.pow((normalized_position - 0.5) * 2, 2.5)
            x_score_composite := -penalty_factor
    
    // Y Parameter Score for composite adjustment - PROGRESSIVE PENALTY
    y_score_composite = 0.0
    if current_Y >= Y_Threshold
        excess_ratio = (current_Y - Y_Threshold) / Y_Threshold
        y_score_composite := -1.0 * (1.0 + math.pow(excess_ratio, 1.5))
    else if current_Y <= Y_Bonus_Threshold
        oversold_ratio = (Y_Bonus_Threshold - current_Y) / math.abs(Y_Bonus_Threshold)
        y_score_composite := 1.0 * (1.0 + math.pow(oversold_ratio, 1.5))
    else
        total_range = Y_Threshold - Y_Bonus_Threshold
        position_in_range = current_Y - Y_Bonus_Threshold
        normalized_position = position_in_range / total_range
        if normalized_position <= 0.5
            penalty_factor = math.pow(normalized_position * 2, 1.2)
            y_score_composite := 1.0 - penalty_factor
        else
            penalty_factor = math.pow((normalized_position - 0.5) * 2, 2.5)
            y_score_composite := -penalty_factor
    
    // FEX Parameter Score for composite adjustment - PROGRESSIVE PENALTY
    fex_score_composite = 0.0
    if fex_to_use >= 95.0
        excess_ratio = (fex_to_use - 95.0) / (105.0 - 95.0)
        fex_score_composite := -1.0 * (1.0 + math.pow(excess_ratio, 1.5))
    else if fex_to_use <= -95.0
        oversold_ratio = (-95.0 - fex_to_use) / (105.0 - 95.0)
        fex_score_composite := 1.0 * (1.0 + math.pow(oversold_ratio, 1.5))
    else
        total_range = 95.0 - (-95.0)
        position_in_range = fex_to_use - (-95.0)
        normalized_position = position_in_range / total_range
        if normalized_position <= 0.5
            penalty_factor = math.pow(normalized_position * 2, 1.2)
            fex_score_composite := 1.0 - penalty_factor
        else
            penalty_factor = math.pow((normalized_position - 0.5) * 2, 2.0)
            fex_score_composite := -penalty_factor
    
    // Weighted composite attractiveness for bullish/bearish adjustment
    composite_attractiveness_factor = (x_score_composite * 0.35) + (y_score_composite * 0.35) + (fex_score_composite * 0.30)
    
    // Convert to adjustment factor (-0.2 to +0.2 range for smoother integration)
    continuous_attractiveness_factor := math.max(-0.2, math.min(0.2, composite_attractiveness_factor * 0.2))

// Apply attractiveness adjustment to composite score
attractiveness_adjusted_score = base_composite_score + continuous_attractiveness_factor

// Convert composite score to Bullish/Bearish percentages
// Use sigmoid-like transformation for smooth transition
bullish_raw = (attractiveness_adjusted_score + 1) / 2  // Convert from [-1,1] to [0,1]

// ==================== SUPPORT/RESISTANCE DYNAMIC LOGIC ====================
// Smooth distance-based adjustment instead of binary on/off
support_resistance_adjustment = 0.0

// Calculate distances to clouds (normalized by ATR for consistency across timeframes)
distance_to_short = math.abs(close - shortTermFilter) / atr_value
distance_to_medium = math.abs(close - mediumTermFilter) / atr_value

// Create smooth decay functions (max effect at cloud, decay to 0 at 2 ATR distance)
// Using exponential decay: effect = max_effect * exp(-distance^2)
short_proximity_factor = math.exp(-math.pow(distance_to_short, 2))  // 1.0 at cloud, ~0.14 at 1 ATR, ~0.02 at 1.5 ATR
medium_proximity_factor = math.exp(-math.pow(distance_to_medium, 2))

// Get signal context - use the actual displayed signals for consistency
short_is_sell = Final_Signal == "S" or Final_Signal == "WS"
short_is_buy = Final_Signal == "B" or Final_Signal == "WB"
medium_is_sell = Final_Medium_Signal == "S" or Final_Medium_Signal == "WS"
medium_is_buy = Final_Medium_Signal == "B" or Final_Medium_Signal == "WB"

// SCENARIO 1: Short = SELL/WS + Medium = BUY/WB (Conflicting Signals)
if short_is_sell and medium_is_buy
    // Medium cloud effect (Support when price below) - STRONGER EFFECT
    if close <= mediumTermFilter
        support_resistance_adjustment := support_resistance_adjustment + (0.40 * medium_proximity_factor)  // Stronger Bullish boost
    
    // Short cloud effect (Resistance when price above) - STRONGER EFFECT
    if close >= shortTermFilter
        support_resistance_adjustment := support_resistance_adjustment - (0.40 * short_proximity_factor)  // Stronger Bearish boost

// SCENARIO 2: Short = BUY/WB + Medium = SELL/WS (Conflicting Signals)
else if short_is_buy and medium_is_sell
    // Medium cloud effect (Resistance when price above) - STRONGER EFFECT
    if close >= mediumTermFilter
        support_resistance_adjustment := support_resistance_adjustment - (0.40 * medium_proximity_factor)  // Stronger Bearish boost
    
    // Short cloud effect (Support when price below)
    if close <= shortTermFilter  
        support_resistance_adjustment := support_resistance_adjustment + (0.40 * short_proximity_factor)  // Stronger Bullish boost

// SCENARIO 3: Both Medium = SELL/WS + Short = SELL/WS (Strong Bearish Consensus)
else if medium_is_sell and short_is_sell
    // Both clouds act as resistance - use the stronger proximity effect
    max_resistance_effect = math.max(short_proximity_factor, medium_proximity_factor)
    support_resistance_adjustment := -0.50 * max_resistance_effect  // Strong Bearish boost (consensus)

// SCENARIO 4: Both Medium = BUY/WB + Short = BUY/WB (Strong Bullish Consensus)
else if medium_is_buy and short_is_buy
    // Both clouds act as support - use the stronger proximity effect  
    max_support_effect = math.max(short_proximity_factor, medium_proximity_factor)
    support_resistance_adjustment := 0.50 * max_support_effect  // Strong Bullish boost (consensus)

// Apply support/resistance adjustment
bullish_with_sr_adjustment = bullish_raw + support_resistance_adjustment
bullish_with_sr_adjustment := math.max(0.0, math.min(1.0, bullish_with_sr_adjustment))  // Keep within [0,1]

// ==================== ENHANCED PENALTY/BONUS APPLICATION ====================
// Apply penalty/bonus with improved logic to reflect attractiveness
penalty_multiplier = 1.2  // Slightly amplify penalty effect

// Enhanced penalty calculation with multiple factors
total_penalty_adjustment = 0.0

// 1. FEX penalty/bonus (based on overbought/oversold conditions)
fex_penalty_adjustment = (final_fex_penalty * penalty_multiplier / 100.0)
total_penalty_adjustment := total_penalty_adjustment + fex_penalty_adjustment

// 2. XY penalty/bonus (based on distance from attractive levels)  
xy_penalty_adjustment = (xy_net_points * penalty_multiplier / 100.0)
total_penalty_adjustment := total_penalty_adjustment + xy_penalty_adjustment

// 3. Basic overextended penalty (for additional factors)
basic_penalty_adjustment = (basic_penalty_points * penalty_multiplier / 100.0)
total_penalty_adjustment := total_penalty_adjustment - basic_penalty_adjustment

// Apply total penalty/bonus adjustment
// Negative adjustment = penalty (reduces bullish %), Positive adjustment = bonus (increases bullish %)
bullish_after_penalty = bullish_with_sr_adjustment + total_penalty_adjustment
bullish_after_penalty := math.max(0.0, math.min(1.0, bullish_after_penalty))  // Keep within [0,1] range

bearish_raw = 1 - bullish_after_penalty

// Direct percentage calculation without scaling
// Scaling was distorting penalty effects, so removed for clarity
bullish_ratio = bullish_after_penalty * 100  // Direct conversion to percentage
bearish_ratio = bearish_raw * 100

// ==================== SIMPLE DYNAMIC WEIGHTING TEST ====================
// Simple version to test if dynamic weighting works without compilation errors
simple_dynamic_weight_test() =>
    // Test if we can create a simple dynamic weight function
    base_short = 0.40
    base_medium = 0.35
    base_fex = 0.25
    
    // Simple adjustment based on trend strength
    short_adj = math.abs(short_trend_strength) > 0.7 ? -0.05 : 0.05
    medium_adj = math.abs(medium_trend_strength) > 0.7 ? -0.05 : 0.05
    
    // Adjust weights
    adj_short = base_short + short_adj
    adj_medium = base_medium + medium_adj
    adj_fex = base_fex - (short_adj + medium_adj)
    
    // Normalize
    total = adj_short + adj_medium + adj_fex
    [adj_short/total, adj_medium/total, adj_fex/total]

// Test the simple dynamic weighting (commented out for now)
// test_weights = simple_dynamic_weight_test()
// test_w_s = array.get(test_weights, 0)
// test_w_m = array.get(test_weights, 1) 
// test_w_f = array.get(test_weights, 2)

// Round to whole numbers (no decimal places)
bullish_percentage = math.round(bullish_ratio, 0)
bearish_percentage = math.round(bearish_ratio, 0)

// Calculate trend line color for cloud reference (no visible line)
trend_line_color = close > trend_line_gaussian ? longUpColor : longDnColor
p_trend = plot(trend_line_gaussian, color = na, linewidth=1, title="Trend Reference")

// Plot invisible reference line for main cloud fill (1H filter for small TF)
p_main_filter_ref = plot(use_1h_filter ? selected_filter : na, color = na, linewidth=1, title="Main Filter Reference")

// Fill main cloud between Gaussian Trend Line and 1H Filter (for small timeframes)
main_cloud_transparency = use_1h_filter ? 70 : na
main_cloud_color = use_1h_filter ? (gaussian_trend ? color.new(longUpColor, main_cloud_transparency) : color.new(longDnColor, main_cloud_transparency)) : na
fill(p_trend, p_main_filter_ref, 
     color = main_cloud_color, 
     title="Main Gaussian Cloud")

// ==================== 4H CLOUD FOR SMALL TIMEFRAMES ====================
// Plot 4H cloud for timeframes < 1H (similar to 1H cloud above)
p_4h_filter_ref = plot(use_1h_filter ? shortTerm_4H : na, color = na, linewidth=1, title="4H Filter Reference")

// Determine 4H trend direction
medium_trend_4h = medium_trend_state_4H
h4_trend_line = medium_trend_4h ? short_lower_band_4H : short_upper_band_4H

p_4h_trend_line = plot(use_1h_filter ? h4_trend_line : na, 
                      color = color.new(#9c27b0, 70), 
                      linewidth=1, 
                      title="4H Distance Trend Line")

// Fill 4H cloud between 4H Filter and 4H Distance Line (for small timeframes only)
h4_cloud_transparency = use_1h_filter ? 75 : na
h4_cloud_color = use_1h_filter ? (medium_trend_4h ? color.new(#9c27b0, h4_cloud_transparency) : color.new(#ff5722, h4_cloud_transparency)) : na
fill(p_4h_filter_ref, p_4h_trend_line, 
     color = h4_cloud_color, 
     title="4H Distance Cloud")

// Cloud status triangles removed - not needed

// Fill between trend line and filter line - COMMENTED due to p_filter removal
gaussian_trend_color = gaussian_trend ? color.new(longUpColor, 80) : color.new(longDnColor, 80)
// fill(p_trend, p_filter, color = gaussian_trend_color, title = "Gaussian Trend Fill")

// ==================== TREND STRENGTH TABLE ====================
// Calculate trend strength score based on price position relative to bands
price_position = (close - lower_trend_band) / (upper_trend_band - lower_trend_band)
trend_score = math.max(0, math.min(1, price_position))

// Simplified logic: show trend strength based on current gaussian_trend direction
// If BULLISH: show how strong the bullish trend is (0-100%)
// If BEARISH: show how strong the bearish trend is (0-100%)
trend_strength_percentage = gaussian_trend ? math.round(trend_score * 100) : math.round((1 - trend_score) * 100)

// Display combined trend strength as part of the main table
// This will be handled in the main table section below

// ==================== COMBINED INFO TABLE ====================
// Combined table with BULLISH/BEARISH and Bull/Bear ratios - 6 rows (added X,Y ratios + XY penalty)
if barstate.islast
    var table combined_info = table.new(position.top_right, 1, 6, bgcolor = color.new(color.gray, 90))
    
    // Clear existing content
    table.clear(combined_info, 0, 0)
    table.clear(combined_info, 0, 1)
    table.clear(combined_info, 0, 2)
    table.clear(combined_info, 0, 3)
    table.clear(combined_info, 0, 4)
    table.clear(combined_info, 0, 5)
    
    // Row 1: Show trend status based on Bull/Bear ratio (all timeframes)
    trend_status = bullish_percentage > bearish_percentage ? "⬆️🚀🔥" : "⬇️💥🩸"
    trend_is_bullish = bullish_percentage > bearish_percentage
        
    table.cell(combined_info, 0, 0, trend_status, 
              text_color = color.white, 
              bgcolor = trend_is_bullish ? color.new(longUpColor, 80) : color.new(longDnColor, 80),
              text_size = size.normal)
    
    // Row 2 & 3: Show Bullish/Bearish ratio (all timeframes)
    // For timeframes < 1H, this shows 1H data
    bullish_text = "🦬 " + str.tostring(bullish_percentage) + "%"
    bearish_text = "🐻 " + str.tostring(bearish_percentage) + "%"
    
    table.cell(combined_info, 0, 1, bullish_text,
              text_color = color.black,
              bgcolor = color.new(color.green, 60),
              text_size = size.normal)
    
    table.cell(combined_info, 0, 2, bearish_text,
              text_color = color.black,
              bgcolor = color.new(color.red, 60),
              text_size = size.normal)
    
    // Rows 4, 5, 6: X, Y, XY - Only show for timeframes >= 1H and when enabled by user
    if show_xy_info and (is60min or is240min or isday or isweek or ismonth)
        // Row 4: X Ratio (High - ShortTermFilter) / ATR
        // Show appropriate threshold based on current state
        x_threshold_display = x_ratio < 0 ? X_Bonus_Threshold : X_Threshold
        x_comparison_symbol = x_ratio < 0 ? "<" : ">"
        x_ratio_text = "X: " + str.tostring(math.round(x_ratio, 2)) + " (" + x_comparison_symbol + str.tostring(x_threshold_display) + ")"
        x_ratio_color = (x_ratio < 0 and x_ratio < X_Bonus_Threshold) ? color.new(color.green, 30) : (x_ratio > X_Threshold ? color.new(color.red, 30) : color.new(color.gray, 50))
        table.cell(combined_info, 0, 3, x_ratio_text,
                  text_color = color.white,
                  bgcolor = x_ratio_color,
                  text_size = size.small)
        
        // Row 5: Y Ratio (High - MediumTermFilter) / ATR  
        // Show appropriate threshold based on current state
        y_threshold_display = y_ratio < 0 ? Y_Bonus_Threshold : Y_Threshold
        y_comparison_symbol = y_ratio < 0 ? "<" : ">"
        y_ratio_text = "Y: " + str.tostring(math.round(y_ratio, 2)) + " (" + y_comparison_symbol + str.tostring(y_threshold_display) + ")"
        y_ratio_color = (y_ratio < 0 and y_ratio < Y_Bonus_Threshold) ? color.new(color.green, 30) : (y_ratio > Y_Threshold ? color.new(color.red, 30) : color.new(color.gray, 50))
        table.cell(combined_info, 0, 4, y_ratio_text,
                  text_color = color.white,
                  bgcolor = y_ratio_color,
                  text_size = size.small)
        
        // Row 6: Unified X-Y Penalty/Bonus Status
        if xy_net_points < 0.0
            // Show active X-Y penalty with level and breakdown
            xy_penalty_rounded = str.tostring(math.round(math.abs(xy_net_points), 1))
            current_x_str = str.tostring(math.round(current_X, 1))
            current_y_str = str.tostring(math.round(current_Y, 1))
            xy_penalty_text = "🎯 XY-" + xy_penalty_rounded + " L" + str.tostring(xy_penalty_level) + " X:" + current_x_str + " Y:" + current_y_str
            
            // Color intensity based on penalty level and severity
            penalty_intensity = math.min(50, math.abs(xy_net_points) * 2)  // Max 50% transparency
            xy_penalty_color = color.new(color.purple, 100 - penalty_intensity)
            
            table.cell(combined_info, 0, 5, xy_penalty_text,
                      text_color = color.white,
                      bgcolor = xy_penalty_color,
                      text_size = size.small)
        else if xy_net_points > 0.0
            // Show active X-Y bonus with level and breakdown
            xy_bonus_rounded = str.tostring(math.round(xy_net_points, 1))
            current_x_str = str.tostring(math.round(current_X, 1))
            current_y_str = str.tostring(math.round(current_Y, 1))
            xy_bonus_text = "🎁 XY+" + xy_bonus_rounded + " L" + str.tostring(xy_bonus_level) + " X:" + current_x_str + " Y:" + current_y_str
            
            // Color intensity based on bonus level and amount
            bonus_intensity = math.min(50, xy_net_points * 2)  // Max 50% transparency
            xy_bonus_color = color.new(color.green, 100 - bonus_intensity)
            
            table.cell(combined_info, 0, 5, xy_bonus_text,
                      text_color = color.white,
                      bgcolor = xy_bonus_color,
                      text_size = size.small)
        else
            // Show X-Y monitoring status (no penalty active)
            current_x_str = str.tostring(math.round(current_X, 1))
            current_y_str = str.tostring(math.round(current_Y, 1))
            x_threshold_str = str.tostring(X_Threshold)
            y_threshold_str = str.tostring(Y_Threshold)
            
            // Initialize xy_monitor_text variable
            xy_monitor_text = ""
            
            // Debug: Show appropriate thresholds and calculations
            current_xy_sum_str = str.tostring(math.round(current_XY_sum, 1))
            xy_penalty_thresh_1st_str = str.tostring(math.round(XY_Penalty_Threshold_1st, 1))
            xy_bonus_thresh_1st_str = str.tostring(math.round(XY_Bonus_Threshold_1st, 1))
            
            if in_bonus_zone
                // Show bonus thresholds when in bonus zone
                xy_monitor_text := "🔍 XY Sum: " + current_xy_sum_str + "/" + xy_bonus_thresh_1st_str + " (Bonus Zone)"
            else if in_penalty_zone
                // Show penalty thresholds when in penalty zone
                xy_monitor_text := "🔔 XY Sum: " + current_xy_sum_str + "/" + xy_penalty_thresh_1st_str + " (Penalty Zone)"
            else
                // Show composite attractiveness score using the new 3-parameter system
                // Recalculate for display (matching the actual calculation)
                x_score_display = current_X >= X_Threshold ? -1.0 * (1.0 + (current_X - X_Threshold) / X_Threshold) :
                                 current_X <= X_Bonus_Threshold ? 1.0 * (1.0 + (X_Bonus_Threshold - current_X) / math.abs(X_Bonus_Threshold)) :
                                 1.0 - (2.0 * ((current_X - X_Bonus_Threshold) / (X_Threshold - X_Bonus_Threshold)))
                
                y_score_display = current_Y >= Y_Threshold ? -1.0 * (1.0 + (current_Y - Y_Threshold) / Y_Threshold) :
                                 current_Y <= Y_Bonus_Threshold ? 1.0 * (1.0 + (Y_Bonus_Threshold - current_Y) / math.abs(Y_Bonus_Threshold)) :
                                 1.0 - (2.0 * ((current_Y - Y_Bonus_Threshold) / (Y_Threshold - Y_Bonus_Threshold)))
                
                fex_score_display = fex_all >= 95.0 ? -1.0 * (1.0 + (fex_all - 95.0) / 10.0) :
                                   fex_all <= -95.0 ? 1.0 * (1.0 + (-95.0 - fex_all) / 10.0) :
                                   1.0 - (2.0 * ((fex_all + 95.0) / 190.0))
                
                composite_attractiveness_display = (x_score_display * 0.35) + (y_score_display * 0.35) + (fex_score_display * 0.30)
                
                attractiveness_str = str.tostring(math.round(composite_attractiveness_display, 3))
                xy_points_str = str.tostring(math.round(xy_net_points, 1))
                xy_monitor_text := "🎯 Attract: " + attractiveness_str + " → XY: " + xy_points_str
            
            // Warning color if approaching thresholds
            approaching_x = current_X > (X_Threshold * 0.8)
            approaching_y = current_Y > (Y_Threshold * 0.8)
            xy_monitor_color = (approaching_x or approaching_y) ? color.new(color.orange, 50) : color.new(color.gray, 50)
            
            table.cell(combined_info, 0, 5, xy_monitor_text,
                      text_color = color.white,
                      bgcolor = xy_monitor_color,
                      text_size = size.small)

// ==================== PEAK/VALLEY TRACKING SYSTEM ====================
// Function to track peak/valley points and detect trend change signals
track_peak_valley_signals() =>
    // Variables to store peak/valley points
    var float peak_point_before = na
    var float peak_point_after = na
    var float valley_point_before = na
    var float valley_point_after = na
    
    // Variables to track current state
    var string current_phase = "neutral"  // "uptrend", "downtrend", "neutral"
    var bool was_above_cloud = false
    var bool was_below_cloud = false
    var string trend_change_signal = na
    var string previous_signal = na
    
    // Calculate cloud boundaries
    upper_cloud = shortTermFilter + short_distance
    lower_cloud = shortTermFilter - short_distance
    
    // Determine current position relative to cloud
    above_cloud = close > shortTermFilter
    below_cloud = close < shortTermFilter
    in_cloud = close <= upper_cloud and close >= lower_cloud
    
    // Current signal status
    is_bullish_signal = Final_Signal == "B" or Final_Signal == "WB"
    is_bearish_signal = Final_Signal == "S" or Final_Signal == "WS"
    is_neutral_signal = Final_Signal == "N"
    
    // === PHASE UPDATE BASED ON FINAL_SIGNAL ===
    // Phase changes only when Final_Signal changes
    if is_bullish_signal and current_phase != "uptrend"
        // Signal changed to bullish → start uptrend
        trend_change_signal := current_phase == "downtrend" ? "signal_to_buy" : "start_uptrend"
        current_phase := "uptrend"
        // Reset valley points and start tracking peak
        valley_point_before := na
        valley_point_after := na
        if above_cloud
            peak_point_before := high
            peak_point_after := na
            was_above_cloud := true
        else
            peak_point_before := na
            peak_point_after := na
            was_above_cloud := false
        was_below_cloud := false
        
    else if is_bearish_signal and current_phase != "downtrend"
        // Signal changed to bearish → start downtrend
        trend_change_signal := current_phase == "uptrend" ? "signal_to_sell" : "start_downtrend"
        current_phase := "downtrend"
        // Reset peak points and start tracking valley
        peak_point_before := na
        peak_point_after := na
        if below_cloud
            valley_point_before := low
            valley_point_after := na
            was_below_cloud := true
        else
            valley_point_before := na
            valley_point_after := na
            was_below_cloud := false
        was_above_cloud := false
        
    else if is_neutral_signal and current_phase != "neutral"
        // Signal changed to neutral
        trend_change_signal := "signal_to_neutral"
        current_phase := "neutral"
        was_above_cloud := false
        was_below_cloud := false
    
    // === PEAK/VALLEY TRACKING WITHIN CURRENT PHASE ===
    if current_phase == "uptrend"
        if above_cloud
            if was_above_cloud
                // Continue updating peak while above cloud
                if na(peak_point_after)
                    // Normal tracking of peak_before
                    peak_point_before := math.max(peak_point_before, high)
                else
                    // Update peak_after while above cloud
                    peak_point_after := math.max(peak_point_after, high)
                    
                    // Continuous comparison while updating peak_after
                    if peak_point_after > peak_point_before
                        trend_change_signal := "higher_peak_detected"
                        // New higher peak found - update before and reset after for new cycle
                        peak_point_before := peak_point_after
                        peak_point_after := na
            else
                // First time above cloud or returning from cloud
                if not na(peak_point_before) and na(peak_point_after)
                    // Returning from cloud - this is peak_after
                    peak_point_after := high
                    
                    // Compare peaks for failure swing (but don't reset immediately)
                    if peak_point_after < peak_point_before
                        trend_change_signal := "peak_failure_detected"
                    else if peak_point_after > peak_point_before
                        trend_change_signal := "higher_peak_detected"
                        // New higher peak found - update before and reset after for new cycle
                        peak_point_before := peak_point_after
                        peak_point_after := na
                else
                    // First peak in this uptrend
                    peak_point_before := high
                    peak_point_after := na
                was_above_cloud := true
        else
            // Price not above cloud - check if we need to finalize comparison
            if not na(peak_point_after)
                // Only peak failure needs confirmation (higher peak already processed)
                if peak_point_after < peak_point_before
                    // Confirm peak failure when returning to cloud
                    trend_change_signal := "peak_failure_confirmed"
                    // Reset after confirmation - ready for new cycle
                    peak_point_after := na
            was_above_cloud := false
            
    else if current_phase == "downtrend"
        if below_cloud
            if was_below_cloud
                // Continue updating valley while below cloud
                if na(valley_point_after)
                    // Normal tracking of valley_before
                    valley_point_before := math.min(valley_point_before, low)
                else
                    // Update valley_after while below cloud
                    valley_point_after := math.min(valley_point_after, low)
                    
                    // Continuous comparison while updating valley_after
                    if valley_point_after < valley_point_before
                        trend_change_signal := "lower_valley_detected"
                        // New lower valley found - update before and reset after for new cycle
                        valley_point_before := valley_point_after
                        valley_point_after := na
            else
                // First time below cloud or returning from cloud
                if not na(valley_point_before) and na(valley_point_after)
                    // Returning from cloud - this is valley_after
                    valley_point_after := low
                    
                    // Initial comparison (but signal will be confirmed when returning to cloud)
                    if valley_point_after > valley_point_before
                        trend_change_signal := "valley_failure_detected"
                    else if valley_point_after < valley_point_before
                        trend_change_signal := "lower_valley_detected"
                        // New lower valley found - update before and reset after for new cycle
                        valley_point_before := valley_point_after
                        valley_point_after := na
                else
                    // First valley in this downtrend
                    valley_point_before := low
                    valley_point_after := na
                was_below_cloud := true
        else
            // Price not below cloud - check if we need to finalize comparison
            if not na(valley_point_after)
                // Only valley failure needs confirmation (lower valley already processed)
                if valley_point_after > valley_point_before
                    // Confirm valley failure when returning to cloud
                    trend_change_signal := "valley_failure_confirmed"
                    // Reset after confirmation - ready for new cycle
                    valley_point_after := na
            was_below_cloud := false
    
    // Store previous signal for next bar
    previous_signal := Final_Signal
    
    // Return all tracking data
    [current_phase, peak_point_before, peak_point_after, valley_point_before, valley_point_after, trend_change_signal]

// Call the tracking function
[phase, peak_before, peak_after, valley_before, valley_after, change_signal] = track_peak_valley_signals()

// Create information label for peak/valley tracking
show_peak_valley_info = input.bool(false, "Show Peak/Valley Tracking", tooltip="Display peak/valley tracking information")

if show_peak_valley_info and barstate.islast
    // Create label with peak/valley information
    info_text = "📊 PEAK/VALLEY TRACKING\n"
    info_text += "Phase: " + phase + "\n"
    
    // Only show relevant information based on current phase
    if phase == "uptrend"
        // In uptrend: only show Peak information, Valley = 0
        if not na(peak_before)
            info_text += "Peak Before: " + str.tostring(peak_before, "#.####") + "\n"
        if not na(peak_after)
            info_text += "Peak After: " + str.tostring(peak_after, "#.####") + "\n"
        info_text += "Valley: 0 (uptrend)\n"
        
    else if phase == "downtrend"
        // In downtrend: only show Valley information, Peak = 0
        if not na(valley_before)
            info_text += "Valley Before: " + str.tostring(valley_before, "#.####") + "\n"
        if not na(valley_after)
            info_text += "Valley After: " + str.tostring(valley_after, "#.####") + "\n"
        info_text += "Peak: 0 (downtrend)\n"
        
    else
        // Neutral phase: show current status
        info_text += "Peak: 0 (neutral)\n"
        info_text += "Valley: 0 (neutral)\n"
    
    if not na(change_signal)
        info_text += "🚨 Signal: " + change_signal + "\n"
    
    info_text += "Cloud: " + str.tostring(shortTermFilter, "#.####")
    
    // Determine label color based on phase
    label_color = switch phase
        "uptrend" => color.new(color.green, 20)
        "downtrend" => color.new(color.red, 20)
        => color.new(color.gray, 20)
    
    label.new(bar_index, high + (high - low) * 0.5, info_text, 
              style = label.style_label_left, 
              color = label_color, 
              textcolor = color.white, 
              size = size.normal)

// ==================== COMPREHENSIVE ALERT SYSTEM ====================
// Alert system for cloud breakouts, trend changes, and extreme conditions

// ==================== CLOUD BREAKOUT DETECTION ====================
// Detect when price is approaching or breaking through clouds
short_cloud_upper = shortTermFilter + short_distance
short_cloud_lower = shortTermFilter - short_distance
medium_cloud_upper = mediumTermFilter + medium_distance  
medium_cloud_lower = mediumTermFilter - medium_distance

// Price position relative to clouds
price_above_short_cloud = close > short_cloud_upper
price_below_short_cloud = close < short_cloud_lower
price_in_short_cloud = close >= short_cloud_lower and close <= short_cloud_upper

price_above_medium_cloud = close > medium_cloud_upper
price_below_medium_cloud = close < medium_cloud_lower
price_in_medium_cloud = close >= medium_cloud_lower and close <= medium_cloud_upper

// Trend direction detection
short_trend_up = ta.rising(shortTermFilter, 3)
short_trend_down = ta.falling(shortTermFilter, 3)
medium_trend_up = ta.rising(mediumTermFilter, 3)
medium_trend_down = ta.falling(mediumTermFilter, 3)

// ==================== SUPPORT/RESISTANCE SIGNALS ====================
// Dynamic coefficient based on timeframe
// Short-term coefficient: <= 4H: 0.9 (more sensitive), >= D: 0.7 (less sensitive)
sr_coefficient_short = switch
    isday => isGold ? 0.7 : isVN ? 1 : 0.9 
    isweek or ismonth => isGold ? 0.5 : isVN ? 1 : 0.9
    => 0.9

// Medium-term coefficient: <= 4H: 0.9 (more sensitive), >= D: 0.7 (less sensitive)  
sr_coefficient_medium = switch
    isday => isGold ? 0.7 : isVN ? 1 : 0.9
    isweek or ismonth => isGold ? 0.5 : isVN ? 1 : 0.9
    => 0.9

// Detect when price enters support/resistance zones based on cloud status
// WEAK_SUPPORT: Price approaches short cloud support when tiny Triangle status is WB or B
// Low touches near support zone: low < (filter - coefficient*distance) instead of penetrating cloud
// Changed from short_trend_up to checking tiny Triangle status (WB or B only)
weak_support = low < (shortTermFilter - sr_coefficient_short * short_distance) and (Final_Signal == "WB" or Final_Signal == "B")
// WEAK_RESISTANCE: Price approaches short cloud resistance when tiny Triangle status is WS or S
// High touches near resistance zone: high > (filter + coefficient*distance) instead of penetrating cloud
// Changed from short_trend_down to checking tiny Triangle status (WS or S only)
weak_resistance = high > (shortTermFilter + sr_coefficient_short * short_distance) and (Final_Signal == "WS" or Final_Signal == "S")
// STRONG_SUPPORT: Price approaches medium cloud support when Medium Triangle status is WB or B
// Low touches near support zone: low < (filter - coefficient*distance) instead of penetrating cloud
// Changed from medium_trend_up to checking Medium Triangle status (WB or B only)
strong_support = low < (mediumTermFilter - sr_coefficient_medium * medium_distance) and (Final_Medium_Signal == "WB" or Final_Medium_Signal == "B")
// STRONG_RESISTANCE: Price approaches medium cloud resistance when Medium Triangle status is WS or S
// High touches near resistance zone: high > (filter + coefficient*distance) instead of penetrating cloud
// Changed from medium_trend_down to checking Medium Triangle status (WS or S only)
strong_resistance = high > (mediumTermFilter + sr_coefficient_medium * medium_distance) and (Final_Medium_Signal == "WS" or Final_Medium_Signal == "S")

// ==================== BREAKOUT SIGNALS ====================
// Detect actual breakouts through clouds - only when Triangle status changes
// IMPORTANT: BREAKOUT only triggers when Triangle status changes from opposite to target status

// Detect Triangle status changes
signal_changed_to_bullish = (Final_Signal[1] == "WS" or Final_Signal[1] == "S") and (Final_Signal == "WB" or Final_Signal == "B")
signal_changed_to_bearish = (Final_Signal[1] == "WB" or Final_Signal[1] == "B") and (Final_Signal == "WS" or Final_Signal == "S")
medium_signal_changed_to_bullish = (Final_Medium_Signal[1] == "WS" or Final_Medium_Signal[1] == "S") and (Final_Medium_Signal == "WB" or Final_Medium_Signal == "B")
medium_signal_changed_to_bearish = (Final_Medium_Signal[1] == "WB" or Final_Medium_Signal[1] == "B") and (Final_Medium_Signal == "WS" or Final_Medium_Signal == "S")

// BREAKOUT conditions: Price breakout + Triangle status change
// weak_breakout_up: Price breaks above short cloud AND Triangle changes from WS/S to WB/B
weak_breakout_up = (low < short_cloud_upper and close > short_cloud_upper) and signal_changed_to_bullish
// weak_breakout_down: Price breaks below short cloud AND Triangle changes from WB/B to WS/S
weak_breakout_down = (high > short_cloud_lower and close < short_cloud_lower) and signal_changed_to_bearish
// strong_breakout_up: Price breaks above medium cloud AND Medium Triangle changes from WS/S to WB/B
strong_breakout_up = (low < medium_cloud_upper and close > medium_cloud_upper) and medium_signal_changed_to_bullish
// strong_breakout_down: Price breaks below medium cloud AND Medium Triangle changes from WB/B to WS/S
strong_breakout_down = (high > medium_cloud_lower and close < medium_cloud_lower) and medium_signal_changed_to_bearish

// ==================== TREND CHANGE SIGNALS ====================
// Detect when trend changes direction (for "Buy to Sell" / "Sell to Buy" alerts)

// Calculate distance variables - separate for bullish and bearish conditions  
// For BEARISH (price too high): use HIGH distance above filters
price_distance_from_short_bearish = (high - shortTermFilter) / atr_value  // X distance for bearish
price_distance_from_medium_bearish = (high - mediumTermFilter) / atr_value  // Y distance for bearish
xy_sum_condition_bearish = (price_distance_from_short_bearish + price_distance_from_medium_bearish) >= (X_Penalty_Threshold + Y_Penalty_Threshold)
price_far_from_filters_bearish = (price_distance_from_short_bearish >= X_Penalty_Threshold or price_distance_from_medium_bearish >= Y_Penalty_Threshold) and xy_sum_condition_bearish

// For BULLISH (price too low): use LOW distance below filters (absolute values vs negative thresholds)
price_distance_from_short_bullish = (shortTermFilter - low) / atr_value  // X distance for bullish
price_distance_from_medium_bullish = (mediumTermFilter - low) / atr_value  // Y distance for bullish
xy_sum_condition_bullish = (price_distance_from_short_bullish + price_distance_from_medium_bullish) >= math.abs(X_Bonus_Threshold_Used + Y_Bonus_Threshold_Used)
price_far_from_filters_bullish = (price_distance_from_short_bullish >= math.abs(X_Bonus_Threshold_Used) or price_distance_from_medium_bullish >= math.abs(Y_Bonus_Threshold_Used)) and xy_sum_condition_bullish

// TREND_CHANGE_BEARISH: Vùng đỉnh - giá tăng cực mạnh, bị penalty từ hệ thống đánh giá
// Điều kiện: 1) FEX_ALL > FEX_Penalty_Threshold_1st, 2) HIGH xa ít nhất 1 filter và thỏa Sum, 3) bearish_ratio > 90%
trend_change_to_bearish = fex_all > FEX_Penalty_Threshold_1st and price_far_from_filters_bearish and bearish_ratio > 90.0

// TREND_CHANGE_BULLISH: Vùng đáy - giá giảm cực mạnh, được bonus từ hệ thống đánh giá
// Điều kiện: 1) FEX_ALL < FEX_Bonus_Threshold_1st, 2) LOW xa ít nhất 1 filter và thỏa Sum, 3) bullish_ratio > 90%
trend_change_to_bullish = fex_all < FEX_Bonus_Threshold_1st and price_far_from_filters_bullish and bullish_ratio > 90.0

// ==================== EXTREME CONDITIONS ====================
// Detect when Bullish/Bearish ratio reaches 100%
extreme_bullish = bullish_ratio >= 99.5  // 100% Bullish (allowing for small rounding)
extreme_bearish = bearish_ratio >= 99.5  // 100% Bearish (allowing for small rounding)

// Track previous extreme states to detect new occurrences
var bool prev_extreme_bullish = false
var bool prev_extreme_bearish = false

new_extreme_bullish = extreme_bullish and not prev_extreme_bullish
new_extreme_bearish = extreme_bearish and not prev_extreme_bearish

prev_extreme_bullish := extreme_bullish
prev_extreme_bearish := extreme_bearish

// ==================== ALERT CONDITIONS ====================

// 1. SUPPORT/RESISTANCE SIGNALS - When touching support/resistance zones
alertcondition(weak_support, title="Weak Support", message="WEAK SUPPORT - Price touched Short Cloud support (Trend: UP) | Price: {{close}} | TF: {{interval}}")

alertcondition(weak_resistance, title="Weak Resistance", message="WEAK RESISTANCE - Price touched Short Cloud resistance (Trend: DOWN) | Price: {{close}} | TF: {{interval}}")

alertcondition(strong_support, title="Strong Support", message="STRONG SUPPORT - Price touched Medium Cloud support (Trend: UP) | Price: {{close}} | TF: {{interval}}")

alertcondition(strong_resistance, title="Strong Resistance", message="STRONG RESISTANCE - Price touched Medium Cloud resistance (Trend: DOWN) | Price: {{close}} | TF: {{interval}}")

// 2. BREAKOUT SIGNALS - When breaking through clouds
alertcondition(weak_breakout_up, title="Weak Buy Signal", message="WEAK BUY SIGNAL - Price broke above Short Cloud (Triangle changed: WS/S → WB/B) | Price: {{close}} | TF: {{interval}}")

alertcondition(weak_breakout_down, title="Weak Sell Signal", message="WEAK SELL SIGNAL - Price broke below Short Cloud (Triangle changed: WB/B → WS/S) | Price: {{close}} | TF: {{interval}}")

alertcondition(strong_breakout_up, title="Strong Buy Signal", message="STRONG BUY SIGNAL - Price broke above Medium Cloud (Triangle changed: WS/S → WB/B) | Price: {{close}} | TF: {{interval}}")

alertcondition(strong_breakout_down, title="Strong Sell Signal", message="STRONG SELL SIGNAL - Price broke below Medium Cloud (Triangle changed: WB/B → WS/S) | Price: {{close}} | TF: {{interval}}")

// 3. TREND REVERSAL SIGNALS
alertcondition(trend_change_to_bearish, title="Buy to Sell Reversal", message="BUY TO SELL REVERSAL - Trend changed from Bullish to Bearish | ST Sell Signal, TP Buy Level | Price: {{close}} | TF: {{interval}}")

alertcondition(trend_change_to_bullish, title="Sell to Buy Reversal", message="SELL TO BUY REVERSAL - Trend changed from Bearish to Bullish | ST Buy Signal, TP Sell Level | Price: {{close}} | TF: {{interval}}")

// 4. EXTREME CONDITIONS - Peak/Valley Warnings
alertcondition(new_extreme_bullish, title="Waiting Sell at Peak", message="WAITING SELL AT PEAK - Bullish ratio reached 100%! Potential reversal zone | Price: {{close}} | TF: {{interval}}")

alertcondition(new_extreme_bearish, title="Waiting Buy at Peak", message="WAITING BUY AT PEAK - Bearish ratio reached 100%! Potential reversal zone | Price: {{close}} | TF: {{interval}}")

// 5. SIGNAL DETECTION - Simplified trigger for visual indicators
trigger_alert = weak_support or weak_resistance or strong_support or strong_resistance or weak_breakout_up or weak_breakout_down or strong_breakout_up or strong_breakout_down or trend_change_to_bullish or trend_change_to_bearish

// Use plotchar with separate icons for Buy and Sell signals
// Define Buy and Sell conditions
buy_signals = weak_support or strong_support or weak_breakout_up or strong_breakout_up or trend_change_to_bullish

sell_signals = weak_resistance or strong_resistance or weak_breakout_down or strong_breakout_down or trend_change_to_bearish

// Plot Buy signals below bars with green color
//plotchar(buy_signals, title="🚀 PTT Buy Signals", char="🚀", location=location.belowbar, color=color.new(color.green, 0), size=size.small)

// Plot Sell signals above bars with red color  
//plotchar(sell_signals, title="📉 PTT Sell Signals", char="📉", location=location.abovebar, color=color.new(color.red, 0), size=size.small)

// ==================== UNIFIED DEBUG LABELS ====================
// When both BUY and SELL signals appear, prioritize STRONG signals only
// Priority: TREND_CHANGE > STRONG > WEAK (only show the strongest single signal)

// Determine the strongest signal across both BUY and SELL
strongest_signal_text = ""
is_buy_signal = false
label_size = size.small

// Only show labels for timeframes >= 1H (no labels for 1min, 3min, 5min, 15min, 30min)
if (buy_signals or sell_signals) and not (is1min or is3min or is5min or is15min or is30min)
    // Check all conditions in global priority order (strongest first)
    if trend_change_to_bullish
        strongest_signal_text := "🔄 UP"
        is_buy_signal := true
        label_size := size.normal  // Trend change = largest
    else if trend_change_to_bearish
        strongest_signal_text := "🔄 DOWN"
        is_buy_signal := false
        label_size := size.normal  // Trend change = largest
    else if strong_support
        strongest_signal_text := "💪 STRONG"
        is_buy_signal := true
        label_size := size.normal  // Strong signals = medium
    else if strong_resistance
        strongest_signal_text := "🛡️ STRONG"
        is_buy_signal := false
        label_size := size.normal  // Strong signals = medium
    else if strong_breakout_up
        strongest_signal_text := "🚀 BREAK"
        is_buy_signal := true
        label_size := size.normal  // Strong signals = medium
    else if strong_breakout_down
        strongest_signal_text := "💥 BREAK"
        is_buy_signal := false
        label_size := size.normal  // Strong signals = medium
    else if weak_support
        strongest_signal_text := "⬆️ WEAK"
        is_buy_signal := true
        label_size := size.small  // Weak signals = small
    else if weak_resistance
        strongest_signal_text := "⬇️ WEAK"
        is_buy_signal := false
        label_size := size.small  // Weak signals = small
    else if weak_breakout_up
        strongest_signal_text := "📶 BREAK"
        is_buy_signal := true
        label_size := size.small  // Weak signals = small
    else if weak_breakout_down
        strongest_signal_text := "📉 BREAK"
        is_buy_signal := false
        label_size := size.small  // Weak signals = small

    // Create single label based on strongest signal
    if strongest_signal_text != ""
        if is_buy_signal
            label.new(bar_index, low - (atr_value * 0.5), 
                      text=strongest_signal_text, 
                      style=label.style_label_up, 
                      color=color.new(color.green, 80), 
                      textcolor=color.black, 
                      size=label_size)
        else
            label.new(bar_index, high + (atr_value * 0.5), 
                      text=strongest_signal_text, 
                      style=label.style_label_down, 
                      color=color.new(color.red, 80), 
                      textcolor=color.black, 
                      size=label_size)

// COMPREHENSIVE ALERT - Single alert for all signals
alertcondition(trigger_alert, title="🔔 PTT Alert", message="{{ticker}} : {{close}} | TF: {{interval}} | Time: {{time}}")
