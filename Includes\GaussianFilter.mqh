//+------------------------------------------------------------------+
//| GaussianFilter.mqh - <PERSON><PERSON>                             |
//+------------------------------------------------------------------+
#include "Constants.mqh"
#include "TimeframeUtils.mqh"
#include "CommonFunctions.mqh"

// Mảng lưu trữ giá trị trước đó cho f_filt9x_Gaus
double f_Gaus_prev[10][10]; // [order][shift]

//+------------------------------------------------------------------+
//| Hàm f_filt9x_Gaus                                                |
//+------------------------------------------------------------------+
double f_filt9x_Gaus(double a_Gaus, double s_Gaus, int i_Gaus, int shift = 0) {
    int m2_Gaus = 0;
    int m3_Gaus = 0;
    int m4_Gaus = 0;
    int m5_Gaus = 0;
    int m6_Gaus = 0;
    int m7_Gaus = 0;
    int m8_Gaus = 0;
    int m9_Gaus = 0;
    double f_Gaus = 0.0;
    double x_Gaus = 1 - a_Gaus;
    
    // Weights
    if(i_Gaus == 9) m2_Gaus = 36;
    else if(i_Gaus == 8) m2_Gaus = 28;
    else if(i_Gaus == 7) m2_Gaus = 21;
    else if(i_Gaus == 6) m2_Gaus = 15;
    else if(i_Gaus == 5) m2_Gaus = 10;
    else if(i_Gaus == 4) m2_Gaus = 6;
    else if(i_Gaus == 3) m2_Gaus = 3;
    else if(i_Gaus == 2) m2_Gaus = 1;
    else m2_Gaus = 0;
    
    if(i_Gaus == 9) m3_Gaus = 84;
    else if(i_Gaus == 8) m3_Gaus = 56;
    else if(i_Gaus == 7) m3_Gaus = 35;
    else if(i_Gaus == 6) m3_Gaus = 20;
    else if(i_Gaus == 5) m3_Gaus = 10;
    else if(i_Gaus == 4) m3_Gaus = 4;
    else if(i_Gaus == 3) m3_Gaus = 1;
    else m3_Gaus = 0;
    
    if(i_Gaus == 9) m4_Gaus = 126;
    else if(i_Gaus == 8) m4_Gaus = 70;
    else if(i_Gaus == 7) m4_Gaus = 35;
    else if(i_Gaus == 6) m4_Gaus = 15;
    else if(i_Gaus == 5) m4_Gaus = 5;
    else if(i_Gaus == 4) m4_Gaus = 1;
    else m4_Gaus = 0;
    
    if(i_Gaus == 9) m5_Gaus = 126;
    else if(i_Gaus == 8) m5_Gaus = 56;
    else if(i_Gaus == 7) m5_Gaus = 21;
    else if(i_Gaus == 6) m5_Gaus = 6;
    else if(i_Gaus == 5) m5_Gaus = 1;
    else m5_Gaus = 0;
    
    if(i_Gaus == 9) m6_Gaus = 84;
    else if(i_Gaus == 8) m6_Gaus = 28;
    else if(i_Gaus == 7) m6_Gaus = 7;
    else if(i_Gaus == 6) m6_Gaus = 1;
    else m6_Gaus = 0;
    
    if(i_Gaus == 9) m7_Gaus = 36;
    else if(i_Gaus == 8) m7_Gaus = 8;
    else if(i_Gaus == 7) m7_Gaus = 1;
    else m7_Gaus = 0;
    
    if(i_Gaus == 9) m8_Gaus = 9;
    else if(i_Gaus == 8) m8_Gaus = 1;
    else m8_Gaus = 0;
    
    if(i_Gaus == 9) m9_Gaus = 1;
    else m9_Gaus = 0;
    
    f_Gaus = MathPow(a_Gaus, i_Gaus) * NZ(s_Gaus) + i_Gaus * x_Gaus * NZ(f_Gaus_prev[i_Gaus][1]);
    
    if(i_Gaus >= 2) f_Gaus -= m2_Gaus * MathPow(x_Gaus, 2) * NZ(f_Gaus_prev[i_Gaus][2]);
    if(i_Gaus >= 3) f_Gaus += m3_Gaus * MathPow(x_Gaus, 3) * NZ(f_Gaus_prev[i_Gaus][3]);
    if(i_Gaus >= 4) f_Gaus -= m4_Gaus * MathPow(x_Gaus, 4) * NZ(f_Gaus_prev[i_Gaus][4]);
    if(i_Gaus >= 5) f_Gaus += m5_Gaus * MathPow(x_Gaus, 5) * NZ(f_Gaus_prev[i_Gaus][5]);
    if(i_Gaus >= 6) f_Gaus -= m6_Gaus * MathPow(x_Gaus, 6) * NZ(f_Gaus_prev[i_Gaus][6]);
    if(i_Gaus >= 7) f_Gaus += m7_Gaus * MathPow(x_Gaus, 7) * NZ(f_Gaus_prev[i_Gaus][7]);
    if(i_Gaus >= 8) f_Gaus -= m8_Gaus * MathPow(x_Gaus, 8) * NZ(f_Gaus_prev[i_Gaus][8]);
    if(i_Gaus == 9) f_Gaus += m9_Gaus * MathPow(x_Gaus, 9) * NZ(f_Gaus_prev[i_Gaus][9]);
    
    // Cập nhật mảng giá trị trước đó
    for(int i=9; i>0; i--) {
        f_Gaus_prev[i_Gaus][i] = f_Gaus_prev[i_Gaus][i-1];
    }
    f_Gaus_prev[i_Gaus][0] = f_Gaus;
    
    return f_Gaus;
}

//+------------------------------------------------------------------+
//| Hàm f_pole_Gaus                                                  |
//+------------------------------------------------------------------+
void f_pole_Gaus(double a, double s, int i, double &fn, double &f1, int shift = 0) {
    f1 = f_filt9x_Gaus(a, s, 1, shift);
    double f2 = i >= 2 ? f_filt9x_Gaus(a, s, 2, shift) : 0;
    double f3 = i >= 3 ? f_filt9x_Gaus(a, s, 3, shift) : 0;
    double f4 = i >= 4 ? f_filt9x_Gaus(a, s, 4, shift) : 0;
    double f5 = i >= 5 ? f_filt9x_Gaus(a, s, 5, shift) : 0;
    double f6 = i >= 6 ? f_filt9x_Gaus(a, s, 6, shift) : 0;
    double f7 = i >= 7 ? f_filt9x_Gaus(a, s, 7, shift) : 0;
    double f8 = i >= 8 ? f_filt9x_Gaus(a, s, 8, shift) : 0;
    double f9 = i == 9 ? f_filt9x_Gaus(a, s, 9, shift) : 0;
    
    if(i == 1) fn = f1;
    else if(i == 2) fn = f2;
    else if(i == 3) fn = f3;
    else if(i == 4) fn = f4;
    else if(i == 5) fn = f5;
    else if(i == 6) fn = f6;
    else if(i == 7) fn = f7;
    else if(i == 8) fn = f8;
    else if(i == 9) fn = f9;
    else fn = 0;
}

//+------------------------------------------------------------------+
//| Tính toán Gaussian Filter cho timeframe cụ thể                   |
//+------------------------------------------------------------------+
void CalcGausFilter(string timeframe, double &filt, double &hband, double &lband, color &fcolor, bool modeLag = false, bool modeFast = false) {
    // Thiết lập period dựa trên timeframe
    int local_per_Gaus = 222;
    if(timeframe == "1") local_per_Gaus = 1500;
    else if(timeframe == "3") local_per_Gaus = 500;
    else if(timeframe == "5") local_per_Gaus = 369;
    else if(timeframe == "15") local_per_Gaus = 211;
    else if(timeframe == "60") local_per_Gaus = 186;
    else if(timeframe == "240") local_per_Gaus = 111;
    else if(timeframe == "D") local_per_Gaus = 150;
    else if(timeframe == "W") local_per_Gaus = 60;
    else if(timeframe == "MN") local_per_Gaus = 72;
    
    // Beta và Alpha Components
    double local_beta_Gaus = (1 - MathCos(4 * PI / local_per_Gaus)) / (MathPow(1.414, 0.5) - 1);
    double local_alpha_Gaus = -local_beta_Gaus + MathSqrt(MathPow(local_beta_Gaus, 2) + 2 * local_beta_Gaus);
    
    // Lag
    double local_lag_Gaus = (local_per_Gaus - 1) / (2 * 4);
    
    // Data
    double ohlc4 = OHLC4();
    double ohlc4_lag = OHLC4((int)local_lag_Gaus);
    double local_srcdata_Gaus = modeLag ? ohlc4 + ohlc4 - ohlc4_lag : ohlc4;
    
    double tr = MathMax(High[0], Close[1]) - MathMin(Low[0], Close[1]);
    double tr_lag = MathMax(High[(int)local_lag_Gaus], Close[(int)local_lag_Gaus+1]) - MathMin(Low[(int)local_lag_Gaus], Close[(int)local_lag_Gaus+1]);
    double local_trdata_Gaus = modeLag ? tr + tr - tr_lag : tr;
    
    // Calculate filter
    double local_filtn_Gaus, local_filt1_Gaus;
    double local_filtntr_Gaus, local_filt1tr_Gaus;
    
    f_pole_Gaus(local_alpha_Gaus, local_srcdata_Gaus, 4, local_filtn_Gaus, local_filt1_Gaus);
    f_pole_Gaus(local_alpha_Gaus, local_trdata_Gaus, 4, local_filtntr_Gaus, local_filt1tr_Gaus);
    
    // Lag Reduction
    double local_filt_Gaus = modeFast ? (local_filtn_Gaus + local_filt1_Gaus) / 2 : local_filtn_Gaus;
    double local_filttr_Gaus = modeFast ? (local_filtntr_Gaus + local_filt1tr_Gaus) / 2 : local_filtntr_Gaus;
    
    // Bands and multiplier based on timeframe
    double close_array[];
    ArraySetAsSeries(close_array, true);
    CopyClose(Symbol(), StringToTimeFrame(timeframe), 0, local_per_Gaus * 2, close_array);
    double stdev = 0;
    for(int i=0; i<local_per_Gaus*2; i++) {
        stdev += MathPow(close_array[i] - local_filtn_Gaus, 2);
    }
    stdev = MathSqrt(stdev / (local_per_Gaus * 2)) / 4;
    double local_cloudSize = stdev;
    
    double local_mult = 1.414;
    
    if(timeframe == "1") local_mult = 6.39;
    else if(timeframe == "3") local_mult = 6.39;
    else if(timeframe == "5") local_mult = 5.39;
    else if(timeframe == "15") local_mult = 3.69;
    else if(timeframe == "60") local_mult = 3.69;
    else if(timeframe == "240") local_mult = 2.39;
    else if(timeframe == "D") local_mult = 1.6;
    else if(timeframe == "W") local_mult = 1.414;
    else if(timeframe == "MN") local_mult = 1.414;
    
    local_hband_Gaus = local_filtn_Gaus + local_mult * local_cloudSize;
    local_lband_Gaus = local_filtn_Gaus - local_mult * local_cloudSize;
    
    // Color
    static double prev_filt_Gaus = 0;
    color local_fcolor_Gaus = local_filt_Gaus > prev_filt_Gaus ? COLOR_GAUS_UP : 
                             local_filt_Gaus < prev_filt_Gaus ? COLOR_GAUS_DN : COLOR_DEFAULT;
    prev_filt_Gaus = local_filt_Gaus;
    
    // Set output values
    filt = local_filt_Gaus;
    hband = local_hband_Gaus;
    lband = local_lband_Gaus;
    fcolor = local_fcolor_Gaus;
}