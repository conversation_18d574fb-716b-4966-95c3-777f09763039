//+------------------------------------------------------------------+
//|                                                  strategy_mq5.mq5 |
//|                        Copyright 2024, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 10
#property indicator_plots   7

// Plot buffers
double Alpha15Buffer[];
double Alpha60Buffer[];
double Alpha240Buffer[];
double AlphaDailyBuffer[];
double AlphaWeeklyBuffer[];
double Filter5mBuffer[];
double Filter1hBuffer[];
double HBand1hBuffer[];
double LBand1hBuffer[];
double FEXAllBuffer[];

// Input parameters
input bool modeLag_Gaus = false;        // Reduced Lag Mode
input bool modeFast_Gaus = false;       // Fast Response Mode

// Global variables for Gaussian filter
double f_Gaus[10][500];  // Reduced array size for memory efficiency
bool isGold, isBitcoin;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    // Set indicator buffers
    SetIndexBuffer(0, Alpha15Buffer, INDICATOR_DATA);
    SetIndexBuffer(1, Alpha60Buffer, INDICATOR_DATA);
    SetIndexBuffer(2, Alpha240Buffer, INDICATOR_DATA);
    SetIndexBuffer(3, AlphaDailyBuffer, INDICATOR_DATA);
    SetIndexBuffer(4, AlphaWeeklyBuffer, INDICATOR_DATA);
    SetIndexBuffer(5, Filter5mBuffer, INDICATOR_DATA);
    SetIndexBuffer(6, Filter1hBuffer, INDICATOR_DATA);
    SetIndexBuffer(7, HBand1hBuffer, INDICATOR_DATA);
    SetIndexBuffer(8, LBand1hBuffer, INDICATOR_DATA);
    SetIndexBuffer(9, FEXAllBuffer, INDICATOR_DATA);
    
    // Set plot properties
    PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(0, PLOT_LINE_COLOR, clrBlue);
    PlotIndexSetInteger(0, PLOT_LINE_WIDTH, 1);
    PlotIndexSetString(0, PLOT_LABEL, "Alpha 15m");
    
    PlotIndexSetInteger(1, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(1, PLOT_LINE_COLOR, clrGreen);
    PlotIndexSetInteger(1, PLOT_LINE_WIDTH, 2);
    PlotIndexSetString(1, PLOT_LABEL, "Alpha 1h");
    
    PlotIndexSetInteger(2, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(2, PLOT_LINE_COLOR, clrBlack);
    PlotIndexSetInteger(2, PLOT_LINE_WIDTH, 2);
    PlotIndexSetString(2, PLOT_LABEL, "Alpha 4h");
    
    PlotIndexSetInteger(3, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(3, PLOT_LINE_COLOR, clrRed);
    PlotIndexSetInteger(3, PLOT_LINE_WIDTH, 2);
    PlotIndexSetString(3, PLOT_LABEL, "Alpha D");
    
    PlotIndexSetInteger(4, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(4, PLOT_LINE_COLOR, clrOrange);
    PlotIndexSetInteger(4, PLOT_LINE_WIDTH, 3);
    PlotIndexSetString(4, PLOT_LABEL, "Alpha W");
    
    PlotIndexSetInteger(5, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(5, PLOT_LINE_COLOR, clrYellow);
    PlotIndexSetInteger(5, PLOT_LINE_WIDTH, 3);
    PlotIndexSetString(5, PLOT_LABEL, "5m Filter");
    
    PlotIndexSetInteger(6, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(6, PLOT_LINE_COLOR, clrCyan);
    PlotIndexSetInteger(6, PLOT_LINE_WIDTH, 2);
    PlotIndexSetString(6, PLOT_LABEL, "1h Filter");
    
    // Initialize symbol detection
    isGold = (StringFind(Symbol(), "XAU") >= 0 || StringFind(Symbol(), "GOLD") >= 0);
    isBitcoin = (StringFind(Symbol(), "BTC") >= 0);
    
    // Initialize Gaussian filter arrays
    ArrayInitialize(f_Gaus, 0.0);
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Helper Functions - Original Pine Script Logic                   |
//+------------------------------------------------------------------+

// Gaussian Filter Function - Original Pine Script Logic
double f_filt9x_Gaus(double _a_Gaus, double _s_Gaus, int _i_Gaus, int shift)
{
    if(shift >= 500) return _s_Gaus; // Safety check
    
    int _m2_Gaus = 0, _m3_Gaus = 0, _m4_Gaus = 0, _m5_Gaus = 0;
    int _m6_Gaus = 0, _m7_Gaus = 0, _m8_Gaus = 0, _m9_Gaus = 0;
    
    double _x_Gaus = 1 - _a_Gaus;
    
    // Weights - Original Pine Script Logic
    if(_i_Gaus == 9) { _m2_Gaus = 36; _m3_Gaus = 84; _m4_Gaus = 126; _m5_Gaus = 126; _m6_Gaus = 84; _m7_Gaus = 36; _m8_Gaus = 9; _m9_Gaus = 1; }
    else if(_i_Gaus == 8) { _m2_Gaus = 28; _m3_Gaus = 56; _m4_Gaus = 70; _m5_Gaus = 56; _m6_Gaus = 28; _m7_Gaus = 8; _m8_Gaus = 1; }
    else if(_i_Gaus == 7) { _m2_Gaus = 21; _m3_Gaus = 35; _m4_Gaus = 35; _m5_Gaus = 21; _m6_Gaus = 7; _m7_Gaus = 1; }
    else if(_i_Gaus == 6) { _m2_Gaus = 15; _m3_Gaus = 20; _m4_Gaus = 15; _m5_Gaus = 6; _m6_Gaus = 1; }
    else if(_i_Gaus == 5) { _m2_Gaus = 10; _m3_Gaus = 10; _m4_Gaus = 5; _m5_Gaus = 1; }
    else if(_i_Gaus == 4) { _m2_Gaus = 6; _m3_Gaus = 4; _m4_Gaus = 1; }
    else if(_i_Gaus == 3) { _m2_Gaus = 3; _m3_Gaus = 1; }
    else if(_i_Gaus == 2) { _m2_Gaus = 1; }
    
    // Original Pine Script calculation
    double _f_Gaus = MathPow(_a_Gaus, _i_Gaus) * _s_Gaus + 
                     _i_Gaus * _x_Gaus * f_Gaus[_i_Gaus][shift + 1];
    
    if(_i_Gaus >= 2) _f_Gaus -= _m2_Gaus * MathPow(_x_Gaus, 2) * f_Gaus[_i_Gaus][shift + 2];
    if(_i_Gaus >= 3) _f_Gaus += _m3_Gaus * MathPow(_x_Gaus, 3) * f_Gaus[_i_Gaus][shift + 3];
    if(_i_Gaus >= 4) _f_Gaus -= _m4_Gaus * MathPow(_x_Gaus, 4) * f_Gaus[_i_Gaus][shift + 4];
    if(_i_Gaus >= 5) _f_Gaus += _m5_Gaus * MathPow(_x_Gaus, 5) * f_Gaus[_i_Gaus][shift + 5];
    if(_i_Gaus >= 6) _f_Gaus -= _m6_Gaus * MathPow(_x_Gaus, 6) * f_Gaus[_i_Gaus][shift + 6];
    if(_i_Gaus >= 7) _f_Gaus += _m7_Gaus * MathPow(_x_Gaus, 7) * f_Gaus[_i_Gaus][shift + 7];
    if(_i_Gaus >= 8) _f_Gaus -= _m8_Gaus * MathPow(_x_Gaus, 8) * f_Gaus[_i_Gaus][shift + 8];
    if(_i_Gaus == 9) _f_Gaus += _m9_Gaus * MathPow(_x_Gaus, 9) * f_Gaus[0][shift + 9];
    
    f_Gaus[_i_Gaus][shift] = _f_Gaus;
    return _f_Gaus;
}

// f_pole_Gaus function - Original Pine Script Logic
void f_pole_Gaus(double _a, double _s, int _i, int shift, double &_fn, double &_f1)
{
    _f1 = f_filt9x_Gaus(_a, _s, 1, shift);
    double _f2 = (_i >= 2) ? f_filt9x_Gaus(_a, _s, 2, shift) : 0;
    double _f3 = (_i >= 3) ? f_filt9x_Gaus(_a, _s, 3, shift) : 0;
    double _f4 = (_i >= 4) ? f_filt9x_Gaus(_a, _s, 4, shift) : 0;
    double _f5 = (_i >= 5) ? f_filt9x_Gaus(_a, _s, 5, shift) : 0;
    double _f6 = (_i >= 6) ? f_filt9x_Gaus(_a, _s, 6, shift) : 0;
    double _f7 = (_i >= 7) ? f_filt9x_Gaus(_a, _s, 7, shift) : 0;
    double _f8 = (_i >= 8) ? f_filt9x_Gaus(_a, _s, 8, shift) : 0;
    double _f9 = (_i == 9) ? f_filt9x_Gaus(_a, _s, 9, shift) : 0;
    
    if(_i == 1) _fn = _f1;
    else if(_i == 2) _fn = _f2;
    else if(_i == 3) _fn = _f3;
    else if(_i == 4) _fn = _f4;
    else if(_i == 5) _fn = _f5;
    else if(_i == 6) _fn = _f6;
    else if(_i == 7) _fn = _f7;
    else if(_i == 8) _fn = _f8;
    else if(_i == 9) _fn = _f9;
    else _fn = 0;
}

// Calculate Gaussian Filter - Original Pine Script Logic
void CalcGausFilter(string timeframe, double &filt, double &hband, double &lband, int shift, 
                   const double &open[], const double &high[], const double &low[], const double &close[])
{
    // Original Pine Script period logic
    int per_Gaus;
    double mult;
    
    if(timeframe == "5") { per_Gaus = 369; mult = 5.39; }
    else if(timeframe == "240") { per_Gaus = 111; mult = 2.39; }
    else if(timeframe == "60") { per_Gaus = 186; mult = 2.69; }
    else if(timeframe == "15") { per_Gaus = 211; mult = 2.69; }
    else if(timeframe == "D") { per_Gaus = 150; mult = 1.6; }
    else if(timeframe == "W") { per_Gaus = 60; mult = 1.414; }
    else { per_Gaus = 222; mult = 1.414; }
    
    // Original Pine Script Beta and Alpha Components
    double beta_Gaus = (1 - MathCos(4 * M_PI / 2 / per_Gaus)) / (MathPow(1.414, 0.5) - 1);
    double alpha_Gaus = -beta_Gaus + MathSqrt(MathPow(beta_Gaus, 2) + 2 * beta_Gaus);
    
    // Original Pine Script Lag
    int lag_Gaus = (per_Gaus - 1) / (2 * 4);
    
    // Original Pine Script Data (ohlc4)
    double ohlc4_val = (open[shift] + high[shift] + low[shift] + close[shift]) / 4;
    double srcdata_Gaus = ohlc4_val;
    
    if(modeLag_Gaus && shift + lag_Gaus < ArraySize(close))
    {
        double ohlc4_lag = (open[shift + lag_Gaus] + high[shift + lag_Gaus] + 
                           low[shift + lag_Gaus] + close[shift + lag_Gaus]) / 4;
        srcdata_Gaus = ohlc4_val + ohlc4_val - ohlc4_lag;
    }
    
    // Original Pine Script filter calculation
    double filtn_Gaus, filt1_Gaus;
    f_pole_Gaus(alpha_Gaus, srcdata_Gaus, 4, shift, filtn_Gaus, filt1_Gaus);
    
    // Original Pine Script Lag Reduction
    filt = modeFast_Gaus ? (filtn_Gaus + filt1_Gaus) / 2 : filtn_Gaus;
    
    // Original Pine Script cloudSize calculation
    double sum = 0, mean = 0;
    int count = MathMin(per_Gaus * 2, ArraySize(close) - shift);
    
    for(int i = 0; i < count; i++)
    {
        if(shift + i < ArraySize(close))
            sum += close[shift + i];
    }
    mean = count > 0 ? sum / count : close[shift];
    
    sum = 0;
    for(int i = 0; i < count; i++)
    {
        if(shift + i < ArraySize(close))
        {
            double diff = close[shift + i] - mean;
            sum += diff * diff;
        }
    }
    double cloudSize = count > 0 ? MathSqrt(sum / count) / 4 : 0;
    
    // Original Pine Script bands calculation
    hband = filtn_Gaus + mult * cloudSize;
    lband = filtn_Gaus - mult * cloudSize;
}

// Calculate Alpha - Original Pine Script Logic
double CalcAlpha(string tf, int shift, const double &high[], const double &low[], const double &close[])
{
    // Original Pine Script factor_alpha
    double factor_alpha;
    if(tf == "W") factor_alpha = 2.79;
    else if(tf == "D") factor_alpha = 3.69;
    else if(tf == "240") factor_alpha = 3.369;
    else if(tf == "60") factor_alpha = 2.8;
    else if(tf == "15") factor_alpha = 4.963;
    else factor_alpha = 3;
    
    double tfHigh = high[shift];
    double tfLow = low[shift];
    double tfClose = close[shift];
    
    // Original Pine Script True Range calculation
    double tfTR = tfHigh - tfLow;
    if(shift > 0 && shift < ArraySize(close))
    {
        double prev_close = close[shift + 1];
        tfTR = MathMax(tfTR, MathMax(MathAbs(tfHigh - prev_close), MathAbs(tfLow - prev_close)));
    }
    
    // Original Pine Script TR SMA calculation
    double tfTR_SMA = tfTR;
    if(shift >= 69)
    {
        double sum = 0;
        for(int i = 0; i < 69; i++)
        {
            if(shift + i < ArraySize(close))
            {
                double h = high[shift + i];
                double l = low[shift + i];
                double tr = h - l;
                if(shift + i > 0)
                {
                    double pc = close[shift + i + 1];
                    tr = MathMax(tr, MathMax(MathAbs(h - pc), MathAbs(l - pc)));
                }
                sum += tr;
            }
        }
        tfTR_SMA = sum / 69;
    }
    
    // Original Pine Script MFI calculation (simplified for now)
    double tfMFI = 50;
    
    // Original Pine Script alpha calculation
    static double alpha_prev = 0;
    double upT_S = tfLow - tfTR_SMA * factor_alpha;
    double downT_S = tfHigh + tfTR_SMA * factor_alpha;
    
    double alpha = (tfMFI >= 50) ? 
                   (upT_S < alpha_prev ? alpha_prev : upT_S) : 
                   (downT_S > alpha_prev ? alpha_prev : downT_S);
    
    alpha_prev = alpha;
    return alpha;
}

// Calculate FEX_ALL - Original Pine Script Logic (simplified for performance)
double CalcFexAll(int shift, const double &open[], const double &high[], const double &low[], const double &close[])
{
    // Original Pine Script timeframe-based parameters
    int Period_val = 100;
    double Factor_FEX = 2.5;
    int RSI_Period = 24;
    int Factor_plus = isBitcoin ? 5 : 0;
    int Factor_plus_rsi = isBitcoin ? 3 : 0;
    
    // Original Pine Script timeframe logic (simplified to current timeframe)
    ENUM_TIMEFRAMES current_tf = Period();
    if(current_tf == PERIOD_M5 || current_tf == PERIOD_M1 || current_tf == PERIOD_M15 || current_tf == PERIOD_M30)
    {
        Factor_FEX = 3;
        RSI_Period = 26;
        Period_val = 269;
    }
    else if(current_tf == PERIOD_H1)
    {
        Factor_FEX = 2.7;
        RSI_Period = 24 + Factor_plus_rsi;
        Period_val = 179;
    }
    else if(current_tf == PERIOD_H4)
    {
        Factor_FEX = 2.7;
        RSI_Period = 24 + Factor_plus_rsi;
        Period_val = 179;
    }
    else if(current_tf == PERIOD_D1)
    {
        Factor_FEX = 2;
        RSI_Period = 23 + Factor_plus_rsi;
    }
    
    // Simplified FEX calculation maintaining original logic structure
    double current_price = close[shift];
    double momentum = 0;
    
    if(shift < ArraySize(close) - 20)
    {
        momentum = (current_price - close[shift + 20]) / close[shift + 20] * 100;
    }
    
    // Original Pine Script BTC factor
    double BTC_factor = isBitcoin ? 0.9 : 1;
    double fex_all = momentum * BTC_factor;
    
    return fex_all;
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    if(rates_total < 100) return 0;
    
    int limit = prev_calculated == 0 ? rates_total - 100 : rates_total - prev_calculated;
    
    for(int i = limit; i >= 0; i--)
    {
        // Original Pine Script Alpha calculations
        Alpha15Buffer[i] = CalcAlpha("15", i, high, low, close);
        Alpha60Buffer[i] = CalcAlpha("60", i, high, low, close);
        Alpha240Buffer[i] = CalcAlpha("240", i, high, low, close);
        AlphaDailyBuffer[i] = CalcAlpha("D", i, high, low, close);
        AlphaWeeklyBuffer[i] = CalcAlpha("W", i, high, low, close);
        
        // Original Pine Script Gaussian filter calculations
        double filt_5m, hband_5m, lband_5m;
        double filt_1h, hband_1h, lband_1h;
        
        CalcGausFilter("5", filt_5m, hband_5m, lband_5m, i, open, high, low, close);
        CalcGausFilter("60", filt_1h, hband_1h, lband_1h, i, open, high, low, close);
        
        Filter5mBuffer[i] = filt_5m;
        Filter1hBuffer[i] = filt_1h;
        HBand1hBuffer[i] = hband_1h;
        LBand1hBuffer[i] = lband_1h;
        
        // Original Pine Script FEX_ALL calculation
        FEXAllBuffer[i] = CalcFexAll(i, open, high, low, close);
    }
    
    return(rates_total);
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                      |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Cleanup
}
