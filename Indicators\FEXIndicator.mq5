//+------------------------------------------------------------------+
//| FEXIndicator.mq5                                                 |
//+------------------------------------------------------------------+
#property copyright "Tom"
#property link      ""
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 10
#property indicator_plots   5

#include "../Includes/FEXIndicator.mqh"
#include "../Includes/Constants.mqh"

// Input parameters
input string tf_w = "W";    // Weekly Timeframe
input string tf_d = "D";    // Daily Timeframe
input string tf_4h = "240"; // 4-Hour Timeframe
input string tf_1h = "60";  // 1-Hour Timeframe
input string tf_15m = "15"; // 15-Minute Timeframe

// Indicator buffers
double CurrentFEXBuffer[];
double CurrentOutBoundBuffer[];
double WeeklyFEXBuffer[];
double WeeklyOutBoundBuffer[];
double DailyFEXBuffer[];
double DailyOutBoundBuffer[];
double H4FEXBuffer[];
double H4OutBoundBuffer[];
double H1FEXBuffer[];
double H1OutBoundBuffer[];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                          |
//+------------------------------------------------------------------+
int OnInit() {
    // Set indicator buffers
    SetIndexBuffer(0, CurrentFEXBuffer, INDICATOR_DATA);
    SetIndexBuffer(1, CurrentOutBoundBuffer, INDICATOR_DATA);
    SetIndexBuffer(2, WeeklyFEXBuffer, INDICATOR_DATA);
    SetIndexBuffer(3, WeeklyOutBoundBuffer, INDICATOR_DATA);
    SetIndexBuffer(4, DailyFEXBuffer, INDICATOR_DATA);
    SetIndexBuffer(5, DailyOutBoundBuffer, INDICATOR_DATA);
    SetIndexBuffer(6, H4FEXBuffer, INDICATOR_DATA);
    SetIndexBuffer(7, H4OutBoundBuffer, INDICATOR_DATA);
    SetIndexBuffer(8, H1FEXBuffer, INDICATOR_DATA);
    SetIndexBuffer(9, H1OutBoundBuffer, INDICATOR_DATA);
    
    // Set indicator labels
    PlotIndexSetString(0, PLOT_LABEL, "Current FEX_ALL");
    
    // Hide other plots
    for(int i=1; i<5; i++) {
        PlotIndexSetInteger(i, PLOT_DRAW_TYPE, DRAW_NONE);
    }
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                               |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[]) {
    
    // Calculate from the last uncalculated bar
    int start = prev_calculated == 0 ? 0 : prev_calculated - 1;
    
    // Loop through bars
    for(int i = start; i < rates_total; i++) {
        // Calculate FEX_ALL and OutBound for current timeframe
        double fex_all;
        int out_bound;
        CalcFexAllOutbound(fex_all, out_bound);
        
        CurrentFEXBuffer[i] = fex_all;
        CurrentOutBoundBuffer[i] = out_bound;
        
        // Get FEX_ALL and OutBound from other timeframes using iCustom
        // This would require creating separate indicators for each timeframe
        // or using a more complex approach with CopyBuffer
        
        // For demonstration, we'll just use the current values
        WeeklyFEXBuffer[i] = fex_all;
        WeeklyOutBoundBuffer[i] = out_bound;
        DailyFEXBuffer[i] = fex_all;
        DailyOutBoundBuffer[i] = out_bound;
        H4FEXBuffer[i] = fex_all;
        H4OutBoundBuffer[i] = out_bound;
        H1FEXBuffer[i] = fex_all;
        H1OutBoundBuffer[i] = out_bound;
        
        // Set color based on FEX_ALL value
        PlotIndexSetInteger(0, PLOT_LINE_COLOR, GetFexColor(fex_all));
    }
    
    // Draw signals for high/low conditions
    for(int i = start; i < rates_total; i++) {
        bool high_4H_1H_Condition = H4FEXBuffer[i] >= 92 && H1FEXBuffer[i] > 85;
        bool low_4H_1H_Condition = (H4FEXBuffer[i] <= -90 && H1FEXBuffer[i] < -85) || 
                                  (H4FEXBuffer[i] <= -91 && Symbol() == "XAUUSD");
        
        // Use ObjectCreate to draw arrows for signals
        if(high_4H_1H_Condition) {
            string name = "High_4H_1H_" + IntegerToString(i);
            ObjectCreate(0, name, OBJ_ARROW_DOWN, 0, time[i], High[i] + 10 * Point());
            ObjectSetInteger(0, name, OBJPROP_COLOR, clrPurple);
            ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
        }
        
        if(low_4H_1H_Condition) {
            string name = "Low_4H_1H_" + IntegerToString(i);
            ObjectCreate(0, name, OBJ_ARROW_UP, 0, time[i], Low[i] - 10 * Point());
            ObjectSetInteger(0, name, OBJPROP_COLOR, clrBlue);
            ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
        }
    }
    
    return(rates_total);
}