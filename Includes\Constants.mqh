//+------------------------------------------------------------------+
//| Constants.mqh - <PERSON><PERSON><PERSON> nghĩa các hằng số và màu sắc                |
//+------------------------------------------------------------------+

// M<PERSON>u sắc cho các timeframe
#define COLOR_15MIN  C'0,34,252'    // Blue for 15min
#define COLOR_60MIN  C'0,179,0'     // Green for 1h
#define COLOR_240MIN C'0,0,0'       // Black for 4h
#define COLOR_DAILY  C'255,0,0'     // Red for Daily
#define COLOR_WEEKLY C'255,165,0'   // Orange for Weekly

// M<PERSON>u sắc cho Gaussian Filter
#define COLOR_GAUS_UP   C'10,255,104'  // #0aff68
#define COLOR_GAUS_DN   C'255,10,90'   // #ff0a5a
#define COLOR_DEFAULT   C'204,204,204' // #cccccc

// Các hằng số khác
#define PI 3.14159265358979323846

// Các hằng số cho FEX
#define FEX_PURPLE_THRESHOLD_GOLD 90
#define FEX_PURPLE_THRESHOLD_DEFAULT 95
#define FEX_GREEN_THRESHOLD_GOLD 75
#define FEX_GREEN_THRESHOLD_DEFAULT 60
#define FEX_RED_THRESHOLD_GOLD -80
#define FEX_RED_THRESHOLD_DEFAULT -65

// Các hằng số cho OutBound
#define OUTBOUND_BUY 1
#define OUTBOUND_SELL -1
#define OUTBOUND_NEUTRAL 0

// Các hằng số cho Trend State
#define TREND_STRONG_BUY 1
#define TREND_WEAK_BUY 0.5
#define TREND_STRONG_SELL -1
#define TREND_WEAK_SELL -0.5
#define TREND_NEUTRAL 0