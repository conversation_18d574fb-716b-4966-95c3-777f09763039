# AlphaTrend Multi-Timeframe Indicator for TradingView

A PineScript v5 indicator that displays the AlphaTrend indicator for multiple timeframes (15m, 1h, 4h, Daily, and Weekly) simultaneously on a single chart.

## Features

- Shows AlphaTrend for five different timeframes simultaneously
- Color-coded lines for easy identification:
  - Blue: 15-minute AlphaTrend
  - Green: 1-hour AlphaTrend
  - Red: 4-hour AlphaTrend
  - Magenta: Daily AlphaTrend
  - Orange: Weekly AlphaTrend
- Includes a legend table for easy reference
- Automatically adjusts sensitivity based on timeframe
- Works on any chart timeframe

## How It Works

The AlphaTrend indicator uses a combination of Money Flow Index (MFI) and Average True Range (ATR) to identify trends and potential support/resistance levels. The calculation automatically adjusts its sensitivity based on the timeframe:

- 15-minute: More responsive (factor 4.6)
- 1-hour: Balanced (factor 2.7)
- 4-hour: More stable (factor 1.2)
- Daily: More stable (factor 1.2)
- Weekly: Most stable (factor 1.15)

This makes the indicator more responsive on shorter timeframes and more stable on longer timeframes.

## Installation

1. Open TradingView
2. Go to Pine Editor
3. Copy and paste the code from `my_indicator.pine`
4. Click "Save" and give your indicator a name
5. Click "Add to Chart" to apply it to your current chart

## Usage Tips

- Use the 15-minute and 1-hour lines for short-term trading decisions
- Use the 4-hour and Daily lines for medium-term trend confirmation
- Use the Weekly line for long-term trend direction
- Look for confluence between multiple timeframes for stronger signals

## License

This project is open source and available for personal and commercial use.

