# AlphaTrend Multi-Timeframe Indicator for TradingView

A PineScript v5 indicator that displays the AlphaTrend indicator for multiple timeframes (15m, 1h, and 4h) simultaneously on a single chart.

## Features

- Shows AlphaTrend for 15-minute, 1-hour, and 4-hour timeframes
- Color-coded lines for easy identification:
  - Blue: 15-minute AlphaTrend
  - Green: 1-hour AlphaTrend
  - Red: 4-hour AlphaTrend
- Automatically adjusts sensitivity based on timeframe
- Works on any chart timeframe

## How It Works

The AlphaTrend indicator uses a combination of Money Flow Index (MFI) and Average True Range (ATR) to identify trends and potential support/resistance levels. The calculation automatically adjusts its sensitivity based on the timeframe, making it more responsive on shorter timeframes and more stable on longer timeframes.

## Installation

1. Open TradingView
2. Go to Pine Editor
3. Copy and paste the code from `my_indicator.pine`
4. Click "Save" and give your indicator a name
5. Click "Add to Chart" to apply it to your current chart

## License

This project is open source and available for personal and commercial use.
