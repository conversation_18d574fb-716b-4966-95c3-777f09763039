//+------------------------------------------------------------------+
//| AlphaIndicator.mq5                                               |
//+------------------------------------------------------------------+
#property copyright "Tom"
#property link      ""
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 5
#property indicator_plots   5

#include "../Includes/AlphaCalculator.mqh"
#include "../Includes/Constants.mqh"

// Indicator buffers
double Alpha15Buffer[];
double Alpha60Buffer[];
double Alpha240Buffer[];
double AlphaDailyBuffer[];
double AlphaWeeklyBuffer[];

// Indicator colors
input color Alpha15Color = C'0,34,252';    // 15min Alpha Color
input color Alpha60Color = C'0,179,0';     // 1h Alpha Color
input color Alpha240Color = C'0,0,0';      // 4h Alpha Color
input color AlphaDailyColor = C'255,0,0';  // Daily Alpha Color
input color AlphaWeeklyColor = C'255,165,0'; // Weekly Alpha Color

//+------------------------------------------------------------------+
//| Custom indicator initialization function                          |
//+------------------------------------------------------------------+
int OnInit() {
    // Set indicator buffers
    SetIndexBuffer(0, Alpha15Buffer, INDICATOR_DATA);
    SetIndexBuffer(1, Alpha60Buffer, INDICATOR_DATA);
    SetIndexBuffer(2, Alpha240Buffer, INDICATOR_DATA);
    SetIndexBuffer(3, AlphaDailyBuffer, INDICATOR_DATA);
    SetIndexBuffer(4, AlphaWeeklyBuffer, INDICATOR_DATA);
    
    // Set indicator labels
    PlotIndexSetString(0, PLOT_LABEL, "Alpha 15m");
    PlotIndexSetString(1, PLOT_LABEL, "Alpha 1h");
    PlotIndexSetString(2, PLOT_LABEL, "Alpha 4h");
    PlotIndexSetString(3, PLOT_LABEL, "Alpha D");
    PlotIndexSetString(4, PLOT_LABEL, "Alpha W");
    
    // Set indicator colors
    PlotIndexSetInteger(0, PLOT_LINE_COLOR, Alpha15Color);
    PlotIndexSetInteger(1, PLOT_LINE_COLOR, Alpha60Color);
    PlotIndexSetInteger(2, PLOT_LINE_COLOR, Alpha240Color);
    PlotIndexSetInteger(3, PLOT_LINE_COLOR, AlphaDailyColor);
    PlotIndexSetInteger(4, PLOT_LINE_COLOR, AlphaWeeklyColor);
    
    // Set line widths
    PlotIndexSetInteger(0, PLOT_LINE_WIDTH, 1);
    PlotIndexSetInteger(1, PLOT_LINE_WIDTH, 2);
    PlotIndexSetInteger(2, PLOT_LINE_WIDTH, 2);
    PlotIndexSetInteger(3, PLOT_LINE_WIDTH, 2);
    PlotIndexSetInteger(4, PLOT_LINE_WIDTH, 3);
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                               |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[]) {
    
    // Calculate from the last uncalculated bar
    int start = prev_calculated == 0 ? 0 : prev_calculated - 1;
    
    // Loop through bars
    for(int i = start; i < rates_total; i++) {
        // Calculate Alpha for each timeframe
        Alpha15Buffer[i] = CalcAlpha("15");
        Alpha60Buffer[i] = CalcAlpha("60");
        Alpha240Buffer[i] = CalcAlpha("240");
        AlphaDailyBuffer[i] = CalcAlpha("D");
        AlphaWeeklyBuffer[i] = CalcAlpha("W");
    }
    
    return(rates_total);
}