//+------------------------------------------------------------------+
//| TomCompleteIndicator.mq5                                         |
//+------------------------------------------------------------------+
#property copyright "Tom"
#property link      ""
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 15
#property indicator_plots   10

#include "../Includes/Constants.mqh"
#include "../Includes/GaussianFilter.mqh"
#include "../Includes/AlphaCalculator.mqh"
#include "../Includes/TrendDetector.mqh"
#include "../Includes/FEXIndicator.mqh"
#include "../Includes/CommonFunctions.mqh"

// Input parameters
input bool modeLag_Gaus = false;  // Reduced Lag Mode
input bool modeFast_Gaus = false; // Fast Response Mode
input color Alpha15Color = C'0,34,252';    // 15min Alpha Color
input color Alpha60Color = C'0,179,0';     // 1h Alpha Color
input color Alpha240Color = C'0,0,0';      // 4h Alpha Color
input color AlphaDailyColor = C'255,0,0';  // Daily Alpha Color
input color AlphaWeeklyColor = C'255,165,0'; // Weekly Alpha Color

// Indicator buffers
double Alpha15Buffer[];
double Alpha60Buffer[];
double Alpha240Buffer[];
double AlphaDailyBuffer[];
double AlphaWeeklyBuffer[];

double Filt5mBuffer[];
double Filt1hBuffer[];
double HighBand1hBuffer[];
double LowBand1hBuffer[];

double FEXBuffer[];

// Trend state buffers (for drawing)
double StrongBuyBuffer[];
double WeakBuyBuffer[];
double StrongSellBuffer[];
double WeakSellBuffer[];
double TrendStateBuffer[]; // Combined state

//+------------------------------------------------------------------+
//| Custom indicator initialization function                          |
//+------------------------------------------------------------------+
int OnInit() {
    // Set indicator buffers
    SetIndexBuffer(0, Alpha15Buffer, INDICATOR_DATA);
    SetIndexBuffer(1, Alpha60Buffer, INDICATOR_DATA);
    SetIndexBuffer(2, Alpha240Buffer, INDICATOR_DATA);
    SetIndexBuffer(3, AlphaDailyBuffer, INDICATOR_DATA);
    SetIndexBuffer(4, AlphaWeeklyBuffer, INDICATOR_DATA);
    
    SetIndexBuffer(5, Filt5mBuffer, INDICATOR_DATA);
    SetIndexBuffer(6, Filt1hBuffer, INDICATOR_DATA);
    SetIndexBuffer(7, HighBand1hBuffer, INDICATOR_DATA);
    SetIndexBuffer(8, LowBand1hBuffer, INDICATOR_DATA);
    
    SetIndexBuffer(9, FEXBuffer, INDICATOR_DATA);
    
    SetIndexBuffer(10, StrongBuyBuffer, INDICATOR_DATA);
    SetIndexBuffer(11, WeakBuyBuffer, INDICATOR_DATA);
    SetIndexBuffer(12, StrongSellBuffer, INDICATOR_DATA);
    SetIndexBuffer(13, WeakSellBuffer, INDICATOR_DATA);
    SetIndexBuffer(14, TrendStateBuffer, INDICATOR_DATA);
    
    // Set indicator labels
    PlotIndexSetString(0, PLOT_LABEL, "Alpha 15m");
    PlotIndexSetString(1, PLOT_LABEL, "Alpha 1h");
    PlotIndexSetString(2, PLOT_LABEL, "Alpha 4h");
    PlotIndexSetString(3, PLOT_LABEL, "Alpha D");
    PlotIndexSetString(4, PLOT_LABEL, "Alpha W");
    
    PlotIndexSetString(5, PLOT_LABEL, "5m Filter");
    PlotIndexSetString(6, PLOT_LABEL, "1h Filter");
    PlotIndexSetString(7, PLOT_LABEL, "1h High Band");
    PlotIndexSetString(8, PLOT_LABEL, "1h Low Band");
    
    PlotIndexSetString(9, PLOT_LABEL, "FEX_ALL");
    
    // Set indicator colors
    PlotIndexSetInteger(0, PLOT_LINE_COLOR, Alpha15Color);
    PlotIndexSetInteger(1, PLOT_LINE_COLOR, Alpha60Color);
    PlotIndexSetInteger(2, PLOT_LINE_COLOR, Alpha240Color);
    PlotIndexSetInteger(3, PLOT_LINE_COLOR, AlphaDailyColor);
    PlotIndexSetInteger(4, PLOT_LINE_COLOR, AlphaWeeklyColor);
    
    // Set line widths
    PlotIndexSetInteger(0, PLOT_LINE_WIDTH, 1);
    PlotIndexSetInteger(1, PLOT_LINE_WIDTH, 2);
    PlotIndexSetInteger(2, PLOT_LINE_WIDTH, 2);
    PlotIndexSetInteger(3, PLOT_LINE_WIDTH, 2);
    PlotIndexSetInteger(4, PLOT_LINE_WIDTH, 3);
    
    // Set up trend state plots as arrows
    PlotIndexSetInteger(10, PLOT_ARROW, 233); // Triangle up
    PlotIndexSetInteger(11, PLOT_ARROW, 233); // Triangle up
    PlotIndexSetInteger(12, PLOT_ARROW, 234); // Triangle down
    PlotIndexSetInteger(13, PLOT_ARROW, 234); // Triangle down
    
    PlotIndexSetInteger(10, PLOT_ARROW_SHIFT, -10);
    PlotIndexSetInteger(11, PLOT_ARROW_SHIFT, -10);
    PlotIndexSetInteger(12, PLOT_ARROW_SHIFT, 10);
    PlotIndexSetInteger(13, PLOT_ARROW_SHIFT, 10);
    
    PlotIndexSetInteger(10, PLOT_LINE_COLOR, clrGreen);
    PlotIndexSetInteger(11, PLOT_LINE_COLOR, clrLime);
    PlotIndexSetInteger(12, PLOT_LINE_COLOR, clrRed);
    PlotIndexSetInteger(13, PLOT_LINE_COLOR, clrOrange);
    
    // Set up fill between 1h bands
    PlotIndexSetInteger(7, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(8, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(7, PLOT_LINE_COLOR, Alpha60Color);
    PlotIndexSetInteger(8, PLOT_LINE_COLOR, Alpha60Color);
    PlotIndexSetInteger(7, PLOT_LINE_STYLE, STYLE_DOT);
    PlotIndexSetInteger(8, PLOT_LINE_STYLE, STYLE_DOT);
    
    // Set up FEX_ALL plot
    PlotIndexSetInteger(9, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(9, PLOT_LINE_WIDTH, 2);
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                               |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[]) {
    
    // Calculate from the last uncalculated bar
    int start = prev_calculated == 0 ? 0 : prev_calculated - 1;
    if(start < 0) start = 0;
    
    // Loop through bars
    for(int i = start; i < rates_total; i++) {
        // Calculate Alpha for each timeframe
        Alpha15Buffer[i] = CalcAlpha("15");
        Alpha60Buffer[i] = CalcAlpha("60");
        Alpha240Buffer[i] = CalcAlpha("240");
        AlphaDailyBuffer[i] = CalcAlpha("D");
        AlphaWeeklyBuffer[i] = CalcAlpha("W");
        
        // Calculate Gaussian Filter for different timeframes
        double filt_5m, hband_5m, lband_5m;
        color fcolor_5m;
        double filt_1h, hband_1h, lband_1h;
        color fcolor_1h;
        double filt_1h_prev, hband_1h_prev, lband_1h_prev;
        color fcolor_1h_prev;
        
        CalcGausFilter("5", filt_5m, hband_5m, lband_5m, fcolor_5m, modeLag_Gaus, modeFast_Gaus);
        CalcGausFilter("60", filt_1h, hband_1h, lband_1h, fcolor_1h, modeLag_Gaus, modeFast_Gaus);
        
        // Get previous 1h filter value (for trend calculation)
        if(i > 0) {
            filt_1h_prev = Filt1hBuffer[i-1];
        } else {
            CalcGausFilter("60", filt_1h_prev, hband_1h_prev, lband_1h_prev, fcolor_1h_prev, modeLag_Gaus, modeFast_Gaus);
        }
        
        Filt5mBuffer[i] = filt_5m;
        Filt1hBuffer[i] = filt_1h;
        HighBand1hBuffer[i] = hband_1h;
        LowBand1hBuffer[i] = lband_1h;
        
        // Calculate FEX_ALL
        double fex_all;
        int out_bound;
        CalcFexAllOutbound(fex_all, out_bound);
        FEXBuffer[i] = fex_all;
        
        // Calculate trend states
        bool strongBuy, weakBuy, strongSell, weakSell;
        double close_5m = close[i]; // Use current close as 5m close for simplicity
        
        CalculateTrendStates(filt_1h, filt_1h_prev, filt_5m, Alpha15Buffer[i], Alpha60Buffer[i], close_5m, 
                             strongBuy, weakBuy, strongSell, weakSell);
        
        // Set trend state buffers
        StrongBuyBuffer[i] = strongBuy ? low[i] - 20 * Point() : EMPTY_VALUE;
        WeakBuyBuffer[i] = weakBuy ? low[i] - 20 * Point() : EMPTY_VALUE;
        StrongSellBuffer[i] = strongSell ? high[i] + 20 * Point() : EMPTY_VALUE;
        WeakSellBuffer[i] = weakSell ? high[i] + 20 * Point() : EMPTY_VALUE;
        
        // Set combined trend state (for coloring)
        if(strongBuy) TrendStateBuffer[i] = 1;
        else if(weakBuy) TrendStateBuffer[i] = 0.5;
        else if(strongSell) TrendStateBuffer[i] = -1;
        else if(weakSell) TrendStateBuffer[i] = -0.5;
        else TrendStateBuffer[i] = 0;
        
        // Set FEX_ALL color based on value
        PlotIndexSetInteger(9, PLOT_LINE_COLOR, GetFexColor(fex_all));
    }
    
    // Create legend table on the last bar
    if(rates_total > 0) {
        CreateLegendTable(time[rates_total-1]);
    }
    
    return(rates_total);
}

//+------------------------------------------------------------------+
//| Create legend table                                               |
//+------------------------------------------------------------------+
void CreateLegendTable(datetime lastBarTime) {
    // Remove old objects
    ObjectsDeleteAll(0, "TomLegend_");
    
    // Create table background
    string tableName = "TomLegend_Table";
    if(!ObjectCreate(0, tableName, OBJ_RECTANGLE_LABEL, 0, 0, 0)) {
        Print("Failed to create legend table: ", GetLastError());
        return;
    }
    
    ObjectSetInteger(0, tableName, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
    ObjectSetInteger(0, tableName, OBJPROP_XDISTANCE, 10);
    ObjectSetInteger(0, tableName, OBJPROP_YDISTANCE, 10);
    ObjectSetInteger(0, tableName, OBJPROP_XSIZE, 150);
    ObjectSetInteger(0, tableName, OBJPROP_YSIZE, 220);
    ObjectSetInteger(0, tableName, OBJPROP_BGCOLOR, clrBlack);
    ObjectSetInteger(0, tableName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, tableName, OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, tableName, OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, tableName, OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, tableName, OBJPROP_BACK, false);
    ObjectSetInteger(0, tableName, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, tableName, OBJPROP_SELECTED, false);
    ObjectSetInteger(0, tableName, OBJPROP_HIDDEN, true);
    ObjectSetInteger(0, tableName, OBJPROP_ZORDER, 0);
    
    // Add title
    string titleName = "TomLegend_Title";
    if(!ObjectCreate(0, titleName, OBJ_LABEL, 0, 0, 0)) {
        Print("Failed to create legend title: ", GetLastError());
        return;
    }
    
    ObjectSetInteger(0, titleName, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
    ObjectSetInteger(0, titleName, OBJPROP_XDISTANCE, 75);
    ObjectSetInteger(0, titleName, OBJPROP_YDISTANCE, 20);
    ObjectSetString(0, titleName, OBJPROP_TEXT, "Tom Strategy");
    ObjectSetString(0, titleName, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, titleName, OBJPROP_FONTSIZE, 10);
    ObjectSetInteger(0, titleName, OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, titleName, OBJPROP_SELECTABLE, false);
    
    // Add timeframe colors
    AddLegendItem("TomLegend_15m", "15m", Alpha15Color, 40);
    AddLegendItem("TomLegend_1h", "1h", Alpha60Color, 60);
    AddLegendItem("TomLegend_4h", "4h", Alpha240Color, 80);
    AddLegendItem("TomLegend_D", "D", AlphaDailyColor, 100);
    AddLegendItem("TomLegend_W", "W", AlphaWeeklyColor, 120);
    
    // Add trend state
    string trendText = "";
    color trendColor = clrWhite;
    
    if(TrendStateBuffer[ArraySize(TrendStateBuffer)-1] == 1) {
        trendText = "Strong Buy";
        trendColor = clrGreen;
    }
    else if(TrendStateBuffer[ArraySize(TrendStateBuffer)-1] == 0.5) {
        trendText = "Weak Buy";
        trendColor = clrLime;
    }
    else if(TrendStateBuffer[ArraySize(TrendStateBuffer)-1] == -1) {
        trendText = "Strong Sell";
        trendColor = clrRed;
    }
    else if(TrendStateBuffer[ArraySize(TrendStateBuffer)-1] == -0.5) {
        trendText = "Weak Sell";
        trendColor = clrOrange;
    }
    else {
        trendText = "Neutral";
        trendColor = clrWhite;
    }
    
    AddLegendItem("TomLegend_Trend", "Trend: " + trendText, trendColor, 150);
    
    // Add FEX_ALL value
    double fex_all = FEXBuffer[ArraySize(FEXBuffer)-1];
    AddLegendItem("TomLegend_FEX", "FEX: " + DoubleToString(fex_all, 2), GetFexColor(fex_all), 170);
}

//+------------------------------------------------------------------+
//| Add legend item                                                   |
//+------------------------------------------------------------------+
void AddLegendItem(string name, string text, color itemColor, int yPos) {
    if(!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0)) {
        Print("Failed to create legend item: ", GetLastError());
        return;
    }
    
    ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
    ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 75);
    ObjectSetInteger(0, name, OBJPROP_YDISTANCE, yPos);
    ObjectSetString(0, name, OBJPROP_TEXT, text);
    ObjectSetString(0, name, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 8);
    ObjectSetInteger(0, name, OBJPROP_COLOR, itemColor);
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
}