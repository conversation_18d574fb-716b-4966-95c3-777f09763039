//+------------------------------------------------------------------+
//| TimeframeUtils.mqh - Xử lý các timeframe                         |
//+------------------------------------------------------------------+
#include "Constants.mqh"

//+------------------------------------------------------------------+
//| Kiểm tra timeframe hiện tại                                      |
//+------------------------------------------------------------------+
bool IsTimeframe(ENUM_TIMEFRAMES tf) {
    return Period() == tf;
}

//+------------------------------------------------------------------+
//| Lấy giá trị period cho các tham số dựa trên timeframe            |
//+------------------------------------------------------------------+
int GetBaseCloudEmaLength() {
    if(IsTimeframe(PERIOD_MN1)) return 60;
    if(IsTimeframe(PERIOD_W1))  return 52;
    if(IsTimeframe(PERIOD_D1))  return 66;
    if(IsTimeframe(PERIOD_H4))  return 60;
    if(IsTimeframe(PERIOD_H1))  return 45;
    if(IsTimeframe(PERIOD_M15)) return 179;
    return 122;
}

//+------------------------------------------------------------------+
//| Lấy giá trị period cho Gaussian Filter                           |
//+------------------------------------------------------------------+
int GetGausPeriod() {
    if(IsTimeframe(PERIOD_MN1)) return 72;
    if(IsTimeframe(PERIOD_W1))  return 60;
    if(IsTimeframe(PERIOD_D1))  return 150;
    if(IsTimeframe(PERIOD_H4))  return 111;
    if(IsTimeframe(PERIOD_H1))  return 186;
    if(IsTimeframe(PERIOD_M15)) return 211;
    if(IsTimeframe(PERIOD_M5))  return 369;
    if(IsTimeframe(PERIOD_M3))  return 500;
    if(IsTimeframe(PERIOD_M1))  return 1500;
    return 222;
}

//+------------------------------------------------------------------+
//| Lấy giá trị multiplier cho Gaussian Filter                       |
//+------------------------------------------------------------------+
double GetGausMultiplier() {
    if(IsTimeframe(PERIOD_MN1)) return 1.414;
    if(IsTimeframe(PERIOD_W1))  return 1.414;
    if(IsTimeframe(PERIOD_D1))  return 1.6;
    if(IsTimeframe(PERIOD_H4))  return 2.39;
    if(IsTimeframe(PERIOD_H1))  return 2.69;
    if(IsTimeframe(PERIOD_M15)) return 2.69;
    if(IsTimeframe(PERIOD_M5))  return 5.39;
    if(IsTimeframe(PERIOD_M3))  return 6.39;
    if(IsTimeframe(PERIOD_M1))  return 6.39;
    return 1.414;
}

//+------------------------------------------------------------------+
//| Lấy giá trị factor alpha dựa trên timeframe                      |
//+------------------------------------------------------------------+
double GetAlphaFactor() {
    if(IsTimeframe(PERIOD_W1))  return 2.79;
    if(IsTimeframe(PERIOD_D1))  return 3.69;
    if(IsTimeframe(PERIOD_H4))  return 3.369;
    if(IsTimeframe(PERIOD_H1))  return 2.8;
    if(IsTimeframe(PERIOD_M15)) return 4.963;
    return 3.0;
}

//+------------------------------------------------------------------+
//| Chuyển đổi ENUM_TIMEFRAMES sang string                           |
//+------------------------------------------------------------------+
string TimeframeToString(ENUM_TIMEFRAMES tf) {
    switch(tf) {
        case PERIOD_M1:  return "1";
        case PERIOD_M3:  return "3";
        case PERIOD_M5:  return "5";
        case PERIOD_M15: return "15";
        case PERIOD_M30: return "30";
        case PERIOD_H1:  return "60";
        case PERIOD_H4:  return "240";
        case PERIOD_D1:  return "D";
        case PERIOD_W1:  return "W";
        case PERIOD_MN1: return "MN";
        default:         return "Current";
    }
}

//+------------------------------------------------------------------+
//| Kiểm tra xem symbol có phải là XAUUSD (Gold)                     |
//+------------------------------------------------------------------+
bool IsGold() {
    return StringFind(Symbol(), "XAUUSD") >= 0 || StringFind(Symbol(), "GOLD") >= 0;
}

//+------------------------------------------------------------------+
//| Kiểm tra xem symbol có phải là BTCUSD (Bitcoin)                  |
//+------------------------------------------------------------------+
bool IsBitcoin() {
    return StringFind(Symbol(), "BTCUSD") >= 0 || StringFind(Symbol(), "BTC") >= 0;
}