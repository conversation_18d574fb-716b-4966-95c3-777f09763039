//+------------------------------------------------------------------+
//| AlphaCalculator.mqh - Tính toán Alpha                            |
//+------------------------------------------------------------------+
#include "Constants.mqh"
#include "CommonFunctions.mqh"

// Mảng lưu trữ giá trị alpha cho mỗi timeframe
double alpha_values[10]; // Lưu trữ giá trị alpha cho mỗi timeframe

//+------------------------------------------------------------------+
//| T<PERSON>h toán Alpha cho timeframe cụ thể                             |
//+------------------------------------------------------------------+
double CalcAlpha(string tf) {
    // Thiết lập factor_alpha dựa trên timeframe
    double factor_alpha = 3.0;
    
    if(tf == "W") factor_alpha = 2.79;
    else if(tf == "D") factor_alpha = 3.69;
    else if(tf == "240") factor_alpha = 3.369;
    else if(tf == "60") factor_alpha = 2.8;
    else if(tf == "15") factor_alpha = 4.963;
    
    // Lấy dữ liệu từ timeframe cụ thể
    ENUM_TIMEFRAMES timeframe = StringToTimeFrame(tf);
    int tf_index = (int)timeframe; // Sử dụng timeframe làm index cho mảng alpha_values
    
    double high_array[], low_array[], close_array[];
    ArraySetAsSeries(high_array, true);
    ArraySetAsSeries(low_array, true);
    ArraySetAsSeries(close_array, true);
    
    CopyHigh(Symbol(), timeframe, 0, 2, high_array);
    CopyLow(Symbol(), timeframe, 0, 2, low_array);
    CopyClose(Symbol(), timeframe, 0, 2, close_array);
    
    double tfHigh = high_array[0];
    double tfLow = low_array[0];
    double tfClose = close_array[0];
    double tfHLC3 = (tfHigh + tfLow + tfClose) / 3;
    
    // Tính True Range cho timeframe
    double tr_array[2];
    tr_array[0] = MathMax(tfHigh, close_array[1]) - MathMin(tfLow, close_array[1]);
    
    // Tính SMA của True Range
    double tr_sma_array[];
    ArraySetAsSeries(tr_sma_array, true);
    int tr_handle = iMA(Symbol(), timeframe, 69, 0, MODE_SMA, PRICE_TYPICAL);
    CopyBuffer(tr_handle, 0, 0, 1, tr_sma_array);
    double tfTR_SMA = tr_sma_array[0];
    
    // Tính MFI cho timeframe
    double mfi_array[];
    ArraySetAsSeries(mfi_array, true);
    int mfi_handle = iMFI(Symbol(), timeframe, 69, VOLUME_TICK);
    CopyBuffer(mfi_handle, 0, 0, 1, mfi_array);
    double tfMFI = mfi_array[0];
    
    // Tính upT_S và downT_S
    double upT_S = tfLow - tfTR_SMA * factor_alpha;
    double downT_S = tfHigh + tfTR_SMA * factor_alpha;
    
    // Cập nhật alpha dựa trên điều kiện
    if(tfMFI >= 50) {
        alpha_values[tf_index] = upT_S < alpha_values[tf_index] ? alpha_values[tf_index] : upT_S;
    } else {
        alpha_values[tf_index] = downT_S > alpha_values[tf_index] ? alpha_values[tf_index] : downT_S;
    }
    
    return alpha_values[tf_index];
}

//+------------------------------------------------------------------+
//| Tính toán Alpha một cách nhất quán trong timeframe               |
//+------------------------------------------------------------------+
double CalcConsistentAlpha(string tf) {
    return CalcAlpha(tf);
}