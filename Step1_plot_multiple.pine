// @version=5
indicator("alpha Multi-Timeframe", overlay=true)

// Define colors for each timeframe
color_15min = color.new(#0022FC, 0)    // Blue for 15min
color_60min = color.new(#00B300, 0)    // Green for 1h
color_240min = color.new(#FF0000, 0)   // Red for 4h
color_daily = color.new(#000000, 0)    // Black for Daily
color_weekly = color.new(#FFA500, 0)   // Orange for Weekly

// Function to calculate alpha for a specific timeframe
calcAlpha(tf) =>
    // Get data from the specified timeframe
    factor_alpha = tf == "D" ? 1.2 : 
                     tf == "W" ? 1.15 : 
                     tf == "240" ? 1.2 : 
                     tf == "60" ? 2.7 : 
                     tf == "15" ? 4.6 : 3
    
    // Request security data for the specified timeframe
    [tfHigh, tfLow, tfClose] = request.security(syminfo.tickerid, tf, [high, low, close], lookahead=barmerge.lookahead_off)
    tfHLC3 = (tfHigh + tfLow + tfClose) / 3
    
    // Calculate True Range for the timeframe
    tfTR = request.security(syminfo.tickerid, tf, ta.tr, lookahead=barmerge.lookahead_off)
    tfTR_SMA = request.security(syminfo.tickerid, tf, ta.sma(ta.tr, 69), lookahead=barmerge.lookahead_off)
    
    // Calculate MFI for the timeframe
    tfMFI = request.security(syminfo.tickerid, tf, ta.mfi(hlc3, 69), lookahead=barmerge.lookahead_off)
    
    // Calculate upT_S and downT_S
    upT_S = tfLow - tfTR_SMA * factor_alpha
    downT_S = tfHigh + tfTR_SMA * factor_alpha
    
    // Initialize alpha
    var float alpha = 0.0
    
    // Update alpha based on conditions
    alpha := tfMFI >= 50 ? 
                 upT_S < nz(alpha[1]) ? nz(alpha[1]) : upT_S : 
                 downT_S > nz(alpha[1]) ? nz(alpha[1]) : downT_S
    
    alpha

// Calculate alpha for each timeframe
alpha15 = calcAlpha("15")
alpha60 = calcAlpha("60")
alpha240 = calcAlpha("240")
alphaDaily = calcAlpha("D")
alphaWeekly = calcAlpha("W")

// Plot all timeframes simultaneously
plot(alpha15, title="Alpha 15m", color=color_15min, linewidth=2)
plot(alpha60, title="Alpha 1h", color=color_60min, linewidth=2)
plot(alpha240, title="Alpha 4h", color=color_240min, linewidth=2)
// plot(alphaDaily, title="Alpha D", color=color_daily, linewidth=2)
plot(alphaWeekly, title="Alpha W", color=color_weekly, linewidth=3)

// Add a table to show which color corresponds to which timeframe and display alpha values
if (barstate.islast)
    var table legendTable = table.new(position.top_right, 2, 6, bgcolor=color.new(color.black, 70), border_width=1)
    table.cell(legendTable, 0, 0, "alpha Timeframes", bgcolor=color.new(color.black, 70), text_color=color.white)
    table.cell(legendTable, 1, 0, "Value", bgcolor=color.new(color.black, 70), text_color=color.white)
    
    table.cell(legendTable, 0, 1, "15 min", bgcolor=color.new(color.black, 90), text_color=color_15min)
    table.cell(legendTable, 1, 1, str.tostring(alpha15, "#.##"), bgcolor=color.new(color.black, 90), text_color=color_15min)
    
    table.cell(legendTable, 0, 2, "1 hour", bgcolor=color.new(color.black, 90), text_color=color_60min)
    table.cell(legendTable, 1, 2, str.tostring(alpha60, "#.##"), bgcolor=color.new(color.black, 90), text_color=color_60min)
    
    table.cell(legendTable, 0, 3, "4 hour", bgcolor=color.new(color.black, 90), text_color=color_240min)
    table.cell(legendTable, 1, 3, str.tostring(alpha240, "#.##"), bgcolor=color.new(color.black, 90), text_color=color_240min)
    
    //table.cell(legendTable, 0, 4, "Daily", bgcolor=color.new(color.black, 90), text_color=color_daily)
    //table.cell(legendTable, 1, 4, str.tostring(alphaDaily, "#.##"), bgcolor=color.new(color.black, 90), text_color=color_daily)
    
   // table.cell(legendTable, 0, 5, "Weekly", bgcolor=color.new(color.black, 90), text_color=color_weekly)
   // table.cell(legendTable, 1, 5, str.tostring(alphaWeekly, "#.##"), bgcolor=color.new(color.black, 90), text_color=color_weekly)