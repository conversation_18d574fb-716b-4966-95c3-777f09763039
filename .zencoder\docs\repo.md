# Repository Information Overview

## Repository Summary

This repository contains two main components: a MetaTrader 5 (MT5) trading strategy implementation and a Visual Studio Code extension called "Code Context Whisperer".

## Repository Structure

- **Experts/**: Contains MT5 Expert Advisors (trading robots)
- **Includes/**: Contains MQL5 include files with utility functions
- **Indicators/**: Contains MT5 technical indicators
- **code-context-whisperer/**: VS Code extension project

## Projects

### MetaTrader 5 Trading Strategy

**Configuration Files**: strategy_mq5.mq5, Experts/TomStrategy.mq5

#### Language & Runtime

**Language**: MQL5 (MetaQuotes Language 5)
**Version**: MQL5 compatible with MetaTrader 5
**Build System**: MetaEditor (MT5 IDE)

#### Main Components

- **AlphaTrend Indicator**: Multi-timeframe indicator showing trend direction
- **Gaussian Filter**: Advanced filtering algorithm for price smoothing
- **TomStrategy**: Expert Advisor implementing the trading strategy

#### Structure

- **Indicators/**: Contains indicator implementations
  - AlphaIndicator.mq5
  - FEXIndicator.mq5
  - GaussianFilterIndicator.mq5
  - TomCompleteIndicator.mq5
- **Includes/**: Contains utility functions and shared code
  - AlphaCalculator.mqh
  - CommonFunctions.mqh
  - Constants.mqh
  - FEXIndicator.mqh
  - GaussianFilter.mqh
  - TimeframeUtils.mqh
  - TrendDetector.mqh
- **Experts/**: Contains trading robots
  - TomStrategy.mq5

#### Usage & Operations

The strategy uses a combination of indicators:

- AlphaTrend for trend direction across multiple timeframes
- Gaussian Filter for price smoothing and noise reduction
- FEX indicator for momentum detection

Trading signals are generated based on:

- Trend direction from multiple timeframes
- Price position relative to Gaussian Filter
- FEX momentum indicator values

#### Build & Installation

```bash
# Compile in MetaEditor or copy files to MT5 data folder:
# - Copy .mq5 files to [MT5_DATA_FOLDER]/MQL5/Experts/
# - Copy .mqh files to [MT5_DATA_FOLDER]/MQL5/Include/
# - Copy indicator files to [MT5_DATA_FOLDER]/MQL5/Indicators/
```

### Code Context Whisperer (VS Code Extension)

**Configuration File**: code-context-whisperer/package.json

#### Language & Runtime

**Language**: TypeScript
**Version**: TypeScript 4.3.x
**Build System**: npm
**Package Manager**: npm

#### Dependencies

**Main Dependencies**:

- VS Code Extension API (^1.60.0)

**Development Dependencies**:

- TypeScript (^4.3.2)
- ESLint (^7.27.0)
- Mocha (^8.4.0)
- VS Code Test (^1.5.2)

#### Build & Installation

```bash
# Install dependencies
cd code-context-whisperer
npm install

# Build extension
npm run compile

# Package extension
npm run package
```

#### Features

- Displays context information when hovering over variables, functions, or classes
- Searches for definitions and usages within the project
- Premium features with AI and data synchronization (unlockable)

#### Testing

**Framework**: Mocha
**Test Location**: Not explicitly defined, likely in standard VS Code extension test structure
**Run Command**:

```bash
npm test
```
