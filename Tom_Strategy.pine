#property copyright "TOMBOBAP 2024"
#property version   "3.80"
#property indicator_chart_window
#property indicator_buffers 1
#property indicator_plots   1
#property indicator_label1  "Tom Gaussian Filter 1H"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrMagenta
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2
#property description       "Tom's Custom Gaussian Filter Implementation"
#include <Trade/Trade.mqh>
#include <Indicators/Trend.mqh>

// Input parameters for Gaussian filter
input int GausPeriod = 186;        // Gaussian Filter Period (default for 1H)
input bool ReducedLagMode = false; // Enable Reduced Lag Mode
input bool FastResponseMode = false; // Enable Fast Response Mode

CTrade trade;
ulong posticket;

//--- Indicator Handles
int BuySignal = 0;
int SellSignal = 0;
bool BuySell = true;  // Direction flag: true for Buy bias, false for Sell bias

//--- Trade history tracking
bool lastTradeLoss = false;  // Flag to track if the last trade was a loss
double currentLotSize = 0.0; // Current lot size to maintain after losses

//--- Gaussian Filter Variables
int gaus_period_1h;        // Period for 1h timeframe from Pine script
double gaus_alpha_1h;      // Alpha value calculated from beta
double gaus_beta_1h;       // Beta value calculated from period
bool modeLag_Gaus;         // Reduced Lag Mode
bool modeFast_Gaus;        // Fast Response Mode

//--- Indicator buffer for plotting
double GaussianBuffer[];
double GaussianFilterValues[];  // Array to store calculated Gaussian filter values
double PriceBuffer[];           // Array to store price data for calculations

//--- Function to calculate dynamic lot size based on account balance
double GetLotSize()
{
   // If the last trade was a loss, maintain the same lot size
   if(lastTradeLoss && currentLotSize > 0)
   {
      return currentLotSize;
   }
   
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE); // Get the account balance
   double minLot = 0.05;  // Set the minimum lot size
   double lotStep = 0.05; // Lot size increment for each $1000 increase
   double calculatedLot;
   
   // Set lot size based on balance
   if (accountBalance < 2000)
      calculatedLot = minLot;  // For balances below $2000, use minimum lot size
   else if (accountBalance >= 2000 && accountBalance < 3000)
      calculatedLot = minLot;  // For balances between $2000 and $3000, use 0.10 lots
   else if (accountBalance >= 3000 && accountBalance < 4000)
      calculatedLot = minLot;  // For balances between $3000 and $4000, use 0.15 lots
   else
      calculatedLot = minLot;  // Increase lot size by 0.05 for every $1000 beyond $2000
   
   // Store the calculated lot size for future reference
   currentLotSize = calculatedLot;
   return calculatedLot;
}

//--- Function to calculate Gaussian filter with 9 poles (similar to Pine script)
double Filt9xGaus(double alpha, double source, int pole, int shift)
{
   // Safety check for invalid pole value
   if(pole < 1 || pole > 9)
   {
      Print("Warning: Invalid pole value ", pole, " in Filt9xGaus. Using pole=4 instead.");
      pole = 4; // Default to 4 poles
   }
   
   // Safety check for shift value
   if(shift < 0)
   {
      Print("Warning: Negative shift value ", shift, " in Filt9xGaus. Using shift=0 instead.");
      shift = 0;
   }
   
   // Ensure GaussianFilterValues array is properly sized
   int requiredSize = shift + 10; // Need at least shift+9 elements
   if(ArraySize(GaussianFilterValues) < requiredSize)
   {
      ArrayResize(GaussianFilterValues, requiredSize);
   }
   
   // Weights for different poles (from Pine script)
   int m2 = 0, m3 = 0, m4 = 0, m5 = 0, m6 = 0, m7 = 0, m8 = 0, m9 = 0;
   double result = 0.0;
   double x = 1 - alpha;
   
   // Set weights based on pole number (matching Pine script logic)
   switch(pole)
   {
      case 9:
         m2 = 36; m3 = 84; m4 = 126; m5 = 126; m6 = 84; m7 = 36; m8 = 9; m9 = 1;
         break;
      case 8:
         m2 = 28; m3 = 56; m4 = 70; m5 = 56; m6 = 28; m7 = 8; m8 = 1;
         break;
      case 7:
         m2 = 21; m3 = 35; m4 = 35; m5 = 21; m6 = 7; m7 = 1;
         break;
      case 6:
         m2 = 15; m3 = 20; m4 = 15; m5 = 6; m6 = 1;
         break;
      case 5:
         m2 = 10; m3 = 10; m4 = 5; m5 = 1;
         break;
      case 4:
         m2 = 6; m3 = 4; m4 = 1;
         break;
      case 3:
         m2 = 3; m3 = 1;
         break;
      case 2:
         m2 = 1;
         break;
      default:
         // Should never reach here due to earlier check
         break;
   }
   
   // Get previous values (with safety checks)
   double prev1 = shift+1 < ArraySize(GaussianFilterValues) ? GaussianFilterValues[shift+1] : 0;
   double prev2 = shift+2 < ArraySize(GaussianFilterValues) ? GaussianFilterValues[shift+2] : 0;
   double prev3 = shift+3 < ArraySize(GaussianFilterValues) ? GaussianFilterValues[shift+3] : 0;
   double prev4 = shift+4 < ArraySize(GaussianFilterValues) ? GaussianFilterValues[shift+4] : 0;
   double prev5 = shift+5 < ArraySize(GaussianFilterValues) ? GaussianFilterValues[shift+5] : 0;
   double prev6 = shift+6 < ArraySize(GaussianFilterValues) ? GaussianFilterValues[shift+6] : 0;
   double prev7 = shift+7 < ArraySize(GaussianFilterValues) ? GaussianFilterValues[shift+7] : 0;
   double prev8 = shift+8 < ArraySize(GaussianFilterValues) ? GaussianFilterValues[shift+8] : 0;
   double prev9 = shift+9 < ArraySize(GaussianFilterValues) ? GaussianFilterValues[shift+9] : 0;
   
   // Calculate filter using the formula from Pine script
   result = MathPow(alpha, pole) * source + 
            pole * x * prev1 - 
            (pole >= 2 ? m2 * MathPow(x, 2) * prev2 : 0) + 
            (pole >= 3 ? m3 * MathPow(x, 3) * prev3 : 0) - 
            (pole >= 4 ? m4 * MathPow(x, 4) * prev4 : 0) + 
            (pole >= 5 ? m5 * MathPow(x, 5) * prev5 : 0) - 
            (pole >= 6 ? m6 * MathPow(x, 6) * prev6 : 0) + 
            (pole >= 7 ? m7 * MathPow(x, 7) * prev7 : 0) - 
            (pole >= 8 ? m8 * MathPow(x, 8) * prev8 : 0) + 
            (pole == 9 ? m9 * MathPow(x, 9) * prev9 : 0);
   
   return result;
}

//--- Function to calculate pole-based Gaussian filter
void CalculatePoleGaus(double alpha, double source, int poles, int shift, double &result)
{
   // Safety check for invalid poles value
   if(poles < 1 || poles > 9)
   {
      Print("Warning: Invalid poles value ", poles, " in CalculatePoleGaus. Using poles=4 instead.");
      poles = 4; // Default to 4 poles
   }
   
   // Safety check for shift value
   if(shift < 0)
   {
      Print("Warning: Negative shift value ", shift, " in CalculatePoleGaus. Using shift=0 instead.");
      shift = 0;
   }
   
   // Ensure GaussianFilterValues array is properly sized
   int requiredSize = shift + 1;
   if(ArraySize(GaussianFilterValues) < requiredSize)
   {
      ArrayResize(GaussianFilterValues, MathMax(requiredSize, ArraySize(GaussianFilterValues) + 100));
   }
   
   double f1 = Filt9xGaus(alpha, source, 1, shift);
   double f2 = poles >= 2 ? Filt9xGaus(alpha, source, 2, shift) : 0;
   double f3 = poles >= 3 ? Filt9xGaus(alpha, source, 3, shift) : 0;
   double f4 = poles >= 4 ? Filt9xGaus(alpha, source, 4, shift) : 0;
   double f5 = poles >= 5 ? Filt9xGaus(alpha, source, 5, shift) : 0;
   double f6 = poles >= 6 ? Filt9xGaus(alpha, source, 6, shift) : 0;
   double f7 = poles >= 7 ? Filt9xGaus(alpha, source, 7, shift) : 0;
   double f8 = poles >= 8 ? Filt9xGaus(alpha, source, 8, shift) : 0;
   double f9 = poles == 9 ? Filt9xGaus(alpha, source, 9, shift) : 0;
   
   // Select the appropriate filter based on the number of poles
   switch(poles)
   {
      case 1: result = f1; break;
      case 2: result = f2; break;
      case 3: result = f3; break;
      case 4: result = f4; break;
      case 5: result = f5; break;
      case 6: result = f6; break;
      case 7: result = f7; break;
      case 8: result = f8; break;
      case 9: result = f9; break;
      default: result = f4; // Default to 4 poles as in the Pine script
   }
   
   // Store the calculated value for future reference (with safety check)
   if(shift < ArraySize(GaussianFilterValues))
   {
      GaussianFilterValues[shift] = result;
   }
   else
   {
      Print("Warning: Failed to store Gaussian filter value at index ", shift, ". Array size is ", ArraySize(GaussianFilterValues));
   }
}

//--- Function to calculate Gaussian filter value (main function)
double CalculateGaussianFilter(const double &price[], int period, int bars)
{
   // Safety check for period
   if(period <= 0)
   {
      Print("Warning: Invalid period value ", period, " in CalculateGaussianFilter. Using period=186 instead.");
      period = 186; // Default to 186 for 1H timeframe
   }
   
   // Safety check for price array
   if(ArraySize(price) <= 0)
   {
      Print("Warning: Empty price array in CalculateGaussianFilter.");
      return 0.0;
   }
   
   // Calculate beta and alpha components (from Pine script)
   double beta = (1 - MathCos(4 * M_PI / period)) / (MathSqrt(1.414) - 1);
   double alpha = -beta + MathSqrt(MathPow(beta, 2) + 2 * beta);
   
   // Calculate lag
   double lag = (period - 1) / (2 * 4);
   int lagIndex = (int)lag;
   
   // Safety check for lag index
   if(lagIndex < 0) lagIndex = 0;
   if(lagIndex >= ArraySize(price)) lagIndex = ArraySize(price) - 1;
   
   // Prepare source data with lag reduction if enabled
   double srcdata = 0;
   if(modeLag_Gaus && bars > lagIndex && lagIndex < ArraySize(price))
   {
      // Apply lag reduction formula from Pine script
      srcdata = price[0] + price[0] - price[lagIndex];
   }
   else
   {
      srcdata = price[0]; // Use current price if lag reduction is disabled or not enough bars
   }
   
   // Calculate filter with 4 poles (as in Pine script)
   double filtn = 0;
   double filt1 = 0;
   
   // Ensure arrays are properly sized
   int requiredSize = MathMax(bars + 10, 100);
   if(ArraySize(GaussianFilterValues) < requiredSize)
   {
      ArrayResize(GaussianFilterValues, requiredSize);
   }
   
   // Calculate the filter
   CalculatePoleGaus(alpha, srcdata, 4, 0, filtn);
   CalculatePoleGaus(alpha, srcdata, 1, 0, filt1);
   
   // Apply fast response mode if enabled
   double filter = modeFast_Gaus ? (filtn + filt1) / 2 : filtn;
   
   return filter;
}

//--- OnInit function to initialize the indicators
int OnInit()
{
   // Set up indicator buffer for plotting
   SetIndexBuffer(0, GaussianBuffer, INDICATOR_DATA);
   
   // Set drawing properties
   PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_LINE);
   PlotIndexSetInteger(0, PLOT_LINE_COLOR, clrMagenta);
   PlotIndexSetInteger(0, PLOT_LINE_STYLE, STYLE_SOLID);
   PlotIndexSetInteger(0, PLOT_LINE_WIDTH, 2);
   
   // Set Gaussian filter parameters from inputs
   gaus_period_1h = GausPeriod;
   modeLag_Gaus = ReducedLagMode;
   modeFast_Gaus = FastResponseMode;
   
   // Calculate beta and alpha for 1h timeframe
   gaus_beta_1h = (1 - MathCos(4 * M_PI / gaus_period_1h)) / (MathSqrt(1.414) - 1);
   gaus_alpha_1h = -gaus_beta_1h + MathSqrt(MathPow(gaus_beta_1h, 2) + 2 * gaus_beta_1h);
   
   // Initialize arrays for Gaussian filter calculations
   int bars = Bars(_Symbol, PERIOD_CURRENT);
   ArrayResize(GaussianFilterValues, bars + 100); // Add extra space for safety
   ArrayResize(PriceBuffer, bars + 100);
   ArrayResize(GaussianBuffer, bars + 100); // Make sure GaussianBuffer is also properly sized
   ArrayInitialize(GaussianFilterValues, 0);
   ArrayInitialize(PriceBuffer, 0);
   ArrayInitialize(GaussianBuffer, 0);
   
   // We'll use our own custom Gaussian filter calculation
   // No need for external indicator handles or MA
   
   // Set indicator properties with a unique name to avoid confusion with built-in indicators
   string indicatorName = "Tom Gaussian Filter 1H (" + IntegerToString(gaus_period_1h) + ")";
   PlotIndexSetString(0, PLOT_LABEL, indicatorName);
   PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_LINE);
   PlotIndexSetInteger(0, PLOT_LINE_COLOR, clrMagenta);
   PlotIndexSetInteger(0, PLOT_LINE_STYLE, STYLE_SOLID);
   PlotIndexSetInteger(0, PLOT_LINE_WIDTH, 2);
   
   // Set short name for the indicator
   IndicatorSetString(INDICATOR_SHORTNAME, indicatorName);
   
   // Initialize the filter with some historical data
   double close_H1[];
   ArrayResize(close_H1, 100);
   
   if(CopyClose(_Symbol, PERIOD_H1, 0, 100, close_H1) > 0)
   {
      // Pre-calculate some historical values to improve initial accuracy
      for(int i = 99; i >= 0; i--)
      {
         // Use typical price (OHLC4) for calculation
         PriceBuffer[i] = close_H1[i]; // Simplified, ideally would be OHLC4
         
         // Calculate and store the Gaussian filter value
         int lagIndex = (int)((gaus_period_1h - 1) / (2 * 4));
         
         // Make sure lag index is valid
         if(lagIndex < 0) lagIndex = 0;
         if(lagIndex > 99) lagIndex = 99;
         
         double srcdata = modeLag_Gaus ? 
                         PriceBuffer[i] + PriceBuffer[i] - PriceBuffer[MathMin(i + lagIndex, 99)] : 
                         PriceBuffer[i];
         double filtn = 0;
         double filt1 = 0;
         
         CalculatePoleGaus(gaus_alpha_1h, srcdata, 4, i, filtn);
         CalculatePoleGaus(gaus_alpha_1h, srcdata, 1, i, filt1);
         
         GaussianBuffer[i] = modeFast_Gaus ? (filtn + filt1) / 2 : filtn;
      }
   }
   
   Print("Tom Gaussian Filter initialized with period: ", gaus_period_1h, 
         ", alpha: ", gaus_alpha_1h, 
         ", Reduced Lag Mode: ", modeLag_Gaus ? "Enabled" : "Disabled", 
         ", Fast Response Mode: ", modeFast_Gaus ? "Enabled" : "Disabled");
         
   // Print buffer information for debugging
   Print("GaussianBuffer size: ", ArraySize(GaussianBuffer));
   Print("GaussianFilterValues size: ", ArraySize(GaussianFilterValues));
   
   // Print some initial values
   if(ArraySize(GaussianBuffer) > 0)
      Print("Initial GaussianBuffer[0]: ", GaussianBuffer[0]);
   
   return INIT_SUCCEEDED;
}

//--- OnCalculate function for indicator calculation
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   // Calculate Gaussian filter values
   int start = prev_calculated > 0 ? prev_calculated - 1 : 0;
   
   // Print debug info
   if(rates_total > 0 && start == 0)
   {
      Print("OnCalculate called with rates_total=", rates_total, ", prev_calculated=", prev_calculated);
   }
   
   // Make sure arrays are properly sized
   if(ArraySize(GaussianBuffer) < rates_total)
      ArrayResize(GaussianBuffer, rates_total);
   
   if(ArraySize(GaussianFilterValues) < rates_total + 10)
      ArrayResize(GaussianFilterValues, rates_total + 10);
   
   if(ArraySize(PriceBuffer) < rates_total)
      ArrayResize(PriceBuffer, rates_total);
   
   // Calculate typical price (OHLC4)
   for(int i = start; i < rates_total; i++)
   {
      // Store price data
      PriceBuffer[i] = (open[i] + high[i] + low[i] + close[i]) / 4.0;
      
      // Calculate lag
      double lag = (gaus_period_1h - 1) / (2 * 4);
      int lagIndex = (int)lag;
      
      // Make sure lag index is valid
      if(lagIndex >= ArraySize(PriceBuffer))
         lagIndex = ArraySize(PriceBuffer) - 1;
      if(lagIndex < 0)
         lagIndex = 0;
      
      // Prepare source data with lag reduction if enabled
      double srcdata = 0;
      if(modeLag_Gaus)
      {
         // Apply lag reduction formula with safety checks
         if(i - lagIndex >= 0)
            srcdata = PriceBuffer[i] + PriceBuffer[i] - PriceBuffer[i - lagIndex];
         else
            srcdata = PriceBuffer[i];
      }
      else
      {
         srcdata = PriceBuffer[i];
      }
      
      // Calculate filter with 4 poles
      double filtn = 0;
      double filt1 = 0;
      
      CalculatePoleGaus(gaus_alpha_1h, srcdata, 4, i, filtn);
      CalculatePoleGaus(gaus_alpha_1h, srcdata, 1, i, filt1);
      
      // Apply fast response mode if enabled
      GaussianBuffer[i] = modeFast_Gaus ? (filtn + filt1) / 2 : filtn;
      
      // Debug output for the current bar
      if(i == rates_total - 1)
         Print("Calculated GaussianBuffer[", i, "] = ", GaussianBuffer[i]);
   }
   
   // Return the number of calculated bars
   return rates_total;
}

//--- OnDeinit function to clean up
void OnDeinit(const int reason)
{
   // No indicator handles to release anymore
   Print("Tom Gaussian Filter deinitialized");
}

//--- Function to close all positions of a specific type
void CloseAllPositions(ENUM_POSITION_TYPE posType)
{
   int total = PositionsTotal();
   for(int i = total - 1; i >= 0; i--)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket > 0)
      {
         if(PositionGetInteger(POSITION_TYPE) == posType && PositionGetString(POSITION_SYMBOL) == _Symbol)
         {
            trade.PositionClose(ticket);
         }
      }
   }
}

//--- OnTick function to process each tick
void OnTick()
{
   // Check if we already have an open position
   if(BuySignal == 1 || SellSignal == 1)
   {
      ManageOpenPositions();
      return;
   }
   
   // Arrays to store price data
   double close_H1[3];
   double open_H1[3];
   double high_H1[3];
   double low_H1[3];
   double typical_H1[3]; // For OHLC4 calculation
   double gaus_filter_1h[3];
   
   // Copy price data
   if(CopyClose(_Symbol, PERIOD_H1, 0, 3, close_H1) <= 0 ||
      CopyOpen(_Symbol, PERIOD_H1, 0, 3, open_H1) <= 0 ||
      CopyHigh(_Symbol, PERIOD_H1, 0, 3, high_H1) <= 0 ||
      CopyLow(_Symbol, PERIOD_H1, 0, 3, low_H1) <= 0)
   {
      Print("Failed to copy price data");
      return;
   }
   
   // Calculate typical price (OHLC4) for each bar
   for(int i = 0; i < 3; i++)
   {
      typical_H1[i] = (open_H1[i] + high_H1[i] + low_H1[i] + close_H1[i]) / 4.0;
   }
   
   // Calculate Gaussian filter using our custom implementation
   for(int i = 0; i < 3; i++)
   {
      // Update price buffer
      PriceBuffer[i] = typical_H1[i];
      
      // Calculate lag
      double lag = (gaus_period_1h - 1) / (2 * 4);
      
      // Prepare source data with lag reduction if enabled
      double srcdata = 0;
      int lagIndex = (int)lag;
      
      // Make sure lag index is valid
      if(lagIndex >= ArraySize(PriceBuffer))
         lagIndex = ArraySize(PriceBuffer) - 1;
      if(lagIndex < 0)
         lagIndex = 0;
      
      if(modeLag_Gaus)
      {
         // Apply lag reduction formula from Pine script with safety checks
         if(i + lagIndex < 3)
            srcdata = typical_H1[i] + typical_H1[i] - typical_H1[MathMin(i + lagIndex, 2)];
         else
            srcdata = typical_H1[i] + typical_H1[i] - PriceBuffer[MathMin(i + lagIndex, ArraySize(PriceBuffer) - 1)];
      }
      else
      {
         srcdata = typical_H1[i];
      }
      
      // Calculate filter with 4 poles (as in Pine script)
      double filtn = 0;
      double filt1 = 0;
      
      CalculatePoleGaus(gaus_alpha_1h, srcdata, 4, i, filtn);
      CalculatePoleGaus(gaus_alpha_1h, srcdata, 1, i, filt1);
      
      // Apply fast response mode if enabled
      gaus_filter_1h[i] = modeFast_Gaus ? (filtn + filt1) / 2 : filtn;
      
      // Store in the buffer for plotting - this is what will be displayed on the chart
      GaussianBuffer[i] = gaus_filter_1h[i];
      
      // Debug output to verify values are being calculated
      if(i == 1) // Only print for the current bar
         Print("Updating GaussianBuffer[", i, "] = ", gaus_filter_1h[i]);
   }
   
   // We'll only use our custom calculation now, no need for the built-in indicator
   // But we'll keep some debug output occasionally
   static int tick_counter = 0;
   if(tick_counter % 100 == 0)
   {
      Print("Custom Gaussian[1]: ", gaus_filter_1h[1], ", Period: ", gaus_period_1h);
   }
   tick_counter++;
   
   // For historical data beyond our calculation, we'll extend the current values
   int bars = Bars(_Symbol, PERIOD_CURRENT);
   if(bars > 3)
   {
      // Make sure GaussianBuffer is properly sized
      if(ArraySize(GaussianBuffer) < bars)
         ArrayResize(GaussianBuffer, bars);
      
      // For bars beyond our calculation, just use the last calculated value
      // This is simpler and avoids using any MA calculation
      for(int i = 3; i < bars; i++)
      {
         GaussianBuffer[i] = GaussianBuffer[2]; // Use the last calculated value
      }
   }
   
   // No general debug information - will only print when entering trades
   
   // Update BuySell direction flag
   if(low_H1[1] > gaus_filter_1h[1])
   {
      BuySell = true;
      Print("BuySell = ", BuySell, " (BUY bias): Low[1]=", low_H1[1], " > Gaussian[1]=", gaus_filter_1h[1]);
   }
   else if(high_H1[1] < gaus_filter_1h[1])
   {
      BuySell = false;
      Print("BuySell = ", BuySell, " (SELL bias): High[1]=", high_H1[1], " < Gaussian[1]=", gaus_filter_1h[1]);
   }
   
   // Buy condition: BuySell = true AND close[1] > gaus_filter_1h[1] AND low[1] < gaus_filter_1h[1]
   if(BuySignal == 0 && SellSignal == 0 && BuySell == true && close_H1[1] > gaus_filter_1h[1] && low_H1[1] < gaus_filter_1h[1])
   {
      // Close all SELL positions before opening a BUY position
      CloseAllPositions(POSITION_TYPE_SELL);
      
      double lotSize = GetLotSize();
      double entryPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
      
      // Calculate initial stop loss at the low of the candle
      double initialStopLoss = low_H1[1];
      double initialSLDistance = entryPrice - initialStopLoss;
      
      // Ensure minimum SL distance of 7000 points
      double minSLDistance = 7000 * _Point;
      double stopLossDistance = MathMax(initialSLDistance, minSLDistance);
      double stopLoss = entryPrice - stopLossDistance;
      
      // Calculate take profit options
      double tp_5xSL = entryPrice + (5 * stopLossDistance);  // TP = 5 * SL
      double tp_100000points = entryPrice + (100000 * _Point);  // TP = 100000 points
      double tp_minPoints = entryPrice + (50000 * _Point);  // Minimum TP of 30000 points
      
      // Take the smaller of tp_5xSL and tp_100000points, but ensure it's at least tp_minPoints
      double takeProfit = MathMax(MathMin(tp_5xSL, tp_100000points), tp_minPoints);
      
      // Set stop loss and take profit
      trade.SetDeviationInPoints(10);  // Allow 10 points of slippage
      
      if(trade.Buy(lotSize, _Symbol, 0, stopLoss, takeProfit))
      {
         posticket = trade.ResultOrder();
         BuySignal = 1;
      }
      else
      {
         // Do nothing on error
      }
   }
   
   // Sell condition: BuySell = false AND close[1] > gaus_filter_1h[1] AND high[1] > gaus_filter_1h[1]
   else if(BuySignal == 0 && SellSignal == 0 && BuySell == false && close_H1[1] > gaus_filter_1h[1] && high_H1[1] > gaus_filter_1h[1])
   {
      // Close all BUY positions before opening a SELL position
      CloseAllPositions(POSITION_TYPE_BUY);
      
      double lotSize = GetLotSize();
      double entryPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
      
      // Calculate initial stop loss at the high of the candle
      double initialStopLoss = high_H1[1];
      double initialSLDistance = initialStopLoss - entryPrice;
      
      // Ensure minimum SL distance of 5000 points
      double minSLDistance = 5000 * _Point;
      double stopLossDistance = MathMax(initialSLDistance, minSLDistance);
      double stopLoss = entryPrice + stopLossDistance;
      
      // Calculate take profit options
      double tp_3xSL = entryPrice - (3 * stopLossDistance);  // TP = 3 * SL
      double tp_maxPoints = entryPrice - (50000 * _Point);  // Maximum TP of 30000 points
      
      // Take the larger of tp_3xSL and tp_maxPoints (since it's a sell order, the lower price is better)
      double takeProfit = MathMax(tp_3xSL, tp_maxPoints);
      
      // Set stop loss and take profit
      trade.SetDeviationInPoints(10);  // Allow 10 points of slippage
      
      if(trade.Sell(lotSize, _Symbol, 0, stopLoss, takeProfit))
      {
         posticket = trade.ResultOrder();
         SellSignal = 1;
      }
      else
      {
         // Do nothing on error
      }
   }
}

//--- Function to manage open positions
void ManageOpenPositions()
{
   // Check if position exists
   bool positionExists = false;
   
   if(PositionSelectByTicket(posticket))
   {
      positionExists = true;
      double positionProfit = PositionGetDouble(POSITION_PROFIT);
      ENUM_POSITION_TYPE positionType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
      
   }
   else
   {
      // Position not found, check history to see if it was closed with profit or loss
      HistorySelect(0, TimeCurrent()); // Select all history orders
      
      if(HistoryOrderSelect(posticket))
      {
         // Get the deal ticket from the order
         ulong dealTicket = HistoryOrderGetInteger(posticket, ORDER_POSITION_ID);
         
         if(HistoryDealSelect(dealTicket))
         {
            double dealProfit = HistoryDealGetDouble(dealTicket, DEAL_PROFIT);
            
            // Check if the trade was a loss
            if(dealProfit < 0)
            {
               lastTradeLoss = true;
            }
            else
            {
               lastTradeLoss = false;
            }
         }
      }
      
      // Reset signals
      if(BuySignal == 1)
      {
         BuySignal = 0;
      }
      else if(SellSignal == 1)
      {
         SellSignal = 0;
      }
   }
   
   // Note: We don't need to manually close positions here because we're using built-in stop loss and take profit
}
