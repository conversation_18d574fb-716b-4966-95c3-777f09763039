//+------------------------------------------------------------------+
//| TomStrategy.mq5                                                   |
//+------------------------------------------------------------------+
#property copyright "Tom"
#property link      ""
#property version   "1.00"

#include "../Includes/Constants.mqh"
#include "../Includes/TimeframeUtils.mqh"
#include "../Includes/CommonFunctions.mqh"
#include "../Includes/GaussianFilter.mqh"
#include "../Includes/AlphaCalculator.mqh"
#include "../Includes/TrendDetector.mqh"
#include "../Includes/FEXIndicator.mqh"

// Input parameters
input bool modeLag_Gaus = false;  // Reduced Lag Mode
input bool modeFast_Gaus = false; // Fast Response Mode
input double LotSize = 0.1;       // Lot Size
input int StopLoss = 100;         // Stop Loss in points
input int TakeProfit = 200;       // Take Profit in points
input bool UseStrongSignalsOnly = true; // Use only strong signals

// Global variables
bool g_strongBuy = false;
bool g_weakBuy = false;
bool g_strongSell = false;
bool g_weakSell = false;
int g_outBound = 0;
double g_FEX_ALL = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                    |
//+------------------------------------------------------------------+
int OnInit() {
    // Initialization code
    Print("TomStrategy initialized");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                  |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // Cleanup code
    Print("TomStrategy deinitialized, reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                              |
//+------------------------------------------------------------------+
void OnTick() {
    // Check if we have a new bar
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(Symbol(), Period(), 0);
    
    if(currentBarTime == lastBarTime) {
        return; // No new bar, exit
    }
    lastBarTime = currentBarTime;
    
    // Get Gaussian Filter values for different timeframes
    double filt_Gaus_5m, hband_Gaus_5m, lband_Gaus_5m;
    color fcolor_Gaus_5m;
    double filt_Gaus_15m, hband_Gaus_15m, lband_Gaus_15m;
    color fcolor_Gaus_15m;
    double filt_Gaus_1h, hband_Gaus_1h, lband_Gaus_1h;
    color fcolor_Gaus_1h;
    double filt_Gaus_4h, hband_Gaus_4h, lband_Gaus_4h;
    color fcolor_Gaus_4h;
    
    // Calculate Gaussian Filter for each timeframe
    CalcGausFilter("5", filt_Gaus_5m, hband_Gaus_5m, lband_Gaus_5m, fcolor_Gaus_5m, modeLag_Gaus, modeFast_Gaus);
    CalcGausFilter("15", filt_Gaus_15m, hband_Gaus_15m, lband_Gaus_15m, fcolor_Gaus_15m, modeLag_Gaus, modeFast_Gaus);
    CalcGausFilter("60", filt_Gaus_1h, hband_Gaus_1h, lband_Gaus_1h, fcolor_Gaus_1h, modeLag_Gaus, modeFast_Gaus);
    CalcGausFilter("240", filt_Gaus_4h, hband_Gaus_4h, lband_Gaus_4h, fcolor_Gaus_4h, modeLag_Gaus, modeFast_Gaus);
    
    // Get previous values for trend calculation
    double filt_Gaus_1h_prev = 0;
    color fcolor_Gaus_1h_prev;
    double hband_Gaus_1h_prev = 0, lband_Gaus_1h_prev = 0;
    CalcGausFilter("60", filt_Gaus_1h_prev, hband_Gaus_1h_prev, lband_Gaus_1h_prev, fcolor_Gaus_1h_prev, modeLag_Gaus, modeFast_Gaus);
    
    // Calculate Alpha for different timeframes
    double alpha15 = CalcAlpha("15");
    double alpha60 = CalcAlpha("60");
    double alpha240 = CalcAlpha("240");
    double alphaDaily = CalcAlpha("D");
    double alphaWeekly = CalcAlpha("W");
    
    // Get 5-minute candle data
    double close_5m = iClose(Symbol(), PERIOD_M5, 1);
    
    // Calculate trend states
    CalculateTrendStates(filt_Gaus_1h, filt_Gaus_1h_prev, filt_Gaus_5m, alpha15, alpha60, close_5m, 
                         g_strongBuy, g_weakBuy, g_strongSell, g_weakSell);
    
    // Calculate FEX_ALL and OutBound
    CalcFexAllOutbound(g_FEX_ALL, g_outBound);
    
    // Trading logic based on trend states and FEX_ALL
    if(!PositionSelect(Symbol())) { // No open position
        if(g_strongBuy && g_outBound > 0) {
            if(UseStrongSignalsOnly) {
                OpenBuy();
            }
        }
        else if(g_weakBuy && g_outBound > 0 && !UseStrongSignalsOnly) {
            OpenBuy();
        }
        else if(g_strongSell && g_outBound < 0) {
            if(UseStrongSignalsOnly) {
                OpenSell();
            }
        }
        else if(g_weakSell && g_outBound < 0 && !UseStrongSignalsOnly) {
            OpenSell();
        }
    }
    else { // Have open position
        ManagePosition();
    }
    
    // Display current state
    Comment("TomStrategy\n",
            "Strong Buy: ", g_strongBuy, "\n",
            "Weak Buy: ", g_weakBuy, "\n",
            "Strong Sell: ", g_strongSell, "\n",
            "Weak Sell: ", g_weakSell, "\n",
            "OutBound: ", g_outBound, "\n",
            "FEX_ALL: ", g_FEX_ALL, "\n",
            "Alpha15: ", alpha15, "\n",
            "Alpha60: ", alpha60, "\n",
            "Filt_Gaus_5m: ", filt_Gaus_5m, "\n",
            "Filt_Gaus_1h: ", filt_Gaus_1h, "\n",
            "Filt_Gaus_4h: ", filt_Gaus_4h);
}

//+------------------------------------------------------------------+
//| Mở lệnh Buy                                                      |
//+------------------------------------------------------------------+
void OpenBuy() {
    double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double sl = StopLoss > 0 ? ask - StopLoss * Point() : 0;
    double tp = TakeProfit > 0 ? ask + TakeProfit * Point() : 0;
    
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = LotSize;
    request.type = ORDER_TYPE_BUY;
    request.price = ask;
    request.sl = sl;
    request.tp = tp;
    request.deviation = 10;
    request.magic = 123456; // Magic number
    request.comment = "TomStrategy Buy";
    request.type_filling = ORDER_FILLING_FOK;
    
    if(!OrderSend(request, result)) {
        Print("OrderSend error: ", GetLastError());
    }
    else {
        Print("OrderSend placed successfully. Ticket: ", result.order);
    }
}

//+------------------------------------------------------------------+
//| Mở lệnh Sell                                                     |
//+------------------------------------------------------------------+
void OpenSell() {
    double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double sl = StopLoss > 0 ? bid + StopLoss * Point() : 0;
    double tp = TakeProfit > 0 ? bid - TakeProfit * Point() : 0;
    
    MqlTradeRequest request = {};
    MqlTradeResult result = {};
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = LotSize;
    request.type = ORDER_TYPE_SELL;
    request.price = bid;
    request.sl = sl;
    request.tp = tp;
    request.deviation = 10;
    request.magic = 123456; // Magic number
    request.comment = "TomStrategy Sell";
    request.type_filling = ORDER_FILLING_FOK;
    
    if(!OrderSend(request, result)) {
        Print("OrderSend error: ", GetLastError());
    }
    else {
        Print("OrderSend placed successfully. Ticket: ", result.order);
    }
}

//+------------------------------------------------------------------+
//| Quản lý vị thế đang mở                                           |
//+------------------------------------------------------------------+
void ManagePosition() {
    if(!PositionSelect(Symbol())) return;
    
    long posType = PositionGetInteger(POSITION_TYPE);
    double posPrice = PositionGetDouble(POSITION_PRICE_OPEN);
    double posSL = PositionGetDouble(POSITION_SL);
    double posTP = PositionGetDouble(POSITION_TP);
    double currentPrice = posType == POSITION_TYPE_BUY ? 
                         SymbolInfoDouble(Symbol(), SYMBOL_BID) : 
                         SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    
    // Kiểm tra tín hiệu đảo chiều
    bool closeSignal = false;
    
    if(posType == POSITION_TYPE_BUY) {
        // Đóng lệnh Buy khi có tín hiệu Sell mạnh hoặc FEX_ALL < 0
        if(g_strongSell || g_outBound < 0 || g_FEX_ALL < -85) {
            closeSignal = true;
        }
    }
    else if(posType == POSITION_TYPE_SELL) {
        // Đóng lệnh Sell khi có tín hiệu Buy mạnh hoặc FEX_ALL > 0
        if(g_strongBuy || g_outBound > 0 || g_FEX_ALL > 85) {
            closeSignal = true;
        }
    }
    
    // Đóng lệnh nếu có tín hiệu
    if(closeSignal) {
        MqlTradeRequest request = {};
        MqlTradeResult result = {};
        
        request.action = TRADE_ACTION_DEAL;
        request.symbol = Symbol();
        request.volume = PositionGetDouble(POSITION_VOLUME);
        request.type = posType == POSITION_TYPE_BUY ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
        request.price = currentPrice;
        request.deviation = 10;
        request.magic = 123456;
        request.comment = "TomStrategy Close";
        request.position = PositionGetInteger(POSITION_TICKET);
        request.type_filling = ORDER_FILLING_FOK;
        
        if(!OrderSend(request, result)) {
            Print("Close position error: ", GetLastError());
        }
        else {
            Print("Position closed successfully. Ticket: ", result.order);
        }
    }
    
    // Trailing Stop (tùy chọn)
    // Thêm code trailing stop ở đây nếu cần
}