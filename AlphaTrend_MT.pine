// @version=5
indicator("AlphaTrend Multi-Timeframe", overlay=true)

// Define colors for each timeframe
color_15min = color.new(#0022FC, 0)  // Blue for 15min
color_60min = color.new(#00B300, 0)  // Green for 1h
color_240min = color.new(#FF0000, 0) // Red for 4h

// Function to calculate AlphaTrend for a specific timeframe
calcAlphaTrend(tf) =>
    // Get data from the specified timeframe
    factor_alpha_S = tf == "D" or tf == "M" ? 1.2 : 
                     tf == "W" ? 1.15 : 
                     tf == "240" ? 1.2 : 
                     tf == "60" ? 2.7 : 
                     tf == "15" ? 4.6 : 3
    
    // Request security data for the specified timeframe
    [tfHigh, tfLow, tfClose] = request.security(syminfo.tickerid, tf, [high, low, close])
    tfHLC3 = (tfHigh + tfLow + tfClose) / 3
    
    // Calculate True Range for the timeframe
    tfTR = request.security(syminfo.tickerid, tf, ta.tr)
    tfTR_SMA = request.security(syminfo.tickerid, tf, ta.sma(ta.tr, 69))
    
    // Calculate MFI for the timeframe
    tfMFI = request.security(syminfo.tickerid, tf, ta.mfi(hlc3, 69))
    
    // Calculate upT_S and downT_S
    upT_S = tfLow - tfTR_SMA * factor_alpha_S
    downT_S = tfHigh + tfTR_SMA * factor_alpha_S
    
    // Initialize AlphaTrend_S
    var float alphaTrend = 0.0
    
    // Update AlphaTrend_S based on conditions
    alphaTrend := tfMFI >= 50 ? 
                 upT_S < nz(alphaTrend[1]) ? nz(alphaTrend[1]) : upT_S : 
                 downT_S > nz(alphaTrend[1]) ? nz(alphaTrend[1]) : downT_S
    
    alphaTrend

// Calculate AlphaTrend for each timeframe
alphaTrend15 = calcAlphaTrend("15")
alphaTrend60 = calcAlphaTrend("60")
alphaTrend240 = calcAlphaTrend("240")

// Plot all three timeframes simultaneously
plot(alphaTrend15, title="AlphaS 15m", color=color_15min, linewidth=2)
plot(alphaTrend60, title="AlphaS 1h", color=color_60min, linewidth=2)
plot(alphaTrend240, title="AlphaS 4h", color=color_240min, linewidth=2)