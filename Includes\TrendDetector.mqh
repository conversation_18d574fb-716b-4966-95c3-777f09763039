//+------------------------------------------------------------------+
//| TrendDetector.mqh - Ph<PERSON>t hiện xu hướng                           |
//+------------------------------------------------------------------+
#include "Constants.mqh"
#include "CommonFunctions.mqh"

//+------------------------------------------------------------------+
//| Tính toán xu hướng giờ                                           |
//+------------------------------------------------------------------+
bool <PERSON>rend(double filt_Gaus_1h_current, double filt_Gaus_1h_prev) {
    return filt_Gaus_1h_current > filt_Gaus_1h_prev;
}

//+------------------------------------------------------------------+
//| Tính toán so sánh Alpha                                          |
//+------------------------------------------------------------------+
bool CalcAlphaComparison(double alpha15, double alpha60) {
    return alpha15 > alpha60;
}

//+------------------------------------------------------------------+
//| Tính toán xu hướng dựa trên các chỉ báo                          |
//+------------------------------------------------------------------+
void CalculateTrendStates(double filt_Gaus_1h_current, double filt_Gaus_1h_prev, 
                         double filt_Gaus_5m, double alpha15, double alpha60, 
                         double close_5m, bool &strongBuy, bool &weakBuy, 
                         bool &strongSell, bool &weakSell) {
    // Tính toán xu hướng 1h
    bool hourly_trend_up = CalcHourlyTrend(filt_Gaus_1h_current, filt_Gaus_1h_prev);
    
    // Tính toán so sánh alpha
    bool alpha_condition = CalcAlphaComparison(alpha15, alpha60);
    
    // Tính toán các điều kiện giá
    bool price_above_5m_filter = close_5m > filt_Gaus_5m;
    bool price_below_5m_filter = close_5m < filt_Gaus_5m;
    bool price_max_alphas_bullish = close_5m > MathMax(alpha15, alpha60);
    bool price_min_alphas_bearish = close_5m < MathMin(alpha15, alpha60);
    
    // Bốn trường hợp loại trừ lẫn nhau
    strongBuy = false;
    weakBuy = false;
    strongSell = false;
    weakSell = false;
    
    if(alpha_condition && hourly_trend_up && price_above_5m_filter && price_max_alphas_bullish) {
        strongBuy = true;
    }
    else if(price_above_5m_filter || price_max_alphas_bullish || (alpha_condition && !hourly_trend_up)) {
        weakBuy = true;
    }
    else if(!alpha_condition && !hourly_trend_up && price_below_5m_filter && price_min_alphas_bearish) {
        strongSell = true;
    }
    else if(price_below_5m_filter || price_min_alphas_bearish || (!alpha_condition && hourly_trend_up)) {
        weakSell = true;
    }
}