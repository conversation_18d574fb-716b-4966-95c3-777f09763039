//+------------------------------------------------------------------+
//| FEXIndicator.mqh - Chỉ báo FEX_ALL                               |
//+------------------------------------------------------------------+
#include "Constants.mqh"
#include "CommonFunctions.mqh"

//+------------------------------------------------------------------+
//| Tính toán FEX_ALL và OutBound                                    |
//+------------------------------------------------------------------+
void <PERSON>llOutbound(double &FEX_ALL, int &outBound) {
    // Thiết lập các tham số dựa trên timeframe
    int Period = 100;
    double Factor_FEX = 2.5;
    double Delta_tom = 90.0;
    int RSI_Period = 24;
    double Factor_plus = 0;
    double Factor_plus_rsi = 0;
    
    string symbol = Symbol();
    if(symbol == "BTCUSD") {
        Factor_plus = 5;
        Factor_plus_rsi = 3;
    }
    
    ENUM_TIMEFRAMES tf = Period();
    
    if(tf == PERIOD_M1 || tf == PERIOD_M5 || tf == PERIOD_M15 || tf == PERIOD_M30) {
        Factor_FEX = 3;
        Delta_tom = 93.9;
        RSI_Period = 26;
        Period = 269;
    }
    else if(tf == PERIOD_H1) {
        Factor_FEX = 2.7;
        Delta_tom = 83.9 + Factor_plus;
        RSI_Period = 24 + Factor_plus_rsi;
        Period = 179;
    }
    else if(tf == PERIOD_H4) {
        Factor_FEX = 2.7;
        Delta_tom = 83.9 + Factor_plus;
        RSI_Period = 24 + Factor_plus_rsi;
        Period = 179;
    }
    else if(tf == PERIOD_D1) {
        Factor_FEX = 2;
        Delta_tom = 80 + Factor_plus;
        RSI_Period = 23 + Factor_plus_rsi;
    }
    else if(tf == PERIOD_W1) {
        Factor_FEX = 2;
        Delta_tom = 78 + Factor_plus;
        RSI_Period = 22 + Factor_plus_rsi;
    }
    else if(tf == PERIOD_MN1) {
        Factor_FEX = 2;
        Delta_tom = 76 + Factor_plus;
        RSI_Period = 21 + Factor_plus_rsi;
    }
    
    // Tính toán các giá trị trung bình
    double close_array[], open_array[], high_array[], low_array[];
    ArraySetAsSeries(close_array, true);
    ArraySetAsSeries(open_array, true);
    ArraySetAsSeries(high_array, true);
    ArraySetAsSeries(low_array, true);
    
    CopyClose(Symbol(), 0, 0, Period, close_array);
    CopyOpen(Symbol(), 0, 0, Period, open_array);
    CopyHigh(Symbol(), 0, 0, Period, high_array);
    CopyLow(Symbol(), 0, 0, Period, low_array);
    
    double Closema = 0, Openma = 0, Highma = 0, Lowma = 0;
    for(int i=0; i<Period; i++) {
        Closema += close_array[i];
        Openma += open_array[i];
        Highma += high_array[i];
        Lowma += low_array[i];
    }
    Closema /= Period;
    Openma /= Period;
    Highma /= Period;
    Lowma /= Period;
    
    double CloseSpread = Close[0] - Closema;
    double OpenSpread = Open[0] - Openma;
    double HighSpread = High[0] - Highma;
    double LowSpread = Low[0] - Lowma;
    
    // Tính StdDev
    double CloseStdDev = 0, OpenStdDev = 0, HighStdDev = 0, LowStdDev = 0;
    for(int i=0; i<Period; i++) {
        CloseStdDev += MathPow(close_array[i] - Closema, 2);
        OpenStdDev += MathPow(open_array[i] - Openma, 2);
        HighStdDev += MathPow(high_array[i] - Highma, 2);
        LowStdDev += MathPow(low_array[i] - Lowma, 2);
    }
    CloseStdDev = MathSqrt(CloseStdDev / Period);
    OpenStdDev = MathSqrt(OpenStdDev / Period);
    HighStdDev = MathSqrt(HighStdDev / Period);
    LowStdDev = MathSqrt(LowStdDev / Period);
    
    double ZscoreClose = CloseSpread / CloseStdDev;
    double ZscoreOpen = OpenSpread / OpenStdDev;
    double ZscoreHigh = HighSpread / HighStdDev;
    double ZscoreLow = LowSpread / LowStdDev;
    
    // Tính toán Fisher Transform
    double hl2_array[];
    ArraySetAsSeries(hl2_array, true);
    for(int i=0; i<139; i++) {
        hl2_array[i] = (high_array[i] + low_array[i]) / 2;
    }
    
    double high_ = hl2_array[ArrayMaximum(hl2_array, 0, 139)];
    double low_ = hl2_array[ArrayMinimum(hl2_array, 0, 139)];
    
    static double value = 0.0;
    value = Round(0.66 * ((HL2() - low_) / (high_ - low_) - 0.5) + 0.67 * value);
    
    static double fish1 = 0.0;
    fish1 = 0.5 * MathLog((1 + value) / (1 - value)) + 0.5 * fish1;
    
    // Tính toán ATR
    double atrValue = 0;
    for(int i=0; i<12; i++) {
        atrValue += MathMax(high_array[i], close_array[i+1]) - MathMin(low_array[i], close_array[i+1]);
    }
    atrValue /= 12;
    
    double priceRange = Factor_FEX * atrValue;
    
    // Tính RSI
    double rsi_array[];
    ArraySetAsSeries(rsi_array, true);
    int rsi_handle = iRSI(Symbol(), 0, 22, PRICE_CLOSE);
    CopyBuffer(rsi_handle, 0, 0, 1, rsi_array);
    double rsiValue = rsi_array[0];
    double rsiValueNormalized = Normalize1(rsiValue, 27, 77);
    
    // Tính Elliot Wave
    double ema17_array[], ema66_array[];
    ArraySetAsSeries(ema17_array, true);
    ArraySetAsSeries(ema66_array, true);
    int ema17_handle = iMA(Symbol(), 0, 17, 0, MODE_EMA, PRICE_CLOSE);
    int ema66_handle = iMA(Symbol(), 0, 66, 0, MODE_EMA, PRICE_CLOSE);
    CopyBuffer(ema17_handle, 0, 0, 1, ema17_array);
    CopyBuffer(ema66_handle, 0, 0, 1, ema66_array);
    double emaDiff = ema17_array[0] - ema66_array[0];
    double emaDiffNormalized = Normalize1(emaDiff, -priceRange, priceRange);
    
    // Tính MACD
    double ema16_array[], ema100_array[];
    ArraySetAsSeries(ema16_array, true);
    ArraySetAsSeries(ema100_array, true);
    int ema16_handle = iMA(Symbol(), 0, 16, 0, MODE_EMA, PRICE_CLOSE);
    int ema100_handle = iMA(Symbol(), 0, 100, 0, MODE_EMA, PRICE_CLOSE);
    CopyBuffer(ema16_handle, 0, 0, 1, ema16_array);
    CopyBuffer(ema100_handle, 0, 0, 1, ema100_array);
    double macd = ema16_array[0] - ema100_array[0];
    
    double macd_array[];
    ArraySetAsSeries(macd_array, true);
    for(int i=0; i<12; i++) {
        macd_array[i] = macd;
    }
    double macdSignal = 0;
    for(int i=0; i<12; i++) {
        macdSignal += macd_array[i];
    }
    macdSignal /= 12;
    
    double macdHistogram = macd - macdSignal;
    double macdNormalized = Normalize1(macd, -priceRange, priceRange);
    double macdSignalNormalized = Normalize1(macdSignal, -priceRange, priceRange);
    double macdHistogramNormalized = Normalize1(macdHistogram, -priceRange, priceRange);
    
    // Tính MFI
    double volume_array[], hlc3_array[], change_array[];
    ArraySetAsSeries(volume_array, true);
    ArraySetAsSeries(hlc3_array, true);
    ArraySetAsSeries(change_array, true);
    
    CopyTickVolume(Symbol(), 0, 0, 14, volume_array);
    for(int i=0; i<14; i++) {
        hlc3_array[i] = (high_array[i] + low_array[i] + close_array[i]) / 3;
    }
    
    for(int i=0; i<13; i++) {
        change_array[i] = hlc3_array[i] - hlc3_array[i+1];
    }
    
    double positiveFlow = 0, negativeFlow = 0;
    for(int i=0; i<14; i++) {
        if(i < 13 && change_array[i] > 0) {
            positiveFlow += volume_array[i] * hlc3_array[i];
        } else if(i < 13 && change_array[i] < 0) {
            negativeFlow += volume_array[i] * hlc3_array[i];
        }
    }
    
    double mfiValue = 100.0 - 100.0 / (1.0 + positiveFlow / negativeFlow);
    double mfiValueNormalized = Normalize1(mfiValue, 25, 75);
    
    // Tính Composite
    double compositeValue = emaDiffNormalized + rsiValueNormalized + macdHistogramNormalized + mfiValueNormalized;
    double compositeNormalized = Normalize1(compositeValue, -4, 4);
    
    // Tính QQE
    double rsi_all_array[];
    ArraySetAsSeries(rsi_all_array, true);
    int rsi_all_handle = iRSI(Symbol(), 0, RSI_Period, PRICE_CLOSE);
    CopyBuffer(rsi_all_handle, 0, 0, 1, rsi_all_array);
    double RSI_ALL = rsi_all_array[0];
    
    double rsi_ma_array[];
    ArraySetAsSeries(rsi_ma_array, true);
    int rsi_ma_handle = iMA(Symbol(), 0, 6, 0, MODE_EMA, PRICE_CLOSE);
    CopyBuffer(rsi_ma_handle, 0, 0, 1, rsi_ma_array);
    double RsiMa2 = rsi_ma_array[0];
    
    // Tính Stochastic Tom
    double lowest_array[], highest_array[];
    ArraySetAsSeries(lowest_array, true);
    ArraySetAsSeries(highest_array, true);
    for(int i=0; i<27; i++) {
        lowest_array[i] = low_array[i];
        highest_array[i] = high_array[i];
    }
    
    double ll = lowest_array[ArrayMinimum(lowest_array, 0, 27)];
    double hh = highest_array[ArrayMaximum(highest_array, 0, 27)];
    double diff = hh - ll;
    double rdiff = Close[0] - (hh+ll)/2;
    
    double avgrel_array[], avgdiff_array[];
    ArraySetAsSeries(avgrel_array, true);
    ArraySetAsSeries(avgdiff_array, true);
    
    for(int i=0; i<9; i++) {
        avgrel_array[i] = rdiff;
        avgdiff_array[i] = diff;
    }
    
    double avgrel = 0, avgdiff = 0;
    for(int i=0; i<9; i++) {
        avgrel += avgrel_array[i];
        avgdiff += avgdiff_array[i];
    }
    avgrel /= 9;
    avgdiff /= 9;
    
    double SMI = avgdiff != 0 ? (avgrel/(avgdiff/2)*100) : 0;
    
    double smi_array[];
    ArraySetAsSeries(smi_array, true);
    for(int i=0; i<9; i++) {
        smi_array[i] = SMI;
    }
    
    double SMIsignal = 0, emasignal = 0;
    for(int i=0; i<9; i++) {
        SMIsignal += smi_array[i];
    }
    SMIsignal /= 9;
    
    for(int i=0; i<10; i++) {
        emasignal += smi_array[i];
    }
    emasignal /= 10;
    
    double mfi_cal_array[];
    ArraySetAsSeries(mfi_cal_array, true);
    int mfi_cal_handle = iMFI(Symbol(), 0, 42, VOLUME_TICK);
    CopyBuffer(mfi_cal_handle, 0, 0, 1, mfi_cal_array);
    double mfi_cal = mfi_cal_array[0] - 50;
    
    // Tính các FEX components
    double FEX_E = ZscoreClose > 5.3 ? 5.3 : ZscoreClose < -5.3 ? -5.3 : ZscoreClose*0.85;
    double FEX_F = fish1*6/7 > 6 ? 6 : fish1*6/7;
    double FEX_BLSH = 5.3*compositeNormalized > 5.3 ? 5.3 : 5.3*compositeNormalized;
    double FEX_QQE = (RsiMa2 - 50)*0.3 > 5.5 ? 5.5 : (RsiMa2 - 50)*0.3 < -5.5 ? -5.5 : (RsiMa2 - 50)*0.3;
    double FEX_RSI = (((RSI_ALL - 50)/5.5) > 5.5) ? 5.5 : ((RSI_ALL - 50)/5.5) < -5.5 ? -5.5 : ((RSI_ALL - 50)/5.5);
    double FEX_STOC = emasignal > 77 ? 5.3 : emasignal < -77 ? -5.3 : emasignal*5.3/77;
    
    // Tính FEX
    double FEX_components[6] = {FEX_E, FEX_F, FEX_BLSH, FEX_QQE, FEX_RSI, FEX_STOC};
    double FEX = 0;
    for(int i=0; i<6; i++) {
        FEX += FEX_components[i];
    }
    FEX /= 6;
    
    double FEX_EMA_array[];
    ArraySetAsSeries(FEX_EMA_array, true);
    for(int i=0; i<24; i++) {
        FEX_EMA_array[i] = FEX;
    }
    
    double FEX_EMA = 0;
    for(int i=0; i<24; i++) {
        FEX_EMA += FEX_EMA_array[i];
    }
    FEX_EMA /= 24;
    
    double FEX_TOM = MathRound(MathAvg(FEX, FEX_EMA)*18.1*1000)/1000;
    
    // Tính Tom_wave trend
    double hlc3 = HLC3();
    double esa_array[], d_array[], ci_array[], ema_cn_array[];
    ArraySetAsSeries(esa_array, true);
    ArraySetAsSeries(d_array, true);
    ArraySetAsSeries(ci_array, true);
    ArraySetAsSeries(ema_cn_array, true);
    
    for(int i=0; i<32; i++) {
        esa_array[i] = hlc3;
    }
    
    double esa = 0;
    for(int i=0; i<32; i++) {
        esa += esa_array[i];
    }
    esa /= 32;
    
    for(int i=0; i<32; i++) {
        d_array[i] = MathAbs(hlc3 - esa);
    }
    
    double d = 0;
    for(int i=0; i<32; i++) {
        d += d_array[i];
    }
    d /= 32;
    
    double ci = (hlc3 - esa) / (0.015 * d);
    
    for(int i=0; i<222; i++) {
        ci_array[i] = ci;
    }
    
    double ema_cn = 0;
    for(int i=0; i<222; i++) {
        ema_cn += ci_array[i];
    }
    ema_cn = 100 * ema_cn / 222 / 57;
    
    double tom = ema_cn > 93 ? 93 + (ema_cn -93)/3 : ema_cn < -93 ? -93 + (93 + ema_cn)/3 : ema_cn;
    
    // Tính FEX_ALL
    double BTC_factor = symbol == "BTCUSD" ? 0.9 : 1;
    FEX_ALL = (100*MathAvg(FEX_TOM, 0.92*tom)/87 > 0 ? 100*MathAvg(FEX_TOM, 0.92*tom)/87 : 100*MathAvg(FEX_TOM, 0.92*tom)/87)*BTC_factor;
    
    // Tính OutBound
    int length = 1234;
    double innermult = 4.1;
    double outermult = 4.1;
    double source = High[0];
    
    if(tf == PERIOD_M1) {
        source = Close[0];
        length = 4444;
        innermult = 14.4;
        outermult = 14.4;
    }
    else if(tf == PERIOD_M3) {
        source = OHLC4();
        length = 1600;
        innermult = 11;
        outermult = 11;
    }
    else if(tf == PERIOD_M5) {
        source = OHLC4();
        length = 1036;
        innermult = 7;
        outermult = 7;
    }
    else if(tf == PERIOD_M15 || tf == PERIOD_M30) {
        source = OHLC4();
        length = 1155;
        innermult = 6.6;
        outermult = 6.6;
    }
    else if(tf == PERIOD_H1 || tf == PERIOD_H4) {
        source = OHLC4();
        length = 1234;
        innermult = 9;
        outermult = 9;
    }
    else if(tf == PERIOD_D1) {
        source = OHLC4();
        length = 369;
        innermult = 3.69;
        outermult = 3.69;
    }
    else if(tf == PERIOD_W1) {
        source = High[0];
        length = 369;
        innermult = 3.6;
        outermult = 3.6;
    }
    else if(tf == PERIOD_MN1) {
        source = High[0];
        length = 369;
        innermult = 3.6;
        outermult = 3.6;
    }
    else {
        source = OHLC4();
        length = 1234;
        innermult = 11;
        outermult = 11;
    }
    
    double mult = PI * innermult;
    double mult2 = PI * outermult;
    
    // Tính Mean Reversion Channel
    double source_array[], tr_array[];
    ArraySetAsSeries(source_array, true);
    ArraySetAsSeries(tr_array, true);
    
    for(int i=0; i<length; i++) {
        source_array[i] = source;
        tr_array[i] = MathMax(High[i], Close[i+1]) - MathMin(Low[i], Close[i+1]);
    }
    
    double v_meanline = SuperSmoother(source_array, length, PI);
    double v_meanrange = SuperSmoother(tr_array, length, PI);
    
    double v_upband1 = v_meanline + v_meanrange * mult;
    double v_loband1 = v_meanline - v_meanrange * mult;
    double v_upband2 = v_meanline + v_meanrange * mult2;
    double v_loband2 = v_meanline - v_meanrange * mult2;
    
    // Tính OutBound
    double gradsize = 0.5;
    double meanrange = v_meanrange;
    double upband2 = v_upband2;
    double loband2 = v_loband2;
    
    double upband2_1 = upband2 + meanrange * gradsize * 4;
    double loband2_1 = loband2 - meanrange * gradsize * 4;
    double upband2_9 = upband2 + meanrange * gradsize * -4;
    double loband2_9 = loband2 - meanrange * gradsize * -4;
    
    double minOutBound = MathMin(loband2_1, loband2_9);
    double maxOutBound = MathMax(upband2_1, upband2_9);
    
    // Thiết lập giá trị outBound
    if(Close[0] > upband2_1) {
        outBound = 1;
    }
    else if(Close[0] < minOutBound) {
        outBound = -1;
    }
    else {
        outBound = 0;
    }
}

//+------------------------------------------------------------------+
//| Lấy màu sắc cho FEX_ALL                                          |
//+------------------------------------------------------------------+
color GetFexColor(double fex_value) {
    double FEX_purple_temp = Period() == PERIOD_M5 ? 97 : 
                            (Period() == PERIOD_H1 || Symbol() == "BTCUSD") ? 93 : 85;
    
    double FEX_purple = (Period() == PERIOD_D1 || Period() == PERIOD_W1 || Period() == PERIOD_MN1) ? 
                        Symbol() == "XAUUSD" ? 90 : 90 : 
                        FEX_purple_temp < 95 ? 95 : FEX_purple_temp;
    
    double color_fex_green = Symbol() == "XAUUSD" ? 
                            (Period() == PERIOD_H4 ? 75 : 
                             Period() == PERIOD_H1 ? 75 : 
                             Period() == PERIOD_M15 ? 75 : 
                             Period() == PERIOD_M3 ? 77 : 
                             Period() == PERIOD_M1 ? 80 : 65) : 60;
    
    double color_fex_red = Symbol() == "XAUUSD" ? 
                          (Period() == PERIOD_H1 ? -80 : 
                           Period() == PERIOD_M15 ? -85 : 
                           Period() == PERIOD_M3 ? -87 : 
                           Period() == PERIOD_M1 ? -95 : -60) : -65;
    
    if(fex_value > FEX_purple) return clrPurple;
    if(fex_value > color_fex_green) return clrGreen;
    if(fex_value < -FEX_purple) return clrBlue;
    if(fex_value < color_fex_red) return clrRed;
    return clrBlack;
}