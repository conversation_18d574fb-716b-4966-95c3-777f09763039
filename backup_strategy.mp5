//+------------------------------------------------------------------+
//|                                                  strategy_mq5.mq5 |
//|                        Copyright 2024, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 15
#property indicator_plots   15

// Plot buffers
double Alpha15Buffer[];
double Alpha60Buffer[];
double Alpha240Buffer[];
double AlphaDailyBuffer[];
double AlphaWeeklyBuffer[];
double Filter5mBuffer[];
double Filter1hBuffer[];
double HBand1hBuffer[];
double LBand1hBuffer[];
double FEXAllBuffer[];

// Additional buffers for signals
double StrongBuyBuffer[];
double WeakBuyBuffer[];
double StrongSellBuffer[];
double WeakSellBuffer[];
double HighSignalBuffer[];

// Input parameters
input bool modeLag_Gaus = false;        // Reduced Lag Mode
input bool modeFast_Gaus = false;       // Fast Response Mode
input string tf_w = "W1";               // Weekly Timeframe
input string tf_d = "D1";               // Daily Timeframe
input string tf_4h = "H4";              // 4-Hour Timeframe
input string tf_1h = "H1";              // 1-Hour Timeframe
input string tf_15m = "M15";            // 15-Minute Timeframe

// Global variables
double cloudSize;
int baseCloudEmaLength;
color colorGausUp = clrLime;
color colorGausDn = clrRed;
color colorDefault = clrGray;

// Colors for timeframes
color color_15min = clrBlue;
color color_60min = clrGreen;
color color_240min = clrBlack;
color color_daily = clrRed;
color color_weekly = clrOrange;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("OnInit called");
    // Set indicator buffers
    SetIndexBuffer(0, Alpha15Buffer, INDICATOR_DATA);
    SetIndexBuffer(1, Alpha60Buffer, INDICATOR_DATA);
    SetIndexBuffer(2, Alpha240Buffer, INDICATOR_DATA);
    SetIndexBuffer(3, AlphaDailyBuffer, INDICATOR_DATA);
    SetIndexBuffer(4, AlphaWeeklyBuffer, INDICATOR_DATA);
    SetIndexBuffer(5, Filter5mBuffer, INDICATOR_DATA);
    SetIndexBuffer(6, Filter1hBuffer, INDICATOR_DATA);
    SetIndexBuffer(7, HBand1hBuffer, INDICATOR_DATA);
    SetIndexBuffer(8, LBand1hBuffer, INDICATOR_DATA);
    SetIndexBuffer(9, FEXAllBuffer, INDICATOR_DATA);
    
    SetIndexBuffer(10, StrongBuyBuffer, INDICATOR_DATA);
    SetIndexBuffer(11, WeakBuyBuffer, INDICATOR_DATA);
    SetIndexBuffer(12, StrongSellBuffer, INDICATOR_DATA);
    SetIndexBuffer(13, WeakSellBuffer, INDICATOR_DATA);
    SetIndexBuffer(14, HighSignalBuffer, INDICATOR_DATA);
    
    // Set plot properties
    PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(0, PLOT_LINE_COLOR, color_15min);
    PlotIndexSetInteger(0, PLOT_LINE_WIDTH, 1);
    PlotIndexSetString(0, PLOT_LABEL, "Alpha 15m");
    
    PlotIndexSetInteger(1, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(1, PLOT_LINE_COLOR, color_60min);
    PlotIndexSetInteger(1, PLOT_LINE_WIDTH, 2);
    PlotIndexSetString(1, PLOT_LABEL, "Alpha 1h");
    
    PlotIndexSetInteger(2, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(2, PLOT_LINE_COLOR, color_240min);
    PlotIndexSetInteger(2, PLOT_LINE_WIDTH, 2);
    PlotIndexSetString(2, PLOT_LABEL, "Alpha 4h");
    
    PlotIndexSetInteger(3, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(3, PLOT_LINE_COLOR, color_daily);
    PlotIndexSetInteger(3, PLOT_LINE_WIDTH, 2);
    PlotIndexSetString(3, PLOT_LABEL, "Alpha D");
    
    PlotIndexSetInteger(4, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(4, PLOT_LINE_COLOR, color_weekly);
    PlotIndexSetInteger(4, PLOT_LINE_WIDTH, 3);
    PlotIndexSetString(4, PLOT_LABEL, "Alpha W");
    
    PlotIndexSetInteger(5, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(5, PLOT_LINE_COLOR, clrYellow);
    PlotIndexSetInteger(5, PLOT_LINE_WIDTH, 3);
    PlotIndexSetString(5, PLOT_LABEL, "5m Filter");
    
    PlotIndexSetInteger(6, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(6, PLOT_LINE_COLOR, clrCyan);
    PlotIndexSetInteger(6, PLOT_LINE_WIDTH, 2);
    PlotIndexSetString(6, PLOT_LABEL, "1h Filter");
    
    PlotIndexSetInteger(7, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(7, PLOT_LINE_COLOR, clrCyan);
    PlotIndexSetInteger(7, PLOT_LINE_WIDTH, 1);
    PlotIndexSetString(7, PLOT_LABEL, "1h High Band");
    
    PlotIndexSetInteger(8, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(8, PLOT_LINE_COLOR, clrCyan);
    PlotIndexSetInteger(8, PLOT_LINE_WIDTH, 1);
    PlotIndexSetString(8, PLOT_LABEL, "1h Low Band");
    
    PlotIndexSetInteger(9, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(9, PLOT_LINE_COLOR, clrWhite);
    PlotIndexSetInteger(9, PLOT_LINE_WIDTH, 2);
    PlotIndexSetString(9, PLOT_LABEL, "FEX ALL");

    // --- Signal Plots ---
    PlotIndexSetInteger(10, PLOT_DRAW_TYPE, DRAW_ARROW);
    PlotIndexSetInteger(10, PLOT_ARROW, 233); // Up arrow
    PlotIndexSetInteger(10, PLOT_LINE_COLOR, clrLime);
    PlotIndexSetInteger(10, PLOT_LINE_WIDTH, 2);
    PlotIndexSetString(10, PLOT_LABEL, "Strong Buy");

    PlotIndexSetInteger(11, PLOT_DRAW_TYPE, DRAW_ARROW);
    PlotIndexSetInteger(11, PLOT_ARROW, 241); // Small up arrow
    PlotIndexSetInteger(11, PLOT_LINE_COLOR, clrAqua);
    PlotIndexSetInteger(11, PLOT_LINE_WIDTH, 1);
    PlotIndexSetString(11, PLOT_LABEL, "Weak Buy");

    PlotIndexSetInteger(12, PLOT_DRAW_TYPE, DRAW_ARROW);
    PlotIndexSetInteger(12, PLOT_ARROW, 234); // Down arrow
    PlotIndexSetInteger(12, PLOT_LINE_COLOR, clrRed);
    PlotIndexSetInteger(12, PLOT_LINE_WIDTH, 2);
    PlotIndexSetString(12, PLOT_LABEL, "Strong Sell");

    PlotIndexSetInteger(13, PLOT_DRAW_TYPE, DRAW_ARROW);
    PlotIndexSetInteger(13, PLOT_ARROW, 242); // Small down arrow
    PlotIndexSetInteger(13, PLOT_LINE_COLOR, clrOrangeRed);
    PlotIndexSetInteger(13, PLOT_LINE_WIDTH, 1);
    PlotIndexSetString(13, PLOT_LABEL, "Weak Sell");

    PlotIndexSetInteger(14, PLOT_DRAW_TYPE, DRAW_ARROW);
    PlotIndexSetInteger(14, PLOT_ARROW, 221); // Star or custom
    PlotIndexSetInteger(14, PLOT_LINE_COLOR, clrYellow);
    PlotIndexSetInteger(14, PLOT_LINE_WIDTH, 2);
    PlotIndexSetString(14, PLOT_LABEL, "High Signal");

    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Helper Functions                                                 |
//+------------------------------------------------------------------+

// Check if current symbol is Gold
bool IsGold()
{
    return (StringFind(Symbol(), "XAU") >= 0 || StringFind(Symbol(), "GOLD") >= 0);
}

// Check if current symbol is Bitcoin
bool IsBitcoin()
{
    return (StringFind(Symbol(), "BTC") >= 0);
}

// Get timeframe multiplier
int GetTimeframeMultiplier(ENUM_TIMEFRAMES tf)
{
    switch(tf)
    {
        case PERIOD_M1: return 1;
        case PERIOD_M3: return 3;
        case PERIOD_M5: return 5;
        case PERIOD_M15: return 15;
        case PERIOD_M30: return 30;
        case PERIOD_H1: return 60;
        case PERIOD_H4: return 240;
        case PERIOD_D1: return 1440;
        case PERIOD_W1: return 10080;
        case PERIOD_MN1: return 43200;
        default: return 0;
    }
}

// Check timeframe conditions
bool IsTimeframe(int multiplier)
{
    return (GetTimeframeMultiplier(Period()) == multiplier);
}

// Gaussian Filter Function
double f_filt9x_Gaus(double alpha, double source, int order, int shift)
{
    static double f_Gaus[10][1000]; // Static array to store previous values
    
    if(shift >= 1000) return 0; // Safety check
    
    double x_Gaus = 1 - alpha;
    int m2_Gaus = 0, m3_Gaus = 0, m4_Gaus = 0, m5_Gaus = 0;
    int m6_Gaus = 0, m7_Gaus = 0, m8_Gaus = 0, m9_Gaus = 0;
    
    // Weights calculation
    switch(order)
    {
        case 9: m2_Gaus = 36; m3_Gaus = 84; m4_Gaus = 126; m5_Gaus = 126; m6_Gaus = 84; m7_Gaus = 36; m8_Gaus = 9; m9_Gaus = 1; break;
        case 8: m2_Gaus = 28; m3_Gaus = 56; m4_Gaus = 70; m5_Gaus = 56; m6_Gaus = 28; m7_Gaus = 8; m8_Gaus = 1; break;
        case 7: m2_Gaus = 21; m3_Gaus = 35; m4_Gaus = 35; m5_Gaus = 21; m6_Gaus = 7; m7_Gaus = 1; break;
        case 6: m2_Gaus = 15; m3_Gaus = 20; m4_Gaus = 15; m5_Gaus = 6; m6_Gaus = 1; break;
        case 5: m2_Gaus = 10; m3_Gaus = 10; m4_Gaus = 5; m5_Gaus = 1; break;
        case 4: m2_Gaus = 6; m3_Gaus = 4; m4_Gaus = 1; break;
        case 3: m2_Gaus = 3; m3_Gaus = 1; break;
        case 2: m2_Gaus = 1; break;
    }
    
    double result = MathPow(alpha, order) * source + 
                   order * x_Gaus * f_Gaus[order-1][shift];
    
    if(order >= 2) result -= m2_Gaus * MathPow(x_Gaus, 2) * f_Gaus[order-2][shift];
    if(order >= 3) result += m3_Gaus * MathPow(x_Gaus, 3) * f_Gaus[order-3][shift];
    if(order >= 4) result -= m4_Gaus * MathPow(x_Gaus, 4) * f_Gaus[order-4][shift];
    if(order >= 5) result += m5_Gaus * MathPow(x_Gaus, 5) * f_Gaus[order-5][shift];
    if(order >= 6) result -= m6_Gaus * MathPow(x_Gaus, 6) * f_Gaus[order-6][shift];
    if(order >= 7) result += m7_Gaus * MathPow(x_Gaus, 7) * f_Gaus[order-7][shift];
    if(order >= 8) result -= m8_Gaus * MathPow(x_Gaus, 8) * f_Gaus[order-8][shift];
    if(order == 9) result += m9_Gaus * MathPow(x_Gaus, 9) * f_Gaus[0][shift];
    
    f_Gaus[order][shift] = result;
    return result;
}

// Calculate Gaussian Filter for specific timeframe
void CalcGausFilter(string timeframe, double &filt, double &hband, double &lband, double &fcolor, int shift)
{
    int per_Gaus;
    double mult;
    
    // Set parameters based on timeframe
    if(timeframe == "M5")
    {
        per_Gaus = 369;
        mult = 5.39;
    }
    else if(timeframe == "H4")
    {
        per_Gaus = 111;
        mult = 2.39;
    }
    else if(timeframe == "H1")
    {
        per_Gaus = 186;
        mult = 2.69;
    }
    else if(timeframe == "M15")
    {
        per_Gaus = 211;
        mult = 2.69;
    }
    else if(timeframe == "D1")
    {
        per_Gaus = 150;
        mult = 1.6;
    }
    else if(timeframe == "W1")
    {
        per_Gaus = 60;
        mult = 1.414;
    }
    else
    {
        per_Gaus = 222;
        mult = 1.414;
    }
    
    // Beta and Alpha Components
    double beta_Gaus = (1 - MathCos(4 * M_PI / 2 / per_Gaus)) / (MathPow(1.414, 0.5) - 1);
    double alpha_Gaus = -beta_Gaus + MathSqrt(MathPow(beta_Gaus, 2) + 2 * beta_Gaus);
    
    // Lag
    int lag_Gaus = (per_Gaus - 1) / (2 * 4);
    
    // Data
    double ohlc4_val = (iOpen(Symbol(), PERIOD_CURRENT, shift) + 
                       iHigh(Symbol(), PERIOD_CURRENT, shift) + 
                       iLow(Symbol(), PERIOD_CURRENT, shift) + 
                       iClose(Symbol(), PERIOD_CURRENT, shift)) / 4;
    
    double srcdata_Gaus = modeLag_Gaus ? 
        ohlc4_val + ohlc4_val - (shift + lag_Gaus < Bars(Symbol(), PERIOD_CURRENT) ? 
        (iOpen(Symbol(), PERIOD_CURRENT, shift + lag_Gaus) + 
         iHigh(Symbol(), PERIOD_CURRENT, shift + lag_Gaus) + 
         iLow(Symbol(), PERIOD_CURRENT, shift + lag_Gaus) + 
         iClose(Symbol(), PERIOD_CURRENT, shift + lag_Gaus)) / 4 : ohlc4_val) : ohlc4_val;
    
    // Calculate filter
    double filtn_Gaus = f_filt9x_Gaus(alpha_Gaus, srcdata_Gaus, 4, shift);
    double filt1_Gaus = f_filt9x_Gaus(alpha_Gaus, srcdata_Gaus, 1, shift);
    
    // Lag Reduction
    filt = modeFast_Gaus ? (filtn_Gaus + filt1_Gaus) / 2 : filtn_Gaus;
    
    // Calculate cloud size
    double sum = 0;
    double mean = 0;
    int count = MathMin(per_Gaus * 2, Bars(Symbol(), PERIOD_CURRENT) - shift);
    
    for(int i = 0; i < count; i++)
    {
        if(shift + i < Bars(Symbol(), PERIOD_CURRENT))
            sum += iClose(Symbol(), PERIOD_CURRENT, shift + i);
    }
    mean = sum / count;
    
    sum = 0;
    for(int i = 0; i < count; i++)
    {
        if(shift + i < Bars(Symbol(), PERIOD_CURRENT))
        {
            double diff = iClose(Symbol(), PERIOD_CURRENT, shift + i) - mean;
            sum += diff * diff;
        }
    }
    double stdev = MathSqrt(sum / count);
    double local_cloudSize = stdev / 4;
    
    // Bands
    hband = filtn_Gaus + mult * local_cloudSize;
    lband = filtn_Gaus - mult * local_cloudSize;
    
    // Color
    if(shift > 0 && shift < Bars(Symbol(), PERIOD_CURRENT))
    {
        double prev_filt = shift + 1 < Bars(Symbol(), PERIOD_CURRENT) ? Filter1hBuffer[shift + 1] : filt;
        fcolor = filt > prev_filt ? 1 : filt < prev_filt ? -1 : 0;
    }
    else
        fcolor = 0;
}

// Calculate Alpha for specific timeframe
double CalcAlpha(string tf, int shift)
{
    double factor_alpha;
    
    if(tf == "W1") factor_alpha = 2.79;
    else if(tf == "D1") factor_alpha = 3.69;
    else if(tf == "H4") factor_alpha = 3.369;
    else if(tf == "H1") factor_alpha = 2.8;
    else if(tf == "M15") factor_alpha = 4.963;
    else factor_alpha = 3;
    
    // Get timeframe data
    ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT;
    if(tf == "W1") timeframe = PERIOD_W1;
    else if(tf == "D1") timeframe = PERIOD_D1;
    else if(tf == "H4") timeframe = PERIOD_H4;
    else if(tf == "H1") timeframe = PERIOD_H1;
    else if(tf == "M15") timeframe = PERIOD_M15;
    
    double tfHigh = iHigh(Symbol(), timeframe, shift);
    double tfLow = iLow(Symbol(), timeframe, shift);
    double tfClose = iClose(Symbol(), timeframe, shift);
    double tfHLC3 = (tfHigh + tfLow + tfClose) / 3;
    
    // Calculate True Range
    double tfTR = 0;
    if(shift > 0)
    {
        double prev_close = iClose(Symbol(), timeframe, shift + 1);
        tfTR = MathMax(tfHigh - tfLow, MathMax(MathAbs(tfHigh - prev_close), MathAbs(tfLow - prev_close)));
    }
    else
        tfTR = tfHigh - tfLow;
    
    // Calculate TR SMA
    double tfTR_SMA = 0;
    int count = MathMin(69, Bars(Symbol(), timeframe) - shift);
    for(int i = 0; i < count; i++)
    {
        if(shift + i < Bars(Symbol(), timeframe))
        {
            double high_i = iHigh(Symbol(), timeframe, shift + i);
            double low_i = iLow(Symbol(), timeframe, shift + i);
            double tr_i = high_i - low_i;
            if(shift + i + 1 < Bars(Symbol(), timeframe))
            {
                double prev_close_i = iClose(Symbol(), timeframe, shift + i + 1);
                tr_i = MathMax(tr_i, MathMax(MathAbs(high_i - prev_close_i), MathAbs(low_i - prev_close_i)));
            }
            tfTR_SMA += tr_i;
        }
    }
    tfTR_SMA /= count;
    
    // Calculate MFI (simplified)
    double tfMFI = 50; // Simplified for now
    
    // Calculate alpha
    static double alpha_prev = 0;
    double upT_S = tfLow - tfTR_SMA * factor_alpha;
    double downT_S = tfHigh + tfTR_SMA * factor_alpha;
    
    double alpha;
    if(tfMFI >= 50)
        alpha = upT_S < alpha_prev ? alpha_prev : upT_S;
    else
        alpha = downT_S > alpha_prev ? alpha_prev : downT_S;
    
    alpha_prev = alpha;
    return alpha;
}

// Normalize function
double Normalize(double value, double minValue, double maxValue)
{
    double range = maxValue - minValue == 0 ? 0.0001 : maxValue - minValue;
    return -1 + (value - minValue) / range * 2;
}

// Round function
double Round_(double val)
{
    if(val > 0.99) return 0.999;
    if(val < -0.99) return -0.999;
    return val;
}

// Supersmoother function
double Supersmoother(double src, int length, double pi, int shift)
{
    static double ss[1000];
    if(shift >= 1000) return src;
    
    double s_a1 = MathExp(-MathSqrt(2) * pi / length);
    double s_b1 = 2 * s_a1 * MathCos(MathSqrt(2) * pi / length);
    double s_c3 = -MathPow(s_a1, 2);
    double s_c2 = s_b1;
    double s_c1 = 1 - s_c2 - s_c3;
    
    double prev1 = shift + 1 < 1000 ? ss[shift + 1] : src;
    double prev2 = shift + 2 < 1000 ? ss[shift + 2] : src;
    
    ss[shift] = s_c1 * src + s_c2 * prev1 + s_c3 * prev2;
    return ss[shift];
}

// Calculate FEX_ALL and OutBound
void CalcFexAllOutbound(double &fex_all, int &outbound, int shift)
{
    // Get current timeframe
    ENUM_TIMEFRAMES current_tf = Period();
    int multiplier = GetTimeframeMultiplier(current_tf);
    
    // Set parameters based on timeframe
    int Period_val = 100;
    double Factor_FEX = 2.5;
    double Delta_tom = 90.0;
    int RSI_Period = 24;
    int Factor_plus = IsBitcoin() ? 5 : 0;
    int Factor_plus_rsi = IsBitcoin() ? 3 : 0;
    
    if(multiplier == 5 || multiplier == 1 || multiplier == 15 || multiplier == 30)
    {
        Factor_FEX = 3;
        Delta_tom = 93.9;
        RSI_Period = 26;
        Period_val = 269;
    }
    else if(multiplier == 60)
    {
        Factor_FEX = 2.7;
        Delta_tom = 83.9 + Factor_plus;
        RSI_Period = 24 + Factor_plus_rsi;
        Period_val = 179;
    }
    else if(multiplier == 240)
    {
        Factor_FEX = 2.7;
        Delta_tom = 83.9 + Factor_plus;
        RSI_Period = 24 + Factor_plus_rsi;
        Period_val = 179;
    }
    else if(multiplier == 1440) // Daily
    {
        Factor_FEX = 2;
        Delta_tom = 80 + Factor_plus;
        RSI_Period = 23 + Factor_plus_rsi;
    }
    else if(multiplier == 10080) // Weekly
    {
        Factor_FEX = 2;
        Delta_tom = 78 + Factor_plus;
        RSI_Period = 22 + Factor_plus_rsi;
    }
    else if(multiplier == 43200) // Monthly
    {
        Factor_FEX = 2;
        Delta_tom = 76 + Factor_plus;
        RSI_Period = 21 + Factor_plus_rsi;
    }
    
    // Calculate indicators
    // 1. EX Indicator
    double Closema = 0, Openma = 0, Highma = 0, Lowma = 0;
    for(int i = 0; i < Period_val && shift + i < Bars(Symbol(), PERIOD_CURRENT); i++)
    {
        Closema += iClose(Symbol(), PERIOD_CURRENT, shift + i);
        Openma += iOpen(Symbol(), PERIOD_CURRENT, shift + i);
        Highma += iHigh(Symbol(), PERIOD_CURRENT, shift + i);
        Lowma += iLow(Symbol(), PERIOD_CURRENT, shift + i);
    }
    Closema /= Period_val;
    Openma /= Period_val;
    Highma /= Period_val;
    Lowma /= Period_val;
    
    double CloseSpread = iClose(Symbol(), PERIOD_CURRENT, shift) - Closema;
    double OpenSpread = iOpen(Symbol(), PERIOD_CURRENT, shift) - Openma;
    double HighSpread = iHigh(Symbol(), PERIOD_CURRENT, shift) - Highma;
    double LowSpread = iLow(Symbol(), PERIOD_CURRENT, shift) - Lowma;
    
    // Calculate standard deviations
    double CloseStdev = 0, OpenStdev = 0, HighStdev = 0, LowStdev = 0;
    for(int i = 0; i < Period_val && shift + i < Bars(Symbol(), PERIOD_CURRENT); i++)
    {
        double close_diff = iClose(Symbol(), PERIOD_CURRENT, shift + i) - Closema;
        double open_diff = iOpen(Symbol(), PERIOD_CURRENT, shift + i) - Openma;
        double high_diff = iHigh(Symbol(), PERIOD_CURRENT, shift + i) - Highma;
        double low_diff = iLow(Symbol(), PERIOD_CURRENT, shift + i) - Lowma;
        
        CloseStdev += close_diff * close_diff;
        OpenStdev += open_diff * open_diff;
        HighStdev += high_diff * high_diff;
        LowStdev += low_diff * low_diff;
    }
    CloseStdev = MathSqrt(CloseStdev / Period_val);
    OpenStdev = MathSqrt(OpenStdev / Period_val);
    HighStdev = MathSqrt(HighStdev / Period_val);
    LowStdev = MathSqrt(LowStdev / Period_val);
    
    double ZscoreClose = CloseStdev != 0 ? CloseSpread / CloseStdev : 0;
    
    // 2. Fisher Transform
    double high_139 = iHigh(Symbol(), PERIOD_CURRENT, shift);
    double low_139 = iLow(Symbol(), PERIOD_CURRENT, shift);
    for(int i = 1; i < 139 && shift + i < Bars(Symbol(), PERIOD_CURRENT); i++)
    {
        high_139 = MathMax(high_139, iHigh(Symbol(), PERIOD_CURRENT, shift + i));
        low_139 = MathMin(low_139, iLow(Symbol(), PERIOD_CURRENT, shift + i));
    }
    
    double hl2 = (iHigh(Symbol(), PERIOD_CURRENT, shift) + iLow(Symbol(), PERIOD_CURRENT, shift)) / 2;
    double value = 0.66 * ((hl2 - low_139) / (high_139 - low_139 == 0 ? 0.0001 : high_139 - low_139) - 0.5);
    double fish1 = 0.5 * MathLog((1 + value) / (1 - value == 0 ? 0.0001 : 1 - value));
    
    // 3. BLSH Composite
    double atrValue = 0;
    for(int i = 0; i < 12 && shift + i < Bars(Symbol(), PERIOD_CURRENT); i++)
    {
        double tr = iHigh(Symbol(), PERIOD_CURRENT, shift + i) - iLow(Symbol(), PERIOD_CURRENT, shift + i);
        if(shift + i + 1 < Bars(Symbol(), PERIOD_CURRENT))
        {
            double prev_close = iClose(Symbol(), PERIOD_CURRENT, shift + i + 1);
            tr = MathMax(tr, MathMax(MathAbs(iHigh(Symbol(), PERIOD_CURRENT, shift + i) - prev_close),
                                   MathAbs(iLow(Symbol(), PERIOD_CURRENT, shift + i) - prev_close)));
        }
        atrValue += tr;
    }
    atrValue /= 12;
    
    double priceRange = Factor_FEX * atrValue;
    
    // RSI calculation (simplified)
    double rsiValue = 50; // Simplified
    double rsiValueNormalized = Normalize(rsiValue, 27, 77);
    
    // EMA calculations (simplified)
    double emaDiff = 0; // Simplified
    double emaDiffNormalized = Normalize(emaDiff, -priceRange, priceRange);
    
    // MACD calculations (simplified)
    double macd = 0; // Simplified
    double macdHistogramNormalized = Normalize(macd, -priceRange, priceRange);
    
    // MFI calculations (simplified)
    double mfiValue = 50; // Simplified
    double mfiValueNormalized = Normalize(mfiValue, 25, 75);
    
    double compositeValue = emaDiffNormalized + rsiValueNormalized + macdHistogramNormalized + mfiValueNormalized;
    double compositeNormalized = Normalize(compositeValue, -4, 4);
    
    // 4. QQE
    double RSI_ALL = 50; // Simplified
    double RsiMa2 = RSI_ALL; // Simplified
    
    // 5. Stochastic
    double SMI = 0; // Simplified
    double emasignal = 0; // Simplified
    
    // Calculate FEX components
    double FEX_E = ZscoreClose > 5.3 ? 5.3 : ZscoreClose < -5.3 ? -5.3 : ZscoreClose * 0.85;
    double FEX_F = fish1 * 6 / 7 > 6 ? 6 : fish1 * 6 / 7;
    double FEX_BLSH = 5.3 * compositeNormalized > 5.3 ? 5.3 : 5.3 * compositeNormalized;
    double FEX_QQE = (RsiMa2 - 50) * 0.3 > 5.5 ? 5.5 : (RsiMa2 - 50) * 0.3 < -5.5 ? -5.5 : (RsiMa2 - 50) * 0.3;
    double FEX_RSI = ((RSI_ALL - 50) / 5.5) > 5.5 ? 5.5 : ((RSI_ALL - 50) / 5.5) < -5.5 ? -5.5 : ((RSI_ALL - 50) / 5.5);
    double FEX_STOC = emasignal > 77 ? 5.3 : emasignal < -77 ? -5.3 : emasignal * 5.3 / 77;
    
    double FEX = (FEX_E + FEX_F + FEX_BLSH + FEX_QQE + FEX_RSI + FEX_STOC) / 6;
    double FEX_EMA = FEX; // Simplified
    double FEX_TOM = MathRound((FEX + FEX_EMA) / 2 * 18.1 * 1000) / 1000;
    
    // 7. Tom wave trend
    double hlc3 = (iHigh(Symbol(), PERIOD_CURRENT, shift) + iLow(Symbol(), PERIOD_CURRENT, shift) + iClose(Symbol(), PERIOD_CURRENT, shift)) / 3;
    double esa = hlc3; // Simplified
    double d = MathAbs(hlc3 - esa); // Simplified
    double ci = d != 0 ? (hlc3 - esa) / (0.015 * d) : 0;
    double ema_cn = 100 * ci / 57; // Simplified
    double tom = ema_cn > 93 ? 93 + (ema_cn - 93) / 3 : ema_cn < -93 ? -93 + (93 + ema_cn) / 3 : ema_cn;
    
    double BTC_factor = IsBitcoin() ? 0.9 : 1;
    fex_all = (100 * (FEX_TOM + 0.92 * tom) / 2 / 87) * BTC_factor;
    
    // Calculate OutBound (simplified)
    outbound = 0;
    if(iClose(Symbol(), PERIOD_CURRENT, shift) > iHigh(Symbol(), PERIOD_CURRENT, shift) * 1.1)
        outbound = 1;
    else if(iClose(Symbol(), PERIOD_CURRENT, shift) < iLow(Symbol(), PERIOD_CURRENT, shift) * 0.9)
        outbound = -1;
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    int limit = prev_calculated == 0 ? rates_total - 1 : rates_total - prev_calculated;
    Print("OnCalculate called, rates_total=", rates_total, ", prev_calculated=", prev_calculated);
    for(int i = limit; i >= 0; i--)
    {
        // Calculate Alpha values for different timeframes
        Alpha15Buffer[i] = CalcAlpha("M15", i);
        Alpha60Buffer[i] = CalcAlpha("H1", i);
        Alpha240Buffer[i] = CalcAlpha("H4", i);
        AlphaDailyBuffer[i] = CalcAlpha("D1", i);
        AlphaWeeklyBuffer[i] = CalcAlpha("W1", i);
        
        // Calculate Gaussian filters
        double filt_5m, hband_5m, lband_5m, fcolor_5m;
        double filt_1h, hband_1h, lband_1h, fcolor_1h;
        
        CalcGausFilter("M5", filt_5m, hband_5m, lband_5m, fcolor_5m, i);
        CalcGausFilter("H1", filt_1h, hband_1h, lband_1h, fcolor_1h, i);
        
        Filter5mBuffer[i] = filt_5m;
        Filter1hBuffer[i] = filt_1h;
        HBand1hBuffer[i] = hband_1h;
        LBand1hBuffer[i] = lband_1h;
        
        // Calculate FEX_ALL
        double fex_all;
        int outbound;
        CalcFexAllOutbound(fex_all, outbound, i);
        FEXAllBuffer[i] = fex_all;
        
        // Calculate trend signals
        bool alpha_condition = Alpha15Buffer[i] > Alpha60Buffer[i];
        bool hourly_trend_up = (i < rates_total - 1) ? Filter1hBuffer[i] > Filter1hBuffer[i + 1] : false;
        bool price_above_5m_filter = close[i] > Filter5mBuffer[i];
        bool price_below_5m_filter = close[i] < Filter5mBuffer[i];
        bool price_max_alphas_bullish = close[i] > MathMax(Alpha15Buffer[i], Alpha60Buffer[i]);
        bool price_min_alphas_bearish = close[i] < MathMin(Alpha15Buffer[i], Alpha60Buffer[i]);
        
        // Initialize signal buffers
        StrongBuyBuffer[i] = EMPTY_VALUE;
        WeakBuyBuffer[i] = EMPTY_VALUE;
        StrongSellBuffer[i] = EMPTY_VALUE;
        WeakSellBuffer[i] = EMPTY_VALUE;
        HighSignalBuffer[i] = EMPTY_VALUE;
        
        // Determine trend signals
        if(alpha_condition && hourly_trend_up && price_above_5m_filter && price_max_alphas_bullish)
        {
            StrongBuyBuffer[i] = low[i] - 50 * Point();
        }
        else if(price_above_5m_filter || price_max_alphas_bullish || (alpha_condition && !hourly_trend_up))
        {
            WeakBuyBuffer[i] = low[i] - 30 * Point();
        }
        else if(!alpha_condition && !hourly_trend_up && price_below_5m_filter && price_min_alphas_bearish)
        {
            StrongSellBuffer[i] = high[i] + 50 * Point();
        }
        else if(price_below_5m_filter || price_min_alphas_bearish || (!alpha_condition && hourly_trend_up))
        {
            WeakSellBuffer[i] = high[i] + 30 * Point();
        }
        
        // High/Low FEX signals
        if((FEXAllBuffer[i] >= 92 && Alpha60Buffer[i] > 85) || 
           (FEXAllBuffer[i] >= 97 && Alpha15Buffer[i] > 85))
        {
            HighSignalBuffer[i] = high[i] + 100 * Point();
        }
        else if((FEXAllBuffer[i] <= -90 && Alpha60Buffer[i] < -85) || 
                (FEXAllBuffer[i] <= -91 && IsGold()) ||
                (FEXAllBuffer[i] <= -90 && Alpha15Buffer[i] < -85))
        {
            HighSignalBuffer[i] = low[i] - 100 * Point();
        }
    }
    
    return(rates_total);
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                      |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Cleanup if needed
}
