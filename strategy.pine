// @version=5
indicator("<PERSON>", overlay=true)

// Define colors for each timeframe
color_15min = color.new(#0022FC, 0)    // Blue for 15min
color_60min = color.new(#00B300, 0)    // Green for 1h
color_240min = color.new(#000000, 0)   // Black for 4h
color_daily = color.new(#FF0000, 0)    // Read for Daily
color_weekly = color.new(#FFA500, 0)   // Orange for Weekly
is1min = timeframe.isintraday and timeframe.multiplier == 1
is3min = timeframe.isintraday and timeframe.multiplier == 3
is5min = timeframe.isintraday and timeframe.multiplier == 5
is15min = timeframe.isintraday and timeframe.multiplier == 15
is30min = timeframe.isintraday and timeframe.multiplier == 30
is60min = timeframe.isintraday and timeframe.multiplier == 60
is240min = timeframe.isintraday and timeframe.multiplier == 240
isday = timeframe.isdaily
isweek = timeframe.isweekly
ismonth = timeframe.ismonthly
isdwm = timeframe.isdwm
isGold = syminfo.ticker == "XAUUSD"
isBitcoin = syminfo.ticker == "BTCUSD"

//Gaus Indicator Filter function 
baseCloudEmaLength = ismonth ? 60 : isweek ? 52 : isday ? 66 : is240min ? 60 : is60min ? 45 : is15min ? 179 : 122
cloudSize = ta.stdev(close, baseCloudEmaLength * 2) / 4
f_filt9x_Gaus(_a_Gaus, _s_Gaus, _i_Gaus) =>
    int _m2_Gaus = 0
    int _m3_Gaus = 0
    int _m4_Gaus = 0
    int _m5_Gaus = 0
    int _m6_Gaus = 0
    int _m7_Gaus = 0
    int _m8_Gaus = 0
    int _m9_Gaus = 0
    float _f_Gaus = .0
    _x_Gaus = 1 - _a_Gaus
    // Weights. 
    _m2_Gaus := _i_Gaus == 9 ? 36 : _i_Gaus == 8 ? 28 : _i_Gaus == 7 ? 21 : _i_Gaus == 6 ? 15 : _i_Gaus == 5 ? 10 : _i_Gaus == 4 ? 6 : _i_Gaus == 3 ? 3 : _i_Gaus == 2 ? 1 : 0
    _m3_Gaus := _i_Gaus == 9 ? 84 : _i_Gaus == 8 ? 56 : _i_Gaus == 7 ? 35 : _i_Gaus == 6 ? 20 : _i_Gaus == 5 ? 10 : _i_Gaus == 4 ? 4 : _i_Gaus == 3 ? 1 : 0
    _m4_Gaus := _i_Gaus == 9 ? 126 : _i_Gaus == 8 ? 70 : _i_Gaus == 7 ? 35 : _i_Gaus == 6 ? 15 : _i_Gaus == 5 ? 5 : _i_Gaus == 4 ? 1 : 0
    _m5_Gaus := _i_Gaus == 9 ? 126 : _i_Gaus == 8 ? 56 : _i_Gaus == 7 ? 21 : _i_Gaus == 6 ? 6 : _i_Gaus == 5 ? 1 : 0
    _m6_Gaus := _i_Gaus == 9 ? 84 : _i_Gaus == 8 ? 28 : _i_Gaus == 7 ? 7 : _i_Gaus == 6 ? 1 : 0
    _m7_Gaus := _i_Gaus == 9 ? 36 : _i_Gaus == 8 ? 8 : _i_Gaus == 7 ? 1 : 0
    _m8_Gaus := _i_Gaus == 9 ? 9 : _i_Gaus == 8 ? 1 : 0
    _m9_Gaus := _i_Gaus == 9 ? 1 : 0
    _f_Gaus := math.pow(_a_Gaus, _i_Gaus) * nz(_s_Gaus) + _i_Gaus * _x_Gaus * nz(_f_Gaus[1]) - (_i_Gaus >= 2 ? _m2_Gaus * math.pow(_x_Gaus, 2) * nz(_f_Gaus[2]) : 0) + (_i_Gaus >= 3 ? _m3_Gaus * math.pow(_x_Gaus, 3) * nz(_f_Gaus[3]) : 0) - (_i_Gaus >= 4 ? _m4_Gaus * math.pow(_x_Gaus, 4) * nz(_f_Gaus[4]) : 0) + (_i_Gaus >= 5 ? _m5_Gaus * math.pow(_x_Gaus, 5) * nz(_f_Gaus[5]) : 0) - (_i_Gaus >= 6 ? _m6_Gaus * math.pow(_x_Gaus, 6) * nz(_f_Gaus[6]) : 0) + (_i_Gaus >= 7 ? _m7_Gaus * math.pow(_x_Gaus, 7) * nz(_f_Gaus[7]) : 0) - (_i_Gaus >= 8 ? _m8_Gaus * math.pow(_x_Gaus, 8) * nz(_f_Gaus[8]) : 0) + (_i_Gaus == 9 ? _m9_Gaus * math.pow(_x_Gaus, 9) * nz(_f_Gaus[9]) : 0)
    _f_Gaus

f_pole_Gaus(_a, _s, _i) =>
    _f1 = f_filt9x_Gaus(_a, _s, 1)
    _f2 = _i >= 2 ? f_filt9x_Gaus(_a, _s, 2) : 0
    _f3 = _i >= 3 ? f_filt9x_Gaus(_a, _s, 3) : 0
    _f4 = _i >= 4 ? f_filt9x_Gaus(_a, _s, 4) : 0
    _f5 = _i >= 5 ? f_filt9x_Gaus(_a, _s, 5) : 0
    _f6 = _i >= 6 ? f_filt9x_Gaus(_a, _s, 6) : 0
    _f7 = _i >= 2 ? f_filt9x_Gaus(_a, _s, 7) : 0
    _f8 = _i >= 8 ? f_filt9x_Gaus(_a, _s, 8) : 0
    _f9 = _i == 9 ? f_filt9x_Gaus(_a, _s, 9) : 0
    _fn = _i == 1 ? _f1 : _i == 2 ? _f2 : _i == 3 ? _f3 : _i == 4 ? _f4 : _i == 5 ? _f5 : _i == 6 ? _f6 : _i == 7 ? _f7 : _i == 8 ? _f8 : _i == 9 ? _f9 : na
    [_fn, _f1]

// Period
src_Gaus = ohlc4
per_Gaus = ismonth ? 72 : isweek ? 60 : isday ? 150 : is240min ? 111 : is60min ? 186 : is15min ? 211 : is5min ? 369 : is3min ? 500 : is1min ? 1500 : 222
per_Gaus := per_Gaus == 0 ? 222 : per_Gaus

// True Range Multiplier
mult_Gaus = ismonth ? 1.414 : isweek ? 1.414 : isday ? 1.6 : 1.414

// Lag Reduction
bool modeLag_Gaus = input(defval=false, title='Reduced Lag Mode')
bool modeFast_Gaus = input(defval=false, title='Fast Response Mode')

// Beta and Alpha Components
beta_Gaus = (1 - math.cos(4 * math.asin(1) / per_Gaus)) / (math.pow(1.414, 0.5) - 1)
alpha_Gaus = -beta_Gaus + math.sqrt(math.pow(beta_Gaus, 2) + 2 * beta_Gaus)

// Lag
lag_Gaus = (per_Gaus - 1) / (2 * 4)

// Data
srcdata_Gaus = modeLag_Gaus ? ohlc4 + ohlc4 - ohlc4[lag_Gaus] : ohlc4
trdata_Gaus = modeLag_Gaus ? ta.tr(true) + ta.tr(true) - ta.tr(true)[lag_Gaus] : ta.tr(true)


[filtn_Gaus, filt1_Gaus] = f_pole_Gaus(alpha_Gaus, srcdata_Gaus, 4)
[filtntr_Gaus, filt1tr_Gaus] = f_pole_Gaus(alpha_Gaus, trdata_Gaus, 4)

//Lag Reduction
filt_Gaus = modeFast_Gaus ? (filtn_Gaus + filt1_Gaus) / 2 : filtn_Gaus
filttr_Gaus = modeFast_Gaus ? (filtntr_Gaus + filt1tr_Gaus) / 2 : filtntr_Gaus

// Bands
hband_Gaus = is5min ? (filtn_Gaus + 5.39*cloudSize) : (is3min) ? (filtn_Gaus + 6.39*cloudSize): (is1min) ? (filtn_Gaus + 6.39*cloudSize) : is15min ? (filtn_Gaus + 3.69*cloudSize) : is60min ? (filtn_Gaus + 3.69*cloudSize) : is240min ? (filtn_Gaus + 2.39*cloudSize) : (filtn_Gaus + 1.41* cloudSize)
lband_Gaus = is5min ? (filtn_Gaus - 5.39*cloudSize) : (is3min) ? (filtn_Gaus - 6.39*cloudSize): (is1min) ? (filtn_Gaus - 6.39*cloudSize) : is15min ? (filtn_Gaus - 2.69*cloudSize) : is60min ? (filtn_Gaus - 2.69*cloudSize) : is240min ? (filtn_Gaus - 2.39*cloudSize) : (filtn_Gaus - 1.41*cloudSize)

//hband_Gaus = filtn_Gaus + cloudSize * mult_Gaus
//lband_Gaus = filtn_Gaus - cloudSize * mult_Gaus

colorGausUp = #0aff68
colorGausDn = #ff0a5a
colorDefault = #cccccc
fcolor_Gaus = filt_Gaus > filt_Gaus[1] ? colorGausUp : filt_Gaus < filt_Gaus[1] ? colorGausDn : colorDefault
//-----------------------------------------------------------------------------------------------------------------------------------------------------------------
//Outputs
//-----------------------------------------------------------------------------------------------------------------------------------------------------------------

// Function to calculate filter consistently for a specific timeframe
calcGausFilter(timeframe) =>
    // Set period based on timeframe
    local_per_Gaus = timeframe == "5" ? 369 : 
                     timeframe == "240" ? 111 : 
                     timeframe == "60" ? 186 : 
                     timeframe == "15" ? 211 : 
                     timeframe == "D" ? 150 : 
                     timeframe == "W" ? 60 : 222
    
    // Beta and Alpha Components
    local_beta_Gaus = (1 - math.cos(4 * math.asin(1) / local_per_Gaus)) / (math.pow(1.414, 0.5) - 1)
    local_alpha_Gaus = -local_beta_Gaus + math.sqrt(math.pow(local_beta_Gaus, 2) + 2 * local_beta_Gaus)
    
    // Lag
    local_lag_Gaus = (local_per_Gaus - 1) / (2 * 4)
    
    // Data
    local_srcdata_Gaus = modeLag_Gaus ? ohlc4 + ohlc4 - ohlc4[local_lag_Gaus] : ohlc4
    local_trdata_Gaus = modeLag_Gaus ? ta.tr(true) + ta.tr(true) - ta.tr(true)[local_lag_Gaus] : ta.tr(true)
    
    // Calculate filter
    [local_filtn_Gaus, local_filt1_Gaus] = f_pole_Gaus(local_alpha_Gaus, local_srcdata_Gaus, 4)
    [local_filtntr_Gaus, local_filt1tr_Gaus] = f_pole_Gaus(local_alpha_Gaus, local_trdata_Gaus, 4)
    
    // Lag Reduction
    local_filt_Gaus = modeFast_Gaus ? (local_filtn_Gaus + local_filt1_Gaus) / 2 : local_filtn_Gaus
    local_filttr_Gaus = modeFast_Gaus ? (local_filtntr_Gaus + local_filt1tr_Gaus) / 2 : local_filtntr_Gaus
    
    // Bands and multiplier based on timeframe
    local_cloudSize = ta.stdev(close, local_per_Gaus * 2) / 4
    local_mult = timeframe == "5" ? 5.39 : 
                 timeframe == "240" ? 2.39 : 
                 timeframe == "60" ? 2.69 : 
                 timeframe == "15" ? 2.69 : 
                 timeframe == "D" ? 1.6 : 
                 timeframe == "W" ? 1.414 : 1.414
    
    local_hband_Gaus = local_filtn_Gaus + local_mult * local_cloudSize
    local_lband_Gaus = local_filtn_Gaus - local_mult * local_cloudSize
    
    // Color
    local_fcolor_Gaus = local_filt_Gaus > local_filt_Gaus[1] ? colorGausUp : local_filt_Gaus < local_filt_Gaus[1] ? colorGausDn : colorDefault
    
    [local_filt_Gaus, local_hband_Gaus, local_lband_Gaus, local_fcolor_Gaus]

// Get consistent filter values for different timeframes
[filt_Gaus_5m, hband_Gaus_5m, lband_Gaus_5m, fcolor_Gaus_5m] = request.security(syminfo.tickerid, "5", calcGausFilter("5"), lookahead=barmerge.lookahead_off)
[filt_Gaus_15m, hband_Gaus_15m, lband_Gaus_15m, fcolor_Gaus_15m] = request.security(syminfo.tickerid, "15", calcGausFilter("15"), lookahead=barmerge.lookahead_off)
[filt_Gaus_1h, hband_Gaus_1h, lband_Gaus_1h, fcolor_Gaus_1h] = request.security(syminfo.tickerid, "60", calcGausFilter("60"), lookahead=barmerge.lookahead_off)
[filt_Gaus_4h, hband_Gaus_4h, lband_Gaus_4h, fcolor_Gaus_4h] = request.security(syminfo.tickerid, "240", calcGausFilter("240"), lookahead=barmerge.lookahead_off)
[filt_Gaus_D, hband_Gaus_D, lband_Gaus_D, fcolor_Gaus_D] = request.security(syminfo.tickerid, "D", calcGausFilter("D"), lookahead=barmerge.lookahead_off)
[filt_Gaus_W, hband_Gaus_W, lband_Gaus_W, fcolor_Gaus_W] = request.security(syminfo.tickerid, "W", calcGausFilter("W"), lookahead=barmerge.lookahead_off)

// Function to calculate alpha for a specific timeframe
calcAlpha(tf) =>
    // Get data from the specified timeframe
    factor_alpha = tf == "W" ? 2.79 : 
                     tf == "D" ? 3.69 : 
                     tf == "240" ? 3.369 : 
                     tf == "60" ? 2.8 : 
                     tf == "15" ? 4.963 : 3
    
    // Request security data for the specified timeframe
    [tfHigh, tfLow, tfClose] = request.security(syminfo.tickerid, tf, [high, low, close], lookahead=barmerge.lookahead_off)
    tfHLC3 = (tfHigh + tfLow + tfClose) / 3
    
    // Calculate True Range for the timeframe
    tfTR = request.security(syminfo.tickerid, tf, ta.tr, lookahead=barmerge.lookahead_off)
    tfTR_SMA = request.security(syminfo.tickerid, tf, ta.sma(ta.tr, 69), lookahead=barmerge.lookahead_off)
    
    // Calculate MFI for the timeframe
    tfMFI = request.security(syminfo.tickerid, tf, ta.mfi(hlc3, 69), lookahead=barmerge.lookahead_off)
    
    // Calculate upT_S and downT_S
    upT_S = tfLow - tfTR_SMA * factor_alpha
    downT_S = tfHigh + tfTR_SMA * factor_alpha
    
    // Initialize alpha
    var float alpha = 0.0
    
    // Update alpha based on conditions
    alpha := tfMFI >= 50 ? 
                 upT_S < nz(alpha[1]) ? nz(alpha[1]) : upT_S : 
                 downT_S > nz(alpha[1]) ? nz(alpha[1]) : downT_S
    
    alpha

// Function to calculate alpha consistently within each timeframe
calcConsistentAlpha(tf) =>
    // Calculate alpha completely within the specified timeframe
    result = calcAlpha(tf)
    result

// Calculate alpha for each timeframe consistently
alpha15 = request.security(syminfo.tickerid, "15", calcConsistentAlpha("15"), lookahead=barmerge.lookahead_off)
alpha60 = request.security(syminfo.tickerid, "60", calcConsistentAlpha("60"), lookahead=barmerge.lookahead_off)
alpha240 = request.security(syminfo.tickerid, "240", calcConsistentAlpha("240"), lookahead=barmerge.lookahead_off)
alphaDaily = request.security(syminfo.tickerid, "D", calcConsistentAlpha("D"), lookahead=barmerge.lookahead_off)
alphaWeekly = request.security(syminfo.tickerid, "W", calcConsistentAlpha("W"), lookahead=barmerge.lookahead_off)

// Get 5-minute candle data
[high_5m, low_5m, close_5m] = request.security(syminfo.tickerid, "5", [high[1], low[1], close[1]], lookahead=barmerge.lookahead_off)


// Plot all timeframes simultaneously
plot(alpha15, title="Alpha 15m", color=color_15min, linewidth=1)
plot(alpha60, title="Alpha 1h", color=color_60min, linewidth=2)
plot(alpha240, title="Alpha 4h", color=color_240min, linewidth=2)
plot(alphaDaily, title="Alpha D", color=color_daily, linewidth=2)
plot(alphaWeekly, title="Alpha W", color=color_weekly, linewidth=3)


// Now that we have the Gaussian filter values, update the trend detection
// This will override the simple price comparison we used earlier

// Function to calculate 1h trend direction consistently within the 1h timeframe
calcHourlyTrend() =>
    hourly_up = filt_Gaus_1h[1] > filt_Gaus_1h[2]
    hourly_up
calcMinutelyTrend() =>
    minutely_up = filt_Gaus_5m[1] > filt_Gaus_5m[2]
    minutely_up
// Get the hourly trend that only changes when 1h candle closes
filt_Gaus_1h_up = request.security(syminfo.tickerid, "60", calcHourlyTrend(), lookahead=barmerge.lookahead_off)
filt_Gaus_5m_up = request.security(syminfo.tickerid, "5", calcMinutelyTrend(), lookahead=barmerge.lookahead_off)
// Function to calculate alpha comparison consistently within the 15m timeframe
calcAlphaComparison() =>
    alpha_comparison = alpha15[1] > alpha60[2]
    alpha_comparison

// Get the alpha comparison that only changes when 15m candle closes
alpha15_gt_alpha60 = request.security(syminfo.tickerid, "15", calcAlphaComparison(), lookahead=barmerge.lookahead_off)

// Calculate conditions for trend states using consistent values - mutually exclusive cases
// Primary conditions
bool alpha_condition = alpha15_gt_alpha60
bool hourly_trend_up = filt_Gaus_1h_up
bool price_above_5m_filter = close_5m > filt_Gaus_5m
bool price_below_5m_filter = close_5m < filt_Gaus_5m
bool price_max_alphas_bullish = close_5m > math.max(alpha15, alpha60)
bool price_min_alphas_bearish = close_5m < math.min(alpha15, alpha60)

// Four mutually exclusive cases - using if/else logic to ensure only one is true
bool strongBuy = false
bool weakBuy = false
bool strongSell = false
bool weakSell = false

if alpha_condition and hourly_trend_up and price_above_5m_filter and price_max_alphas_bullish
    strongBuy := true
else if price_above_5m_filter or price_max_alphas_bullish or (alpha15_gt_alpha60 and not filt_Gaus_1h_up)
    weakBuy := true
else if not alpha_condition and not hourly_trend_up and price_below_5m_filter and price_min_alphas_bearish
    strongSell := true
else if price_below_5m_filter or price_min_alphas_bearish or (not alpha15_gt_alpha60 and filt_Gaus_1h_up)
    weakSell := true
else
    strongBuy := strongBuy
    weakBuy := weakBuy
    strongSell := strongSell
    weakSell := weakSell
    
// shift status dependent on 5minutes Gaus
if weakBuy == true and not filt_Gaus_5m_up
    weakBuy := false
    weakSell := true
if weakSell == true and filt_Gaus_5m_up
    weakSell := false
    weakBuy := true
if strongBuy == true and not filt_Gaus_5m_up
    strongBuy := false
    weakBuy := true
if strongSell == true and filt_Gaus_5m_up
    strongSell := false
    weakSell := true

// Plot 5-minute filter (fixed timeframe)
filtplot_5m = plot(filt_Gaus_5m, title='5m Filter', color=fcolor_Gaus_5m, linewidth=3)
//hbandplot_5m = plot(hband_Gaus_5m, title='5m Filtered High Band', color=fcolor_Gaus_5m)
//lbandplot_5m = plot(lband_Gaus_5m, title='5m Filtered Low Band', color=fcolor_Gaus_5m)
//fill(hbandplot_5m, lbandplot_5m, title='5m Channel Fill', color=color.new(fcolor_Gaus_5m, 80))

// Plot 15-minute filter (fixed timeframe)
//filtplot_15m = plot(filt_Gaus_15m, title='15m Filter', color=fcolor_Gaus_15m, linewidth=2)
//hbandplot_15m = plot(hband_Gaus_15m, title='15m Filtered High Band', color=fcolor_Gaus_15m)
//lbandplot_15m = plot(lband_Gaus_15m, title='15m Filtered Low Band', color=fcolor_Gaus_15m)
//fill(hbandplot_15m, lbandplot_15m, title='15m Channel Fill', color=color.new(fcolor_Gaus_15m, 80))

// Plot 1-hour filter (fixed timeframe)
filtplot_1h = plot(filt_Gaus_1h, title='1h Filter', color=fcolor_Gaus_1h, linewidth=2)
hbandplot_1h = plot(hband_Gaus_1h, title='1h Filtered High Band', color=fcolor_Gaus_1h)
lbandplot_1h = plot(lband_Gaus_1h, title='1h Filtered Low Band', color=fcolor_Gaus_1h)
fill(hbandplot_1h, lbandplot_1h, title='1h Channel Fill', color=color.new(fcolor_Gaus_1h, 80))

// Plot 4-hour filter (fixed timeframe) - COMMENTED OUT
//filtplot_4h = plot(filt_Gaus_4h, title='4h Filter', color=fcolor_Gaus_4h, linewidth=2)
//hbandplot_4h = plot(hband_Gaus_4h, title='4h Filtered High Band', color=fcolor_Gaus_4h)
//lbandplot_4h = plot(lband_Gaus_4h, title='4h Filtered Low Band', color=fcolor_Gaus_4h)
//fill(hbandplot_4h, lbandplot_4h, title='4h Channel Fill', color=color.new(fcolor_Gaus_4h, 80))

// Plot Daily filter (fixed timeframe)
//filtplot_D = plot(filt_Gaus_D, title='D Filter', color=fcolor_Gaus_D, linewidth=2)
//hbandplot_D = plot(hband_Gaus_D, title='D Filtered High Band', color=fcolor_Gaus_D)
//lbandplot_D = plot(lband_Gaus_D, title='D Filtered Low Band', color=fcolor_Gaus_D)
//fill(hbandplot_D, lbandplot_D, title='D Channel Fill', color=color.new(fcolor_Gaus_D, 80))

// Plot Weekly filter (fixed timeframe)
//filtplot_W = plot(filt_Gaus_W, title='W Filter', color=fcolor_Gaus_W, linewidth=2)
//hbandplot_W = plot(hband_Gaus_W, title='W Filtered High Band', color=fcolor_Gaus_W)
//lbandplot_W = plot(lband_Gaus_W, title='W Filtered Low Band', color=fcolor_Gaus_W)
//fill(hbandplot_W, lbandplot_W, title='W Channel Fill', color=color.new(fcolor_Gaus_W, 80))
 
// Original filter plots (commented out as requested)
//filtplot = plot(filt_Gaus, title='Filter', color=fcolor_Gaus, linewidth=3)
//hbandplot = plot(hband_Gaus, title='Filtered True Range High Band', color=fcolor_Gaus)
//lbandplot = plot(lband_Gaus, title='Filtered True Range Low Band', color=fcolor_Gaus)
//fill(hbandplot, lbandplot, title='Channel Fill', color=color.new(fcolor_Gaus,80))

// Add a table to show which color corresponds to which timeframe and display alpha values
// Create a global variable to store the table
var table legendTable = table.new(position.top_right, 2, 11, bgcolor=color.new(color.black, 70), border_width=1)

// Update the table in the last bar
if (barstate.islast)
    // Update alpha values
        
    // Trend Signal - use the actual trend state variables
    // We're using the strongBuy, weakSell, etc. variables that are based on trendState
    string trendText = strongBuy ? "SB" : 
                      weakSell ? "WS" : 
                      strongSell ? "SS" : 
                      weakBuy ? "WB" : "NA"
    
    color trendColor = strongBuy ? color.green : 
                      strongSell ? color.red : 
                      weakBuy ? color.lime : 
                      weakSell ? color.orange : color.white
    
    table.cell(legendTable, 1, 1, trendText, bgcolor=color.new(color.black, 50), text_color=trendColor, text_size=size.large)
    



import TradingView/ta/5

// Timeframe options
tf_w = input.string("W", "Weekly Timeframe", options=["W"])
tf_d = input.string("D", "Daily Timeframe", options=["D"])
tf_4h = input.string("240", "4-Hour Timeframe", options=["240"])
tf_1h = input.string("60", "1-Hour Timeframe", options=["60"])
tf_15m = input.string("15", "15-Minute Timeframe", options=["15"])

//Combine calculating 7 indicators: Ex, Fisher, BLSH, QQE, RSI, Stochastic and Tom_wave trend
// Helper functions defined at root level
round_(val) => val > .99 ? .999 : val < -.99 ? -.999 : val

normalize_1(value, minValue, maxValue) =>
    range_1 = maxValue - minValue == 0 ? 0.0001 : maxValue - minValue
    -1 + (value - minValue) / range_1 * 2

supersmoother(_src, _length, _pi) =>
    s_a1 = math.exp(-math.sqrt(2) * _pi / _length)
    s_b1 = 2 * s_a1 * math.cos(math.sqrt(2) * _pi / _length)
    s_c3 = -math.pow(s_a1, 2)
    s_c2 = s_b1
    s_c1 = 1 - s_c2 - s_c3
    ss = 0.0
    ss := s_c1 * _src + s_c2 * nz(ss[1], _src[1]) + s_c3 * nz(ss[2], _src[2])
    ss

// Function to calculate FEX_ALL and OutBound for any timeframe
calc_fex_all_outbound() =>
    //1-Indicator EX
    Period = 100
    Factor_FEX = 2.5
    Delta_tom = 90.0
    RSI_Period = 24
    Factor_plus = syminfo.ticker == "BTCUSD" ? 5 : 0
    Factor_plus_rsi = syminfo.ticker == "BTCUSD" ? 3 : 0

    if timeframe.period == '5' or timeframe.period == '1' or timeframe.period == '15' or timeframe.period == '30'
        Factor_FEX := 3
        Delta_tom := 93.9
        RSI_Period := 26
        Period := 269
    else if timeframe.period == '60'
        Factor_FEX := 2.7
        Delta_tom := 83.9 + Factor_plus
        RSI_Period := 24 + Factor_plus_rsi
        Period := 179
    else if timeframe.period == '240'
        Factor_FEX := 2.7
        Delta_tom := 83.9 + Factor_plus
        RSI_Period := 24  + Factor_plus_rsi
        Period := 179
    else if timeframe.period == 'D'
        Factor_FEX := 2
        Delta_tom := 80 + Factor_plus
        RSI_Period := 23  + Factor_plus_rsi
    else if timeframe.period == 'W'
        Factor_FEX := 2
        Delta_tom := 78 + Factor_plus
        RSI_Period := 22  + Factor_plus_rsi
    else if timeframe.period == 'M'
        Factor_FEX := 2
        Delta_tom := 76 + Factor_plus
        RSI_Period := 21  + Factor_plus_rsi
     
    Closema = ta.sma(close, Period)
    Openma = ta.sma(open, Period)
    Highma = ta.sma(high, Period)
    Lowma = ta.sma(low, Period)
    CloseSpread = close - Closema
    OpenSpread = open - Openma
    HighSpread = high - Highma
    LowSpread = low - Lowma

    ZscoreClose = CloseSpread / ta.stdev(CloseSpread, Period)
    ZscoreOpen = OpenSpread / ta.stdev(OpenSpread, Period)
    ZscoreHigh = HighSpread / ta.stdev(HighSpread, Period)
    ZscoreLow = LowSpread / ta.stdev(LowSpread, Period)

    //2-Indicator Fisher Transform
    high_ = ta.highest(hl2, 139)
    low_ = ta.lowest(hl2, 139)
    value = 0.0
    value := round_(.66 * ((hl2 - low_) / (high_ - low_) - .5) + .67 * nz(value[1]))
    fish1 = 0.0
    fish1 := .5 * math.log((1 + value) / (1 - value)) + .5 * nz(fish1[1])

    //3-Indicator Buy Low Sell High Composite-'BLSH'
    atrValue = ta.atr(12)
    priceRange = Factor_FEX * atrValue

    //3.2 RSI
    rsiValue = ta.rsi(close, 22)
    rsiValueNormalized = normalize_1(rsiValue, 27, 77)

    //3.3 Elliot Wave
    emaDiff = ta.ema(close, 17) - ta.ema(close, 66)
    emaDiffNormalized = normalize_1(emaDiff, -priceRange, priceRange)

    //3.4 MACD
    fastMovingAverage = ta.ema(close, 16)
    slowMovingAverage = ta.ema(close, 100)
    macd = fastMovingAverage - slowMovingAverage
    macdSignal = ta.sma(macd, 12)
    macdHistogram = macd - macdSignal
    macdNormalized = normalize_1(macd, -priceRange, priceRange)
    macdSignalNormalized = normalize_1(macdSignal, -priceRange, priceRange)
    macdHistogramNormalized = normalize_1(macdHistogram, -priceRange, priceRange)

    //3.5 MFI
    positiveFlow = math.sum(volume * (ta.change(hlc3) <= 0 ? 0 : hlc3), 14)
    negativeFlow = math.sum(volume * (ta.change(hlc3) >= 0 ? 0 : hlc3), 14)
    mfiValue = 100.0 - 100.0 / (1.0 + positiveFlow / negativeFlow)
    mfiValueNormalized = normalize_1(mfiValue, 25, 75)

    //3.6 Composite
    compositeValue = emaDiffNormalized + rsiValueNormalized + macdHistogramNormalized + mfiValueNormalized
    compositeNormalized = normalize_1(compositeValue, -4, 4)

    //4-Indicator "QQE MOD ALL" => Quantity
    RSI_ALL = ta.rsi(close, RSI_Period)
    RsiMa2 = ta.ema(RSI_ALL, 6) // trung bình SF2=6  của RSI là 6 kỳ

    //6- Indicator Stochastic Tom
    ll = ta.lowest(27)
    hh = ta.highest(27)
    diff = hh - ll
    rdiff = close - (hh+ll)/2
    avgrel = ta.ema(ta.ema(rdiff,9),9)
    avgdiff = ta.ema(ta.ema(diff,9),9)
    // SMI calculations
    SMI = avgdiff != 0 ? (avgrel/(avgdiff/2)*100) : 0
    SMIsignal = ta.ema(SMI,9)
    emasignal =ta.ema(SMI,10)
    mfi_cal = ta.mfi(hlc3, 42) - 50

    FEX_E = ZscoreClose > 5.3 ? 5.3 : ZscoreClose < -5.3 ? -5.3 : ZscoreClose*0.85 //Indicator 1 FEX_E
    FEX_F = fish1*6/7 > 6 ? 6 : fish1*6/7 //Indicator 2 FEX_F
    FEX_BLSH = 5.3*compositeNormalized > 5.3 ? 5.3 : 5.3*compositeNormalized //Indicator 3 FEX_BLSH
    FEX_QQE = (RsiMa2 - 50)*0.3 > 5.5 ? 5.5 : (RsiMa2 - 50)*0.3 < -5.5 ? -5.5 : (RsiMa2 - 50)*0.3 //Indicator 4 FEX_QQE
    FEX_RSI = (((RSI_ALL - 50)/5.5) > 5.5) ? 5.5 : ((RSI_ALL - 50)/5.5) < -5.5 ? -5.5 : ((RSI_ALL - 50)/5.5) //Indicator 5 FEX_RSI
    FEX_STOC = emasignal > 77 ? 5.3 : emasignal < -77 ? -5.3 : emasignal*5.3/77 //Indicator 6 FEX_STOC

    //FEX_MFI = mfi_cal*5.5/30 > 5.5 ? 5.5 : mfi_cal*5.5/30 < -5.5 ? -5.5 : mfi_cal*5.5/30 //Indicator tested => not yet find the suitable value for it.
    //Calculating 6 Indicators before combining with the 7th Indicator.
    FEX = math.avg(FEX_E,FEX_F,FEX_BLSH,FEX_QQE,FEX_RSI,FEX_STOC)
    FEX_EMA = ta.ema(FEX,24)
    FEX_TOM = math.round(math.avg(FEX,FEX_EMA)*18.1,3)

    //7-Indicator Tom_wave trend
    esa = ta.ema(hlc3, 32)//ema of hlc3 with multiple n1
    d = ta.ema(math.abs(hlc3 - esa), 32)//ema of dev between ap vs esa
    ci = (hlc3 - esa) / (0.015 * d)//dev divide ema of dev
    ema_cn = 100*ta.ema(ci, 222)/57
    tom = ema_cn > 93 ? 93 + (ema_cn -93)/3 : ema_cn < -93 ? -93 + (93 + ema_cn)/3 : ema_cn // ema of ci

    BTC_factor = syminfo.ticker == "BTCUSD" ? 0.9 : 1
    FEX_ALL = (100*math.avg(FEX_TOM,0.92*tom)/87 > 0 ? 100*math.avg(FEX_TOM,0.92*tom)/87 : 100*math.avg(FEX_TOM,0.92*tom)/87)*BTC_factor
    
    // Calculate OutBound
    var length = 1234
    var innermult = 4.1
    var outermult = 4.1
    var source = high
    if timeframe.period == '1'
        source := close
        length := 4444
        innermult := 14.4
        outermult := 14.4
    else if timeframe.period == '3'
        source := ohlc4
        length := 1600
        innermult := 11
        outermult := 11
    else if timeframe.period == '5'
        source := ohlc4
        length := 1036
        innermult := 7
        outermult := 7
    else if timeframe.period == '15' or timeframe.period == '30'
        source := ohlc4
        length := 1155
        innermult := 6.6
        outermult := 6.6
    else if timeframe.period == '60' or timeframe.period == '240'
        source := ohlc4
        length := 1234
        innermult := 9
        outermult := 9
    else if timeframe.period == 'D'
        source := ohlc4
        length := 369
        innermult := 3.69
        outermult := 3.69
    else if timeframe.period == 'W'
        source := high
        length := 369
        innermult := 3.6
        outermult := 3.6
    else if timeframe.period == 'M'
        source := high
        length := 369
        innermult := 3.6
        outermult := 3.6
    else
        source := ohlc4
        length := 1234
        innermult := 11
        outermult := 11

    pi = 2 * math.asin(1)
    mult = pi * innermult
    mult2 = pi * outermult
    
    // Get Mean Reversion Channel values
    v_meanline = source
    v_meanrange = supersmoother(ta.tr, length, pi)

    v_meanline := supersmoother(source, length, pi)
    v_upband1 = v_meanline + v_meanrange * mult
    v_loband1 = v_meanline - v_meanrange * mult
    v_upband2 = v_meanline + v_meanrange * mult2
    v_loband2 = v_meanline - v_meanrange * mult2

    // Calculate OutBound
    gradsize = 0.5
    meanrange = v_meanrange
    upband2 = v_upband2
    loband2 = v_loband2
    
    float upband2_1 = upband2 + meanrange * gradsize * 4
    float loband2_1 = loband2 - meanrange * gradsize * 4
    float upband2_9 = upband2 + meanrange * gradsize * -4
    float loband2_9 = loband2 - meanrange * gradsize * -4

    minOutBound = math.min(loband2_1, loband2_9)
    maxOutBound = math.max(upband2_1, upband2_9)

    outBound = 0
    if close > upband2_1
        outBound := 1
    else if close < minOutBound
        outBound := -1
    else 
        outBound := 0

    [FEX_ALL, outBound]

// Calculate FEX_ALL and OutBound for current timeframe
[current_FEX_ALL, current_OutBound] = calc_fex_all_outbound()

// Get FEX_ALL and OutBound from other timeframes
[w_FEX_ALL, w_OutBound] = request.security(syminfo.tickerid, tf_w, calc_fex_all_outbound())
[d_FEX_ALL, d_OutBound] = request.security(syminfo.tickerid, tf_d, calc_fex_all_outbound())
[h4_FEX_ALL, h4_OutBound] = request.security(syminfo.tickerid, tf_4h, calc_fex_all_outbound())
[h1_FEX_ALL, h1_OutBound] = request.security(syminfo.tickerid, tf_1h, calc_fex_all_outbound())
[m15_FEX_ALL, m15_OutBound] = request.security(syminfo.tickerid, tf_15m, calc_fex_all_outbound())

// Color function for FEX_ALL
get_fex_color(fex_value) =>
    FEX_purple_temp = timeframe.period == '5' ? 97 : timeframe.period == '60' or (syminfo.ticker == "BTCUSD") ? 93 : 85
    FEX_purple = (timeframe.period == 'D' or timeframe.period == 'W' or timeframe.period == 'M') ? syminfo.ticker == "XAUUSD" ? 90 : 90 : FEX_purple_temp < 95 ? 95 : FEX_purple_temp
    color_fex_green = syminfo.ticker == "XAUUSD" ? (timeframe.period == "240" ? 75 : timeframe.period == "60" ? 75 : timeframe.period == "15" ? 75 : timeframe.period == "3" ? 77 : timeframe.period == "1" ? 80 : 65) : 60
    color_fex_red = syminfo.ticker == "XAUUSD" ? (timeframe.period == "60" ? -80 : timeframe.period == "15" ? -85 : timeframe.period == "3" ? -87 : timeframe.period == "1" ? -95 : -60) : -65
    
    fex_value > FEX_purple ? color.purple : 
       fex_value > color_fex_green ? color.green : 
       fex_value < -FEX_purple ? color.blue : 
       fex_value < color_fex_red ? color.red : 
       color.black

// Display current timeframe FEX_ALL
//plot(current_FEX_ALL, color=get_fex_color(current_FEX_ALL), title="Current FEX_ALL", linewidth=2)
//plot(w_FEX_ALL, title="Weekly FEX_ALL", display=display.none)
//plot(w_OutBound, title="Weekly OutBound", display=display.none)
//plot(d_FEX_ALL, title="Daily FEX_ALL", display=display.none)
//plot(d_OutBound, title="Daily OutBound", display=display.none)
//plot(h4_FEX_ALL, title="4H FEX_ALL", display=display.none)
//plot(h4_OutBound, title="4H OutBound", display=display.none)
//plot(h1_FEX_ALL, title="1H FEX_ALL", display=display.none)
//plot(h1_OutBound, title="1H OutBound", display=display.none)
//plot(m15_FEX_ALL, title="15M FEX_ALL", display=display.none)
//plot(m15_OutBound, title="15M OutBound", display=display.none)

// Plot points where h4_FEX_ALL >= 92 and h1_FEX_ALL > 85
high_4H_1H_Conndition = h4_FEX_ALL >= 92 and h1_FEX_ALL > 85
plotchar(high_4H_1H_Conndition, title="High 4H-1H FEX Signal", char="▼", location=location.abovebar, color=color.purple, size=size.normal)
low_4H_1H_Conndition = (h4_FEX_ALL <= -90 and h1_FEX_ALL < -85)  or (h4_FEX_ALL <= -91 and isGold)
plotchar(low_4H_1H_Conndition, title="Low 4H-1H FEX Signal", char="▲", location=location.belowbar, color=color.blue, size=size.normal)

// Plot points where h1_FEX_ALL >= 97 and m15_FEX_ALL > 85
high_1H_15M_Condition = h1_FEX_ALL >= 97 and m15_FEX_ALL > 85
plotchar(high_1H_15M_Condition, title="High 1H-15M FEX Signal", char="▼", location=location.abovebar, color=color.purple, size=size.small)
low_1H_15M_Condition = (h1_FEX_ALL <= -90 and m15_FEX_ALL < -85) or (h1_FEX_ALL <= -90 and isGold)
plotchar(low_1H_15M_Condition, title="Low 1H-15M FEX Signal", char="▲", location=location.belowbar, color=color.blue, size=size.small)

// Plot trend signals as shapes with text
// Strong Buy - B with triangle (green)
plotshape(strongBuy, title="Strong Buy", location=location.belowbar, color=color.green, textcolor=color.green, style=shape.triangleup, text="B")

// Weak Buy - W (light green)
plotshape(weakBuy, title="Weak Buy", location=location.belowbar, color=color.new(color.green, 30), textcolor=color.new(color.green, 30), style=shape.triangleup, text="W")

// Strong Sell - S with triangle (red)
plotshape(strongSell, title="Strong Sell", location=location.abovebar, color=color.red, textcolor=color.red, style=shape.triangledown, text="S")

// Weak Sell - W (light red)
plotshape(weakSell, title="Weak Sell", location=location.abovebar, color=color.new(color.red, 30), textcolor=color.new(color.red, 30), style=shape.triangledown, text="W")
