import * as vscode from 'vscode';

// Kiểm tra xem người dùng đã kích hoạt tính năng cao cấp chưa
export function isPremiumUser(): boolean {
    const config = vscode.workspace.getConfiguration('codeContextWhisperer');
    const licenseKey = config.get<string>('licenseKey');
    
    if (!licenseKey) {
        return false;
    }
    
    // Đây là một kiểm tra đơn giản, trong thực tế bạn sẽ cần một hệ thống xác thực mạnh hơn
    return validateLicenseKey(licenseKey);
}

// Xác thực license key
function validateLicenseKey(key: string): boolean {
    // Trong thực tế, bạn sẽ cần kiểm tra với server hoặc sử dụng mã hóa
    // Đây chỉ là một ví dụ đơn giản
    const validKeys = ['PREMIUM-1234', 'PREMIUM-5678', 'PREMIUM-9012'];
    return validKeys.includes(key);
}

// Phân tích mã nguồn bằng AI (giả lập)
export async function analyzeCodeWithAI(code: string, word: string): Promise<string> {
    // Trong thực tế, bạn sẽ gọi API của một dịch vụ AI
    // Đây chỉ là một ví dụ giả lập
    
    if (!isPremiumUser()) {
        return 'Tính năng phân tích AI chỉ có sẵn cho người dùng cao cấp. Mua key với giá 1 USD để mở khóa.';
    }
    
    // Giả lập phân tích AI
    await new Promise(resolve => setTimeout(resolve, 1000)); // Giả lập độ trễ mạng
    
    return `Phân tích AI: "${word}" có vẻ là một ${getRandomType()}. 
    Nó được sử dụng trong ngữ cảnh ${getRandomContext()}. 
    Gợi ý: ${getRandomSuggestion()}`;
}

// Các hàm helper để tạo phản hồi giả lập
function getRandomType(): string {
    const types = ['hàm', 'biến', 'lớp', 'interface', 'component', 'hook', 'utility'];
    return types[Math.floor(Math.random() * types.length)];
}

function getRandomContext(): string {
    const contexts = [
        'xử lý dữ liệu',
        'tương tác với người dùng',
        'giao tiếp mạng',
        'quản lý trạng thái',
        'xử lý sự kiện',
        'render UI',
        'tối ưu hóa hiệu suất'
    ];
    return contexts[Math.floor(Math.random() * contexts.length)];
}

function getRandomSuggestion(): string {
    const suggestions = [
        'Xem xét tách thành các hàm nhỏ hơn để dễ bảo trì',
        'Thêm kiểu dữ liệu để cải thiện tính an toàn',
        'Xem xét sử dụng mẫu thiết kế Factory cho trường hợp này',
        'Có thể tối ưu hóa bằng cách sử dụng memoization',
        'Xem xét thêm xử lý lỗi chi tiết hơn',
        'Đảm bảo giải phóng tài nguyên khi không cần thiết',
        'Xem xét sử dụng async/await thay vì Promise chains'
    ];
    return suggestions[Math.floor(Math.random() * suggestions.length)];
}