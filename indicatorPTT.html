<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PTT Indicator - Technical Documentation</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin-top: 20px;
            margin-bottom: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            color: white;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .nav-tab {
            padding: 15px 25px;
            margin: 0 5px;
            background: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            color: #666;
        }

        .nav-tab:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .section {
            background: white;
            padding: 30px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #667eea;
        }

        .section h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 2em;
            position: relative;
        }

        .section h2::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .section h3 {
            color: #495057;
            margin: 25px 0 15px 0;
            font-size: 1.4em;
        }

        .mermaid {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
            text-align: center;
        }

        .highlight-box {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #e17055;
        }

        .highlight-box h4 {
            color: #2d3436;
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .info-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-top: 4px solid #667eea;
            transition: transform 0.3s ease;
        }

        .info-card:hover {
            transform: translateY(-5px);
        }

        .info-card h4 {
            color: #667eea;
            margin-bottom: 15px;
        }

        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .badge {
            display: inline-block;
            padding: 5px 12px;
            background: #667eea;
            color: white;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
            margin: 2px;
        }

        .badge.success { background: #00b894; }
        .badge.warning { background: #fdcb6e; }
        .badge.danger { background: #e17055; }

        .footer {
            text-align: center;
            padding: 30px;
            background: #2d3436;
            color: white;
            border-radius: 15px;
            margin-top: 40px;
        }

        @media (max-width: 768px) {
            .nav-tabs {
                flex-direction: column;
            }
            
            .nav-tab {
                margin: 5px 0;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .container {
                margin: 10px;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PTT Indicator</h1>
            <p>Pine Trading Tool - Technical Documentation v2.0</p>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('overview', this)">Overview</button>
            <button class="nav-tab" onclick="showTab('gaussian', this)">Gaussian Filter</button>
            <button class="nav-tab" onclick="showTab('calculator', this)">Bull/Bear Calculator</button>
            <button class="nav-tab" onclick="showTab('support-resistance', this)">Support/Resistance</button>
            <button class="nav-tab" onclick="showTab('consistency', this)">Cloud Consistency</button>
            <button class="nav-tab" onclick="showTab('specifications', this)">Specifications</button>
        </div>

        <!-- Overview Tab -->
        <div id="overview" class="tab-content active">
            <div class="section">
                <h2>System Overview</h2>
                <p>The PTT (Pine Trading Tool) indicator consists of <strong>4 main systems</strong> working together:</p>
                
                <div class="info-grid">
                    <div class="info-card">
                        <h4>🔧 Part 1: Gaussian Filter & Trend System</h4>
                        <p>Core filtering and trend detection using advanced Gaussian mathematics for smooth trend lines and accurate signal generation.</p>
                    </div>
                    <div class="info-card">
                        <h4>📊 Part 2: Bull/Bearish Calculator</h4>
                        <p>FEX calculation combining 7 technical indicators with sophisticated penalty/bonus system for market condition assessment.</p>
                    </div>
                    <div class="info-card">
                        <h4>🎯 Part 3: Support/Resistance Dynamic Logic</h4>
                        <p>Smart adjustment system based on price proximity to clouds with smooth distance-based calculations for accurate market structure analysis.</p>
                    </div>
                    <div class="info-card">
                        <h4>🎨 Part 4: Cloud Visualization System</h4>
                        <p>Consistent cloud and signal mapping across all timeframes - 2 clouds for most timeframes, 3 clouds only for Monthly timeframe.</p>
                    </div>
                </div>

                <div class="mermaid">
graph TB
    A[Market Data Input] --> B[Timeframe Detection]
    
    B --> C[Part 1: Gaussian Filter & Trend System]
    B --> D[Part 2: Bull/Bearish Calculator]
    B --> E[Part 3: Support/Resistance Logic]
    B --> F[Part 4: Cloud Visualization]
    
    C --> C1[Gaussian Filters]
    C --> C2[Trend Detection]
    C --> C3[Distance Calculations]
    
    D --> D1[FEX Calculation Engine]
    D --> D2[Penalty/Bonus System]
    D --> D3[Bull/Bear Percentages]
    
    E --> E1[Distance-Based Adjustment]
    E --> E2[Smooth Proximity Factors]
    E --> E3[Dynamic Signal Enhancement]
    
    F --> F1[Consistent Cloud System]
    F --> F2[Signal Mapping]
    F --> F3[Information Table]
                </div>

                <div class="highlight-box">
                    <h4>🎯 Key Updates v2.0</h4>
                    <p><strong>Support/Resistance Dynamic Logic:</strong> Price proximity to clouds now provides smooth, distance-based adjustments instead of binary on/off signals.</p>
                    <p><strong>Perfect Cloud Consistency:</strong> All clouds and signals are now perfectly synchronized across timeframes using exact same filter sources.</p>
                </div>
            </div>
        </div>

        <!-- Gaussian Filter Tab -->
        <div id="gaussian" class="tab-content">
            <div class="section">
                <h2>Gaussian Filter & Trend System</h2>
                
                <h3>Mathematical Foundation</h3>
                <p>The Gaussian filter system uses advanced mathematical principles to create smooth, responsive trend lines that adapt to market conditions.</p>

                <div class="mermaid">
graph TB
    A[Price Input] --> B[Gaussian Filter 1]
    A --> C[Gaussian Filter 2]
    A --> D[Gaussian Filter 3]
    
    B --> E[Short-term Trend]
    C --> F[Medium-term Trend]
    D --> G[Long-term Trend]
    
    E --> H[Distance Calculation]
    F --> H
    G --> H
    
    H --> I[Trend Strength]
    H --> J[Signal Generation]
    
    I --> K[Cloud Formation]
    J --> K
                </div>

                <h3>Filter Parameters</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Filter</th>
                                <th>Period</th>
                                <th>Smoothing</th>
                                <th>Purpose</th>
                                <th>Sensitivity</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span class="badge">Filter 1</span></td>
                                <td>14</td>
                                <td>High</td>
                                <td>Short-term signals</td>
                                <td>High</td>
                            </tr>
                            <tr>
                                <td><span class="badge">Filter 2</span></td>
                                <td>28</td>
                                <td>Medium</td>
                                <td>Medium-term trend</td>
                                <td>Medium</td>
                            </tr>
                            <tr>
                                <td><span class="badge">Filter 3</span></td>
                                <td>56</td>
                                <td>Low</td>
                                <td>Long-term direction</td>
                                <td>Low</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="highlight-box">
                    <h4>🎯 Key Feature</h4>
                    <p><strong>Adaptive Smoothing:</strong> The Gaussian filters automatically adjust their smoothing based on market volatility, providing cleaner signals in choppy markets while maintaining responsiveness in trending conditions.</p>
                </div>
            </div>
        </div>

        <!-- Bull/Bear Calculator Tab -->
        <div id="calculator" class="tab-content">
            <div class="section">
                <h2>Bull/Bear Calculator (FEX System)</h2>
                
                <h3>Core Components</h3>
                <p>The FEX (Forex Exchange) calculation system combines 7 technical indicators with a sophisticated penalty/bonus system.</p>

                <div class="mermaid">
graph TB
    A[7 Technical Indicators] --> B[Base FEX Calculation]
    
    A1[RSI] --> A
    A2[MACD] --> A
    A3[Stochastic] --> A
    A4[Williams %R] --> A
    A5[CCI] --> A
    A6[MFI] --> A
    A7[ADX] --> A
    
    B --> C[Penalty System]
    B --> D[Bonus System]
    
    C --> E[Market Condition Penalties]
    D --> F[Trend Confirmation Bonuses]
    
    E --> G[Final Bull/Bear %]
    F --> G
    
    G --> H[Support/Resistance Adjustment]
    H --> I[Final Output]
                </div>

                <h3>Indicator Weights</h3>
                <div class="info-grid">
                    <div class="info-card">
                        <h4>📈 Momentum Indicators</h4>
                        <p><strong>RSI:</strong> 15% weight</p>
                        <p><strong>Stochastic:</strong> 12% weight</p>
                        <p><strong>Williams %R:</strong> 10% weight</p>
                    </div>
                    <div class="info-card">
                        <h4>📊 Trend Indicators</h4>
                        <p><strong>MACD:</strong> 20% weight</p>
                        <p><strong>ADX:</strong> 18% weight</p>
                    </div>
                    <div class="info-card">
                        <h4>💰 Volume Indicators</h4>
                        <p><strong>MFI:</strong> 15% weight</p>
                        <p><strong>CCI:</strong> 10% weight</p>
                    </div>
                </div>

                <h3>Penalty/Bonus System</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Condition</th>
                                <th>Type</th>
                                <th>Adjustment</th>
                                <th>Reason</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Low Volume</td>
                                <td><span class="badge danger">Penalty</span></td>
                                <td>-15%</td>
                                <td>Unreliable signals</td>
                            </tr>
                            <tr>
                                <td>High Volatility</td>
                                <td><span class="badge warning">Penalty</span></td>
                                <td>-10%</td>
                                <td>Noise reduction</td>
                            </tr>
                            <tr>
                                <td>Trend Confirmation</td>
                                <td><span class="badge success">Bonus</span></td>
                                <td>+20%</td>
                                <td>Strong directional bias</td>
                            </tr>
                            <tr>
                                <td>Multiple Timeframe Alignment</td>
                                <td><span class="badge success">Bonus</span></td>
                                <td>+25%</td>
                                <td>High probability setup</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="highlight-box">
                    <h4>🎯 Key Enhancement</h4>
                    <p><strong>Support/Resistance Integration:</strong> The calculator now incorporates distance-based cloud proximity adjustments before applying penalties, making it more responsive to market structure.</p>
                </div>
            </div>
        </div>

        <!-- Support/Resistance Tab -->
        <div id="support-resistance" class="tab-content">
            <div class="section">
                <h2>Support/Resistance Dynamic Logic</h2>
                
                <h3>Core Concept</h3>
                <p>The system intelligently adjusts Bull/Bear percentages based on price position relative to clouds and signal context, using smooth distance-based calculations.</p>

                <div class="mermaid">
graph TB
    A[Price Position] --> B[Distance Calculation]
    B --> C[Proximity Factor]
    C --> D[Signal Context Analysis]
    
    D --> E[Scenario 1: Conflicting Signals]
    D --> F[Scenario 2: Consensus Signals]
    
    E --> E1[Short=SELL + Medium=BUY]
    E --> E2[Short=BUY + Medium=SELL]
    
    F --> F1[Both=SELL Consensus]
    F --> F2[Both=BUY Consensus]
    
    E1 --> G[±40% Adjustment]
    E2 --> G
    F1 --> H[±50% Adjustment]
    F2 --> H
                </div>

                <h3>Distance-Based Calculation</h3>
                <div class="info-grid">
                    <div class="info-card">
                        <h4>📏 Distance Normalization</h4>
                        <p><code>distance = |close - cloud| / ATR</code></p>
                        <p>Normalized by ATR for consistency across timeframes and volatility levels.</p>
                    </div>
                    <div class="info-card">
                        <h4>🌊 Exponential Decay</h4>
                        <p><code>proximity_factor = exp(-distance²)</code></p>
                        <p>Smooth decay from 100% at cloud to ~2% at 2 ATR distance.</p>
                    </div>
                </div>

                <h3>Scenario Logic</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Scenario</th>
                                <th>Short Signal</th>
                                <th>Medium Signal</th>
                                <th>Price Condition</th>
                                <th>Adjustment</th>
                                <th>Max Effect</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Conflicting 1</td>
                                <td><span class="badge danger">SELL</span></td>
                                <td><span class="badge success">BUY</span></td>
                                <td>Near Medium Cloud</td>
                                <td>+40% Bull</td>
                                <td>Strong Support</td>
                            </tr>
                            <tr>
                                <td>Conflicting 2</td>
                                <td><span class="badge success">BUY</span></td>
                                <td><span class="badge danger">SELL</span></td>
                                <td>Near Short Cloud</td>
                                <td>-40% Bear</td>
                                <td>Strong Resistance</td>
                            </tr>
                            <tr>
                                <td>Consensus Sell</td>
                                <td><span class="badge danger">SELL</span></td>
                                <td><span class="badge danger">SELL</span></td>
                                <td>Above Both Clouds</td>
                                <td>-50% Bear</td>
                                <td>Major Resistance</td>
                            </tr>
                            <tr>
                                <td>Consensus Buy</td>
                                <td><span class="badge success">BUY</span></td>
                                <td><span class="badge success">BUY</span></td>
                                <td>Below Both Clouds</td>
                                <td>+50% Bull</td>
                                <td>Major Support</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="highlight-box">
                    <h4>🎯 Innovation</h4>
                    <p><strong>Smooth Transitions:</strong> Unlike traditional binary support/resistance, this system provides gradual adjustments based on exact distance, creating more nuanced and accurate market structure analysis.</p>
                </div>
            </div>
        </div>

        <!-- Cloud Consistency Tab -->
        <div id="consistency" class="tab-content">
            <div class="section">
                <h2>Cloud Consistency System</h2>
                
                <h3>Timeframe Mapping</h3>
                <p>Perfect synchronization across all timeframes using consistent filter sources and signal mapping.</p>

                <div class="mermaid">
graph TB
    A[Current Timeframe] --> B{Timeframe Detection}
    
    B --> C[1m - 15m]
    B --> D[30m - 4H]
    B --> E[1D - 1W]
    B --> F[1M]
    
    C --> G[2 Clouds: Short + Medium]
    D --> H[2 Clouds: Short + Medium]
    E --> I[2 Clouds: Short + Medium]
    F --> J[3 Clouds: Short + Medium + Long]
    
    G --> K[Signal Mapping]
    H --> K
    I --> K
    J --> K
    
    K --> L[Consistent Display]
                </div>

                <h3>Cloud Configuration</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Timeframe Range</th>
                                <th>Cloud Count</th>
                                <th>Short Cloud</th>
                                <th>Medium Cloud</th>
                                <th>Long Cloud</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1m - 15m</td>
                                <td>2</td>
                                <td>✅ Active</td>
                                <td>✅ Active</td>
                                <td>❌ Hidden</td>
                            </tr>
                            <tr>
                                <td>30m - 4H</td>
                                <td>2</td>
                                <td>✅ Active</td>
                                <td>✅ Active</td>
                                <td>❌ Hidden</td>
                            </tr>
                            <tr>
                                <td>1D - 1W</td>
                                <td>2</td>
                                <td>✅ Active</td>
                                <td>✅ Active</td>
                                <td>❌ Hidden</td>
                            </tr>
                            <tr>
                                <td>1M</td>
                                <td>3</td>
                                <td>✅ Active</td>
                                <td>✅ Active</td>
                                <td>✅ Active</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>Signal Consistency</h3>
                <div class="info-grid">
                    <div class="info-card">
                        <h4>🎯 Same Filter Sources</h4>
                        <p>All clouds use identical Gaussian filter calculations regardless of timeframe.</p>
                    </div>
                    <div class="info-card">
                        <h4>📊 Consistent Signals</h4>
                        <p>BUY/SELL signals maintain same logic across all timeframes.</p>
                    </div>
                    <div class="info-card">
                        <h4>🎨 Visual Harmony</h4>
                        <p>Colors and styling remain consistent for easy recognition.</p>
                    </div>
                    <div class="info-card">
                        <h4>📈 Information Table</h4>
                        <p>Same data structure and formatting across all timeframes.</p>
                    </div>
                </div>

                <div class="highlight-box">
                    <h4>🎯 Perfect Synchronization</h4>
                    <p><strong>Zero Discrepancies:</strong> The new system eliminates all inconsistencies between timeframes. Whether you're on 5m or 1D, the indicator behavior is perfectly predictable and synchronized.</p>
                </div>
            </div>
        </div>

        <!-- Specifications Tab -->
        <div id="specifications" class="tab-content">
            <div class="section">
                <h2>Technical Specifications</h2>
                
                <h3>System Requirements</h3>
                <div class="info-grid">
                    <div class="info-card">
                        <h4>📊 Platform</h4>
                        <p>TradingView Pine Script v5</p>
                        <p>Compatible with all TradingView plans</p>
                    </div>
                    <div class="info-card">
                        <h4>⚡ Performance</h4>
                        <p>Optimized for real-time execution</p>
                        <p>Low CPU usage design</p>
                    </div>
                    <div class="info-card">
                        <h4>🎯 Accuracy</h4>
                        <p>Sub-tick precision calculations</p>
                        <p>No repainting guarantee</p>
                    </div>
                    <div class="info-card">
                        <h4>🔧 Customization</h4>
                        <p>Fully configurable parameters</p>
                        <p>Multiple display options</p>
                    </div>
                </div>

                <h3>Version History</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Version</th>
                                <th>Release Date</th>
                                <th>Key Features</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span class="badge success">v2.0</span></td>
                                <td>Current</td>
                                <td>Support/Resistance Logic, Cloud Consistency</td>
                                <td><span class="badge success">Active</span></td>
                            </tr>
                            <tr>
                                <td><span class="badge">v1.5</span></td>
                                <td>Previous</td>
                                <td>Enhanced FEX Calculator</td>
                                <td><span class="badge warning">Deprecated</span></td>
                            </tr>
                            <tr>
                                <td><span class="badge">v1.0</span></td>
                                <td>Initial</td>
                                <td>Basic Gaussian Filters</td>
                                <td><span class="badge danger">Legacy</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>Configuration Parameters</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Parameter</th>
                                <th>Default</th>
                                <th>Range</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Short Filter Period</td>
                                <td>14</td>
                                <td>5-50</td>
                                <td>Short-term Gaussian filter length</td>
                            </tr>
                            <tr>
                                <td>Medium Filter Period</td>
                                <td>28</td>
                                <td>10-100</td>
                                <td>Medium-term Gaussian filter length</td>
                            </tr>
                            <tr>
                                <td>Long Filter Period</td>
                                <td>56</td>
                                <td>20-200</td>
                                <td>Long-term Gaussian filter length</td>
                            </tr>
                            <tr>
                                <td>FEX Sensitivity</td>
                                <td>1.0</td>
                                <td>0.1-3.0</td>
                                <td>Bull/Bear calculation sensitivity</td>
                            </tr>
                            <tr>
                                <td>Support/Resistance Factor</td>
                                <td>1.5</td>
                                <td>0.5-5.0</td>
                                <td>Distance-based adjustment strength</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="highlight-box">
                    <h4>🎯 Optimization Notes</h4>
                    <p><strong>Default Settings:</strong> The default parameters are optimized for most market conditions. Advanced users can fine-tune based on specific trading styles and market characteristics.</p>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2024 PTT Indicator Documentation. Advanced Pine Script Trading Tool.</p>
            <p>For support and updates, contact the development team.</p>
        </div>
    </div>

    <script>
        // Global variable to track current tab
        let currentTab = 'overview';
        
        // Store original mermaid content to prevent SVG parsing
        const originalMermaidContent = new Map();
        
        // Tab switching functionality with complete mermaid isolation
        function showTab(tabName, element) {
            console.log(`Switching to tab: ${tabName}`);
            
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all nav tabs
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            element.classList.add('active');
            
            // Update current tab
            currentTab = tabName;

            // COMPLETE ISOLATION: Only process mermaid for specific tabs
            if (tabName === 'specifications') {
                console.log('Specifications tab - NO mermaid processing');
                return; // Complete early return - no mermaid code execution
            }
            
            // Only these tabs get mermaid processing
            const mermaidTabs = ['overview', 'gaussian', 'calculator', 'support-resistance', 'consistency'];
            
            if (mermaidTabs.includes(tabName)) {
                console.log(`Processing mermaid for tab: ${tabName}`);
                
                // Use longer delay for stability
                setTimeout(() => {
                    try {
                        const activeTab = document.getElementById(tabName);
                        if (!activeTab) {
                            console.log(`Tab element not found: ${tabName}`);
                            return;
                        }
                        
                        const mermaidElements = activeTab.querySelectorAll('.mermaid');
                        console.log(`Found ${mermaidElements.length} mermaid elements in ${tabName}`);
                        
                        if (mermaidElements.length > 0) {
                            mermaidElements.forEach((element, index) => {
                                const elementKey = `${tabName}-${index}`;
                                
                                // Get original content from storage or element
                                let originalContent;
                                if (originalMermaidContent.has(elementKey)) {
                                    originalContent = originalMermaidContent.get(elementKey);
                                    console.log(`Using stored content for ${elementKey}`);
                                } else {
                                    // First time - extract and store original content
                                    const textContent = element.textContent.trim();
                                    // Check if it's already SVG (contains CSS or HTML)
                                    if (textContent.includes('#mermaid-') || textContent.includes('<svg') || textContent.includes('fill:')) {
                                        console.log(`Element ${elementKey} contains SVG, skipping`);
                                        return;
                                    }
                                    originalContent = textContent;
                                    originalMermaidContent.set(elementKey, originalContent);
                                    console.log(`Stored original content for ${elementKey}`);
                                }
                                
                                if (!originalContent) {
                                    console.log(`No original content for ${elementKey}`);
                                    return;
                                }
                                
                                console.log(`Rendering mermaid ${index} in ${tabName}`);
                                
                                // Complete reset with original content
                                element.removeAttribute('data-processed');
                                element.innerHTML = originalContent;
                                
                                // Unique ID with timestamp
                                const uniqueId = `mermaid-${tabName}-${index}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
                                element.id = uniqueId;
                                
                                // Render with modern mermaid API
                                try {
                                    mermaid.render(uniqueId + '-svg', originalContent)
                                        .then(({svg}) => {
                                            element.innerHTML = svg;
                                            console.log(`Successfully rendered mermaid ${index} in ${tabName}`);
                                        })
                                        .catch(error => {
                                            console.log(`Mermaid render error for ${tabName}:`, error);
                                            // Fallback to init method
                                            element.innerHTML = originalContent;
                                            mermaid.init(undefined, element);
                                        });
                                } catch (error) {
                                    console.log(`Mermaid setup error for ${tabName}:`, error);
                                    // Final fallback
                                    element.innerHTML = originalContent;
                                    mermaid.init(undefined, element);
                                }
                            });
                        }
                    } catch (globalError) {
                        console.log(`Global error in ${tabName}:`, globalError);
                    }
                }, 300); // Longer delay for complete stability
            } else {
                console.log(`Tab ${tabName} not in mermaid processing list`);
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded, initializing...');
            
            // Initialize mermaid with safe configuration
            mermaid.initialize({ 
                startOnLoad: false, // We handle rendering manually
                theme: 'default',
                themeVariables: {
                    primaryColor: '#667eea',
                    primaryTextColor: '#333',
                    primaryBorderColor: '#764ba2',
                    lineColor: '#666',
                    secondaryColor: '#f8f9fa',
                    tertiaryColor: '#e9ecef'
                },
                securityLevel: 'loose',
                flowchart: {
                    useMaxWidth: true,
                    htmlLabels: true
                }
            });

            // Store original content and initial render for overview tab
            setTimeout(() => {
                console.log('Initial mermaid render for overview tab');
                const overviewTab = document.getElementById('overview');
                if (overviewTab && overviewTab.classList.contains('active')) {
                    const mermaidElements = overviewTab.querySelectorAll('.mermaid');
                    mermaidElements.forEach((element, index) => {
                        const textContent = element.textContent.trim();
                        if (textContent) {
                            // Store original content for future use
                            const elementKey = `overview-${index}`;
                            originalMermaidContent.set(elementKey, textContent);
                            console.log(`Stored initial content for ${elementKey}`);
                            
                            const uniqueId = `mermaid-initial-${index}-${Date.now()}`;
                            element.id = uniqueId;
                            
                            try {
                                mermaid.render(uniqueId + '-svg', textContent)
                                    .then(({svg}) => {
                                        element.innerHTML = svg;
                                        console.log(`Initial render success for element ${index}`);
                                    })
                                    .catch(error => {
                                        console.log('Initial render error:', error);
                                        element.innerHTML = textContent;
                                        mermaid.init(undefined, element);
                                    });
                            } catch (error) {
                                console.log('Initial setup error:', error);
                                mermaid.init(undefined, element);
                            }
                        }
                    });
                }
            }, 500); // Even longer delay for initial load
            
            // Add smooth scrolling
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>