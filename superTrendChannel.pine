//@version=5
indicator("Camarilla TOM", overlay=true)

isGold = syminfo.ticker == "XAUUSD"
isBitcoin = syminfo.ticker == "BTCUSD"
isETH = syminfo.ticker == "ETHUSD"
isGU = syminfo.ticker == "GBPUSD"
isEU = syminfo.ticker == "EURUSD"
isUC = syminfo.ticker == "USDCAD"
isUF = syminfo.ticker == "USDCHF"
isUJ = syminfo.ticker == "USDJPY"
isAU = syminfo.ticker == "AUDUSD"
isNU = syminfo.ticker == "NZDUSD"
is1min = timeframe.isintraday and timeframe.multiplier == 1
is3min = timeframe.isintraday and timeframe.multiplier == 3
is5min = timeframe.isintraday and timeframe.multiplier == 5
is15min = timeframe.isintraday and timeframe.multiplier == 15
is30min = timeframe.isintraday and timeframe.multiplier == 30
is60min = timeframe.isintraday and timeframe.multiplier == 60
is240min = timeframe.isintraday and timeframe.multiplier == 240
isday = timeframe.isdaily
isweek = timeframe.isweekly
ismonth = timeframe.ismonthly
isdwm = timeframe.isdwm
// D1: Daily trung bình 3 phiên
H_D = request.security(syminfo.tickerid, "D", ta.sma(high[1], 1), lookahead=barmerge.lookahead_on)
L_D = request.security(syminfo.tickerid, "D", ta.sma(low[1], 1), lookahead=barmerge.lookahead_on)
C_D = request.security(syminfo.tickerid, "D", ta.sma(close[1], 1), lookahead=barmerge.lookahead_on)
vol_1h = request.security(syminfo.tickerid, "60", volume)  // volume của khung 1H
atr_1h = request.security(syminfo.tickerid, "60", ta.atr(1))  // ATR của khung 1H
atr_1d = request.security(syminfo.tickerid, "D", ta.atr(10))   // ATR của khung ngày

V_D = request.security(syminfo.tickerid, "D", volume[1], lookahead=barmerge.lookahead_on)
V_avg7 = request.security(syminfo.tickerid, "D", ta.sma(volume, 7), lookahead=barmerge.lookahead_on)
mul_R1 = isGold ? 1.33 : isBitcoin ? 2.369 : isGU ? 2.369 : 1.33
mul_S1 = isGold ? 0.9  : isBitcoin ? 2.369 : isGU ? 2.369 : 1.33
rng_D = H_D - L_D
vol_ratio = V_avg7 / V_D
R1 = C_D + rng_D * mul_R1
S1 = C_D - rng_D * mul_S1

// W1: Weekly trung bình 3 tuần
H_W = request.security(syminfo.tickerid, "W", ta.sma(high[1], 1), lookahead=barmerge.lookahead_on)
L_W = request.security(syminfo.tickerid, "W", ta.sma(low[1], 1), lookahead=barmerge.lookahead_on)
C_W = request.security(syminfo.tickerid, "W", ta.sma(close[1], 1), lookahead=barmerge.lookahead_on)
rng_W = H_W - L_W

R2 = isGold ? R1 + rng_D * 1.639 : R1 + rng_D * 1.639
S2 = isGold ? S1 - rng_D * 2.369 : S1 - rng_D * 2.369

// M1: Monthly trung bình 3 tháng
H_M = request.security(syminfo.tickerid, "M", ta.sma(high[1], 3), lookahead=barmerge.lookahead_on)
L_M = request.security(syminfo.tickerid, "M", ta.sma(low[1], 3), lookahead=barmerge.lookahead_on)
C_M = request.security(syminfo.tickerid, "M", ta.sma(close[1], 3), lookahead=barmerge.lookahead_on)
rng_M = H_M - L_M
//R3 = C_M + rng_M * 1.1 / 2
//S3 = C_M - rng_M * 1.1 / 2

// Mở rộng từ R3, S3
R3 = R2 + 2.963 * math.abs((R2 - R1))
R4 = R3 + 2.963 * math.abs((R3 - R2))
R5 = R4 + 2.963 * math.abs((R4 - R3))
S3 = S2 - 2.963 * math.abs((S1 - S2))
S4 = S3 - 2.963 * math.abs((S2 - S3))
S5 = S4 - 2.963 * math.abs((S3 - S4))
base = (S1 + R1) / 2

// Tính EMA volume-weighted (VWEMA)
length_super = is60min ? 144 : is15min ? 576 : is5min ? 1728 : is3min ? 1728 : is1min ? 1728 : 32
vw_base = base * vol_1h
vwema = ta.ema(vw_base, length_super) / ta.ema(vol_1h, length_super)
Middle = ta.ema((S1 + R1)/2,length_super)
ATR = ta.atr(10)
multiplier = ismonth ? 8.6 : isweek ? 7 : isday ? 7 : is240min ? 7 : 13.69
upper_middle = Middle + ta.ema(ATR,length_super*2) * multiplier
lower_middle = Middle - ta.ema(ATR,length_super*2) * multiplier
//plot (Middle, "Middle", color.blue, 2)
plot (upper_middle, "upper_middle", color.blue, 3)
plot (lower_middle, "lower_middle", color.blue, 3)

//upper_middle_v = vwema + ta.ema(atr_1h,length_super*2) * 13.69
//lower_middle_v = vwema - ta.ema(atr_1h,length_super*2) * 13.69
//plot (vwema, "Middle_v", color.black, 2)
//plot (upper_middle_v, "upper_middle", color.black, 3)
//plot (lower_middle_v, "lower_middle", color.black, 3)
// Vẽ các mức
//plot(R1, "R1", color.red, 1)
//plot(close > R1 ? R2 : na, "R2", color.red, 2)
//plot(close > R2 ? R3 : na, "R3", color.red, 3)
//plot(close > R3 ? R4 : na, "R4", color.purple, 2)
//plot(close > R4 ? R5 : na, "R5", color.purple, 3)

//plot(S1, "S1", color.yellow, 1)
//plot(close < S1 ? S2 : na, "S2", color.yellow, 2)
//plot(close < S2 ? S3 : na, "S3", color.yellow, 3)
//plot(close < S3 ? S4 : na, "S4", color.blue, 2)
//plot(close < S4 ? S5 : na, "S5", color.blue, 3)

// Label ở cuối biểu đồ
//f barstate.islast
//   label_x = bar_index + 3
//
//   // Kháng cự
//   label.new(label_x, R1, "R1: " + str.tostring(R1, format.mintick), xloc=xloc.bar_index, yloc=yloc.price, style=label.style_label_left, color=color.red, textcolor=color.white)
//   if close > R1
//       label.new(label_x, R2, "R2: " + str.tostring(R2, format.mintick), xloc=xloc.bar_index, yloc=yloc.price, style=label.style_label_left, color=color.orange, textcolor=color.white)
//   if close > R2
//       label.new(label_x, R3, "R3: " + str.tostring(R3, format.mintick), xloc=xloc.bar_index, yloc=yloc.price, style=label.style_label_left, color=color.fuchsia, textcolor=color.white)
//   if close > R3
//       label.new(label_x, R4, "R4: " + str.tostring(R4, format.mintick), xloc=xloc.bar_index, yloc=yloc.price, style=label.style_label_left, color=color.maroon, textcolor=color.white)
//   if close > R4
//       label.new(label_x, R5, "R5: " + str.tostring(R5, format.mintick), xloc=xloc.bar_index, yloc=yloc.price, style=label.style_label_left, color=color.maroon, textcolor=color.white)
//
//   // Hỗ trợ
//   label.new(label_x, S1, "S1: " + str.tostring(S1, format.mintick), xloc=xloc.bar_index, yloc=yloc.price, style=label.style_label_left, color=color.green, textcolor=color.white)
//   if close < S1
//       label.new(label_x, S2, "S2: " + str.tostring(S2, format.mintick), xloc=xloc.bar_index, yloc=yloc.price, style=label.style_label_left, color=color.teal, textcolor=color.white)
//   if close < S2
//       label.new(label_x, S3, "S3: " + str.tostring(S3, format.mintick), xloc=xloc.bar_index, yloc=yloc.price, style=label.style_label_left, color=color.navy, textcolor=color.white)
//   if close < S3
//       label.new(label_x, S4, "S4: " + str.tostring(S4, format.mintick), xloc=xloc.bar_index, yloc=yloc.price, style=label.style_label_left, color=color.blue, textcolor=color.white)
//   if close < S4
//       label.new(label_x, S5, "S5: " + str.tostring(S5, format.mintick), xloc=xloc.bar_index, yloc=yloc.price, style=label.style_label_left, color=color.blue, textcolor=color.white)
