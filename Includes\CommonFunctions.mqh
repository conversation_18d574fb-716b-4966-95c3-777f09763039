//+------------------------------------------------------------------+
//| CommonFunctions.mqh - Các hàm tiện ích chung                     |
//+------------------------------------------------------------------+
#include "Constants.mqh"

//+------------------------------------------------------------------+
//| Hàm làm tròn giá trị                                             |
//+------------------------------------------------------------------+
double Round(double val) {
    return val > 0.99 ? 0.999 : val < -0.99 ? -0.999 : val;
}

//+------------------------------------------------------------------+
//| Hàm chuẩn hóa giá trị về khoảng [-1, 1]                          |
//+------------------------------------------------------------------+
double Normalize1(double value, double minValue, double maxValue) {
    double range = maxValue - minValue;
    if(range == 0) range = 0.0001;
    return -1 + (value - minValue) / range * 2;
}

//+------------------------------------------------------------------+
//| Hàm SuperSmoother                                                |
//+------------------------------------------------------------------+
double SuperSmoother(double src[], int length, double pi, int shift = 0) {
    double a1 = MathExp(-MathSqrt(2) * pi / length);
    double b1 = 2 * a1 * MathCos(MathSqrt(2) * pi / length);
    double c3 = -MathPow(a1, 2);
    double c2 = b1;
    double c1 = 1 - c2 - c3;
    
    static double ss1 = 0, ss2 = 0;
    double ss = c1 * src[shift] + c2 * ss1 + c3 * ss2;
    
    ss2 = ss1;
    ss1 = ss;
    
    return ss;
}

//+------------------------------------------------------------------+
//| Tính giá trị trung bình của một mảng                             |
//+------------------------------------------------------------------+
double MathAvg(double &array[], int count) {
    if(count <= 0) return 0;
    
    double sum = 0;
    for(int i=0; i<count; i++) {
        sum += array[i];
    }
    
    return sum / count;
}

//+------------------------------------------------------------------+
//| Tính giá trị trung bình của hai số                               |
//+------------------------------------------------------------------+
double MathAvg(double a, double b) {
    return (a + b) / 2;
}

//+------------------------------------------------------------------+
//| Chuyển đổi string timeframe sang ENUM_TIMEFRAMES                 |
//+------------------------------------------------------------------+
ENUM_TIMEFRAMES StringToTimeFrame(string tf) {
    if(tf == "1") return PERIOD_M1;
    if(tf == "3") return PERIOD_M3;
    if(tf == "5") return PERIOD_M5;
    if(tf == "15") return PERIOD_M15;
    if(tf == "30") return PERIOD_M30;
    if(tf == "60") return PERIOD_H1;
    if(tf == "240") return PERIOD_H4;
    if(tf == "D") return PERIOD_D1;
    if(tf == "W") return PERIOD_W1;
    if(tf == "MN") return PERIOD_MN1;
    return PERIOD_CURRENT;
}

//+------------------------------------------------------------------+
//| Lấy giá trị OHLC4                                                |
//+------------------------------------------------------------------+
double OHLC4(int shift = 0) {
    return (Open[shift] + High[shift] + Low[shift] + Close[shift]) / 4;
}

//+------------------------------------------------------------------+
//| Lấy giá trị HLC3                                                 |
//+------------------------------------------------------------------+
double HLC3(int shift = 0) {
    return (High[shift] + Low[shift] + Close[shift]) / 3;
}

//+------------------------------------------------------------------+
//| Lấy giá trị HL2                                                  |
//+------------------------------------------------------------------+
double HL2(int shift = 0) {
    return (High[shift] + Low[shift]) / 2;
}

//+------------------------------------------------------------------+
//| Kiểm tra giá trị NaN                                             |
//+------------------------------------------------------------------+
double NZ(double value, double defaultValue = 0) {
    return MathIsValidNumber(value) ? value : defaultValue;
}