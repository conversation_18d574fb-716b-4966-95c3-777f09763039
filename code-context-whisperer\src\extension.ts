import * as vscode from 'vscode';
import { isPremiumUser, analyzeCodeWithAI } from './premium-features';

export function activate(context: vscode.ExtensionContext) {
    console.log('Extension "Code Context Whisperer" is now active!');

    // Đăng ký lệnh để kích hoạt tính năng cao cấp
    let activatePremiumCommand = vscode.commands.registerCommand('code-context-whisperer.activatePremium', async () => {
        const licenseKey = await vscode.window.showInputBox({
            placeHolder: 'Nhập license key của bạn',
            prompt: 'Nhập license key để kích hoạt tính năng cao cấp'
        });
        
        if (!licenseKey) {
            vscode.window.showErrorMessage('Không có license key nào được nhập');
            return;
        }
        
        // Lưu license key vào cài đặt
        const config = vscode.workspace.getConfiguration('codeContextWhisperer');
        await config.update('licenseKey', licenseKey, true);
        
        // Kiểm tra xem key có hợp lệ không
        if (isPremiumUser()) {
            vscode.window.showInformationMessage('Tính năng cao cấp đã được kích hoạt thành công!');
        } else {
            vscode.window.showErrorMessage('License key không hợp lệ. Vui lòng thử lại.');
        }
    });
    
    context.subscriptions.push(activatePremiumCommand);

    // Đăng ký hover provider cho tất cả các ngôn ngữ
    let disposable = vscode.languages.registerHoverProvider('*', {
        async provideHover(document, position) {
            const wordRange = document.getWordRangeAtPosition(position);
            if (!wordRange) {
                return null;
            }
            
            const word = document.getText(wordRange);
            if (!word || word.trim() === '') {
                return null;
            }
            
            const contextInfo = await findContextInProject(word);
            return new vscode.Hover(`Ngữ cảnh: ${contextInfo}`);
        }
    });

    context.subscriptions.push(disposable);

    // Đăng ký lệnh để hiển thị ngữ cảnh
    let commandDisposable = vscode.commands.registerCommand('code-context-whisperer.showContext', async () => {
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            const position = editor.selection.active;
            const wordRange = editor.document.getWordRangeAtPosition(position);
            
            if (!wordRange) {
                vscode.window.showInformationMessage('Không có từ khóa nào được chọn');
                return;
            }
            
            const word = editor.document.getText(wordRange);
            vscode.window.showInformationMessage('Đang tìm kiếm ngữ cảnh...');
            
            const contextInfo = await findContextInProject(word);
            vscode.window.showInformationMessage(`Ngữ cảnh của "${word}": ${contextInfo}`);
        } else {
            vscode.window.showInformationMessage('Không có editor nào đang mở');
        }
    });

    context.subscriptions.push(commandDisposable);
}

async function findContextInProject(word: string): Promise<string> {
    // Triển khai logic tìm kiếm ngữ cảnh trong dự án
    if (!word || word.trim() === '') {
        return 'Không có từ khóa để tìm kiếm';
    }

    try {
        // Tìm tất cả các file trong workspace
        const files = await vscode.workspace.findFiles('**/*.{ts,js,tsx,jsx,py,java,cs}', '**/node_modules/**');
        
        if (files.length === 0) {
            return 'Không tìm thấy file nào trong dự án';
        }

        let definitions: string[] = [];
        let usages: string[] = [];
        let sampleCode: string = '';

        // Tìm kiếm trong mỗi file
        for (const file of files) {
            try {
                const document = await vscode.workspace.openTextDocument(file);
                const text = document.getText();
                const fileName = file.path.split('/').pop() || file.path;

                // Tìm định nghĩa (đơn giản: function tên, class tên, const/let/var tên =)
                const defRegexes = [
                    new RegExp(`function\\s+${word}\\s*\\(`, 'g'),
                    new RegExp(`class\\s+${word}\\s*[{\\(]`, 'g'),
                    new RegExp(`(const|let|var)\\s+${word}\\s*=`, 'g'),
                    new RegExp(`interface\\s+${word}\\s*[{<]`, 'g'),
                    new RegExp(`type\\s+${word}\\s*=`, 'g')
                ];

                for (const regex of defRegexes) {
                    if (regex.test(text)) {
                        definitions.push(`${fileName}`);
                        
                        // Lưu mẫu code cho phân tích AI
                        if (!sampleCode) {
                            // Tìm vị trí của định nghĩa
                            const match = new RegExp(`(function|class|const|let|var|interface|type)\\s+${word}[\\s\\S]{0,500}`, 'g').exec(text);
                            if (match) {
                                sampleCode = match[0];
                            }
                        }
                        
                        break;
                    }
                }

                // Tìm cách sử dụng (đơn giản: tên(...), new tên(), tên.)
                const usageRegexes = [
                    new RegExp(`${word}\\s*\\(`, 'g'),
                    new RegExp(`new\\s+${word}\\s*\\(`, 'g'),
                    new RegExp(`${word}\\.`, 'g')
                ];

                for (const regex of usageRegexes) {
                    if (regex.test(text)) {
                        // Đếm số lần xuất hiện
                        const matches = text.match(regex);
                        if (matches) {
                            usages.push(`${fileName} (${matches.length} lần)`);
                            break;
                        }
                    }
                }
            } catch (err) {
                console.error(`Lỗi khi đọc file ${file.path}:`, err);
            }
        }

        // Tạo thông tin ngữ cảnh
        let contextInfo = '';
        
        if (definitions.length > 0) {
            contextInfo += `Định nghĩa trong: ${definitions.join(', ')}. `;
        } else {
            contextInfo += 'Không tìm thấy định nghĩa. ';
        }
        
        if (usages.length > 0) {
            contextInfo += `Được sử dụng trong: ${usages.join(', ')}`;
        } else {
            contextInfo += 'Không tìm thấy cách sử dụng.';
        }
        
        // Thêm phân tích AI cho người dùng cao cấp
        if (isPremiumUser() && sampleCode) {
            const aiAnalysis = await analyzeCodeWithAI(sampleCode, word);
            contextInfo += `\n\n${aiAnalysis}`;
        } else if (sampleCode) {
            contextInfo += '\n\n[Nâng cấp lên phiên bản cao cấp để nhận phân tích AI]';
        }
        
        return contextInfo;
    } catch (error) {
        console.error('Lỗi khi tìm kiếm ngữ cảnh:', error);
        return 'Đã xảy ra lỗi khi tìm kiếm ngữ cảnh';
    }
}

export function deactivate() {}