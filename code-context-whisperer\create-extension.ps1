# Script để tạo extension VS Code tự động
Set-Location -Path "e:/IT/Strategy/code-context-whisperer"

# Tạo file package.json với thông tin extension
$packageJson = @"
{
  "name": "code-context-whisperer",
  "displayName": "Code Context Whisperer",
  "description": "<PERSON><PERSON><PERSON> thị ngữ cảnh mã nguồn khi hover chuột",
  "version": "0.0.1",
  "engines": {
    "vscode": "^1.60.0"
  },
  "categories": [
    "Other"
  ],
  "activationEvents": [
    "onLanguage:javascript",
    "onLanguage:typescript",
    "onLanguage:python",
    "onLanguage:java",
    "onLanguage:csharp"
  ],
  "main": "./out/extension.js",
  "contributes": {
    "commands": [
      {
        "command": "code-context-whisperer.showContext",
        "title": "Show Code Context"
      }
    ]
  },
  "scripts": {
    "vscode:prepublish": "npm run compile",
    "compile": "tsc -p ./",
    "watch": "tsc -watch -p ./",
    "pretest": "npm run compile && npm run lint",
    "lint": "eslint src --ext ts",
    "test": "node ./out/test/runTest.js"
  },
  "devDependencies": {
    "@types/vscode": "^1.60.0",
    "@types/glob": "^7.1.3",
    "@types/mocha": "^8.2.2",
    "@types/node": "14.x",
    "eslint": "^7.27.0",
    "@typescript-eslint/eslint-plugin": "^4.26.0",
    "@typescript-eslint/parser": "^4.26.0",
    "glob": "^7.1.7",
    "mocha": "^8.4.0",
    "typescript": "^4.3.2",
    "vscode-test": "^1.5.2"
  }
}
"@

# Tạo cấu trúc thư mục
New-Item -ItemType Directory -Path "src" -Force
New-Item -ItemType Directory -Path "out" -Force
New-Item -ItemType Directory -Path ".vscode" -Force

# Tạo file extension.ts
$extensionTs = @"
import * as vscode from 'vscode';

export function activate(context: vscode.ExtensionContext) {
    console.log('Extension "Code Context Whisperer" is now active!');

    // Đăng ký hover provider cho tất cả các ngôn ngữ
    let disposable = vscode.languages.registerHoverProvider('*', {
        provideHover(document, position) {
            const word = document.getText(document.getWordRangeAtPosition(position));
            const contextInfo = findContextInProject(word);
            return new vscode.Hover(`Ngữ cảnh: ${contextInfo}`);
        }
    });

    context.subscriptions.push(disposable);

    // Đăng ký lệnh để hiển thị ngữ cảnh
    let commandDisposable = vscode.commands.registerCommand('code-context-whisperer.showContext', () => {
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            const position = editor.selection.active;
            const word = editor.document.getText(editor.document.getWordRangeAtPosition(position));
            const contextInfo = findContextInProject(word);
            vscode.window.showInformationMessage(`Ngữ cảnh của "${word}": ${contextInfo}`);
        }
    });

    context.subscriptions.push(commandDisposable);
}

function findContextInProject(word: string): string {
    // TODO: Triển khai logic tìm kiếm ngữ cảnh trong dự án
    // Đây là phiên bản đơn giản, bạn sẽ cần mở rộng nó sau
    return "Được định nghĩa trong file utils.ts, gọi ở main.ts:32";
}

export function deactivate() {}
"@

# Tạo file tsconfig.json
$tsconfigJson = @"
{
  "compilerOptions": {
    "module": "commonjs",
    "target": "es6",
    "outDir": "out",
    "lib": [
      "es6"
    ],
    "sourceMap": true,
    "rootDir": "src",
    "strict": true
  },
  "exclude": [
    "node_modules",
    ".vscode-test"
  ]
}
"@

# Tạo file launch.json
$launchJson = @"
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Run Extension",
      "type": "extensionHost",
      "request": "launch",
      "args": [
        "--extensionDevelopmentPath=\${workspaceFolder}"
      ],
      "outFiles": [
        "\${workspaceFolder}/out/**/*.js"
      ],
      "preLaunchTask": "\${defaultBuildTask}"
    }
  ]
}
"@

# Tạo file tasks.json
$tasksJson = @"
{
  "version": "2.0.0",
  "tasks": [
    {
      "type": "npm",
      "script": "watch",
      "problemMatcher": "$tsc-watch",
      "isBackground": true,
      "presentation": {
        "reveal": "never"
      },
      "group": {
        "kind": "build",
        "isDefault": true
      }
    }
  ]
}
"@

# Tạo file README.md
$readmeMd = @"
# Code Context Whisperer

Extension VS Code hiển thị ngữ cảnh mã nguồn khi hover chuột.

## Tính năng

- Hiển thị thông tin ngữ cảnh khi hover chuột lên biến, hàm, lớp
- Tìm kiếm định nghĩa và cách sử dụng trong dự án

## Yêu cầu

- Visual Studio Code phiên bản 1.60.0 trở lên

## Cài đặt

1. Tải extension từ VS Code Marketplace
2. Hoặc cài đặt thủ công bằng file .vsix

## Sử dụng

Đơn giản chỉ cần di chuột qua bất kỳ biến, hàm hoặc lớp nào trong code để xem ngữ cảnh của nó.

## Phiên bản cao cấp

Phiên bản cao cấp với tính năng AI và đồng bộ dữ liệu có thể được mở khóa với giá 1 USD.
"@

# Ghi các file
$packageJson | Out-File -FilePath "package.json" -Encoding utf8
$extensionTs | Out-File -FilePath "src/extension.ts" -Encoding utf8
$tsconfigJson | Out-File -FilePath "tsconfig.json" -Encoding utf8
$launchJson | Out-File -FilePath ".vscode/launch.json" -Encoding utf8
$tasksJson | Out-File -FilePath ".vscode/tasks.json" -Encoding utf8
$readmeMd | Out-File -FilePath "README.md" -Encoding utf8

# Cài đặt các dependencies
npm install

Write-Host "Đã tạo dự án extension 'Code Context Whisperer' thành công!"
Write-Host "Bạn có thể mở dự án trong VS Code bằng lệnh: code ."