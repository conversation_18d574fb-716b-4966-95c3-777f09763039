//+------------------------------------------------------------------+
//| GaussianFilterIndicator.mq5                                      |
//+------------------------------------------------------------------+
#property copyright "Tom"
#property link      ""
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 6
#property indicator_plots   3

#include "../Includes/GaussianFilter.mqh"
#include "../Includes/Constants.mqh"

// Input parameters
input bool modeLag_Gaus = false;  // Reduced Lag Mode
input bool modeFast_Gaus = false; // Fast Response Mode

// Indicator buffers
double FilterBuffer[];
double HighBandBuffer[];
double LowBandBuffer[];
double Filter5mBuffer[];
double Filter1hBuffer[];
double Filter4hBuffer[];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                          |
//+------------------------------------------------------------------+
int OnInit() {
    // Set indicator buffers
    SetIndexBuffer(0, FilterBuffer, INDICATOR_DATA);
    SetIndexBuffer(1, HighBandBuffer, INDICATOR_DATA);
    SetIndexBuffer(2, LowBandBuffer, INDICATOR_DATA);
    SetIndexBuffer(3, Filter5mBuffer, INDICATOR_DATA);
    SetIndexBuffer(4, Filter1hBuffer, INDICATOR_DATA);
    SetIndexBuffer(5, Filter4hBuffer, INDICATOR_DATA);
    
    // Set indicator labels
    PlotIndexSetString(0, PLOT_LABEL, "Gaussian Filter");
    PlotIndexSetString(1, PLOT_LABEL, "High Band");
    PlotIndexSetString(2, PLOT_LABEL, "Low Band");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                               |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[]) {
    
    // Calculate from the last uncalculated bar
    int start = prev_calculated == 0 ? 0 : prev_calculated - 1;
    
    // Loop through bars
    for(int i = start; i < rates_total; i++) {
        // Calculate Gaussian Filter for current timeframe
        double filt, hband, lband;
        color fcolor;
        
        CalcGausFilter(TimeframeToString(Period()), filt, hband, lband, fcolor, modeLag_Gaus, modeFast_Gaus);
        
        // Store values in buffers
        FilterBuffer[i] = filt;
        HighBandBuffer[i] = hband;
        LowBandBuffer[i] = lband;
        
        // Get values from other timeframes
        double filt5m, hband5m, lband5m;
        color fcolor5m;
        double filt1h, hband1h, lband1h;
        color fcolor1h;
        double filt4h, hband4h, lband4h;
        color fcolor4h;
        
        CalcGausFilter("5", filt5m, hband5m, lband5m, fcolor5m, modeLag_Gaus, modeFast_Gaus);
        CalcGausFilter("60", filt1h, hband1h, lband1h, fcolor1h, modeLag_Gaus, modeFast_Gaus);
        CalcGausFilter("240", filt4h, hband4h, lband4h, fcolor4h, modeLag_Gaus, modeFast_Gaus);
        
        // Store values in buffers
        Filter5mBuffer[i] = filt5m;
        Filter1hBuffer[i] = filt1h;
        Filter4hBuffer[i] = filt4h;
    }
    
    return(rates_total);
}